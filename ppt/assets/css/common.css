/* McKinsey风格通用样式 */
:root {
    /* <PERSON><PERSON><PERSON><PERSON><PERSON>品牌色彩 */
    --primary-color: #0073e6;
    --secondary-color: #00a651;
    --accent-color: #ff6b35;
    --dark-blue: #003d6b;
    --light-blue: #e6f3ff;
    
    /* 语义色彩 */
    --success-color: #28a745;
    --error-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;

    /* 中性色彩 */
    --text-primary: #1a1a1a;
    --text-secondary: #666666;
    --text-tertiary: #999999;
    --text-light: #999999; /* 保持兼容性 */
    --background-primary: #ffffff;
    --background-secondary: #f8f9fa;
    --background-tertiary: #e9ecef;
    --background-dark: #2c3e50; /* 恢复 */
    --border-light: #e1e5e9;
    --border-color: #dee2e6;
    
    /* 阴影 */
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.15);
    --shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.2);
    /* 恢复原始阴影变量 */
    --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.15);
    --shadow-heavy: 0 8px 32px rgba(0, 0, 0, 0.2);

    /* 字体系统 */
    --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; /* 保持兼容性 */
    --font-size-xs: 0.75rem;  /* 12px */
    --font-size-sm: 0.875rem; /* 14px */
    --font-size-base: 1rem;    /* 16px */
    --font-size-lg: 1.125rem; /* 18px */
    --font-size-xl: 1.25rem;  /* 20px */
    --font-size-2xl: 1.5rem;   /* 24px */
    --font-size-3xl: 1.875rem; /* 30px */
    --font-size-4xl: 2.25rem;  /* 36px */

    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;

    /* 行高 */
    --line-height-tight: 1.2;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.7;
    
    /* 间距系统 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
    
    /* 圆角 */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    --radius-full: 9999px;

    /* 动画参数 */
    --transition-fast: 0.15s ease-out;
    --transition-base: 0.3s ease-out;
    --transition-slow: 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    --bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* 颜色透明度 */
    --primary-alpha-10: rgba(0, 115, 230, 0.1);
    --primary-alpha-20: rgba(0, 115, 230, 0.2);
    --secondary-alpha-10: rgba(0, 166, 81, 0.1);
    --accent-alpha-10: rgba(255, 107, 53, 0.1);
}

/* 重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--background-primary);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: var(--background-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-tertiary);
}

/* 选择文本样式 */
::selection {
    background-color: var(--primary-alpha-20);
    color: var(--primary-color);
}

/* 焦点样式 */
*:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

button:focus,
.btn:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* ===================================
   通用组件样式 - 按钮
   ================================== */

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: 6px;
    font-family: inherit;
    font-size: var(--font-size-sm);
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-base);
    user-select: none;
    white-space: nowrap;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--dark-blue);
    transform: translateY(-1px);
    box-shadow: var(--shadow-medium);
}

.btn-secondary {
    background-color: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.btn-secondary:hover {
    background-color: var(--primary-color);
    color: white;
}

.btn-ghost {
    background-color: transparent;
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.btn-ghost:hover {
    background-color: var(--background-secondary);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

/* ===================================
   通用组件样式 - 卡片
   ================================== */

.card {
    background: var(--background-primary);
    border-radius: 12px;
    box-shadow: var(--shadow-light);
    transition: all var(--transition-base);
    overflow: hidden;
}

.card:hover {
    box-shadow: var(--shadow-medium);
    transform: translateY(-2px);
}

.card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.card-body {
    padding: var(--spacing-lg);
}

.card-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    background-color: var(--background-secondary);
}

/* ===================================
   文本样式工具类
   ================================== */

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-light { color: var(--text-light); }
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-weight-light { font-weight: 300; }
.font-weight-normal { font-weight: 400; }
.font-weight-medium { font-weight: 500; }
.font-weight-semibold { font-weight: 600; }
.font-weight-bold { font-weight: 700; }

/* ===================================
   间距工具类
   ================================== */

.m-0 { margin: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }

.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }

.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-md); }
.p-4 { padding: var(--spacing-lg); }
.p-5 { padding: var(--spacing-xl); }

/* ===================================
   通用动画类
   ================================== */

.fade-in {
    animation: fadeIn 0.6s ease-out forwards;
}

.slide-up {
    animation: slideUp 0.6s ease-out forwards;
}

.scale-in {
    animation: scaleIn 0.4s var(--bounce) forwards;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { 
        opacity: 0; 
        transform: translateY(30px); 
    }
    to { 
        opacity: 1; 
        transform: translateY(0); 
    }
}

@keyframes scaleIn {
    from { 
        opacity: 0; 
        transform: scale(0.9); 
    }
    to { 
        opacity: 1; 
        transform: scale(1); 
    }
}

/* ===================================
   音频播放器样式
   ================================== */

.audio-player-container {
    position: absolute;
    top: 20px;
    right: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    z-index: 1000;
    background-color: rgba(255, 255, 255, 0.9);
    padding: 8px 15px;
    border-radius: 25px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.audio-control-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    cursor: pointer;
    box-shadow: var(--shadow-light);
    transition: all var(--transition-base);
    flex-shrink: 0;
}

.audio-control-btn:hover {
    background-color: var(--dark-blue);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.audio-progress-bar {
    flex-grow: 1;
    height: 8px;
    background-color: var(--border-color);
    border-radius: 4px;
    cursor: pointer;
    overflow: hidden;
    position: relative;
}

.audio-progress {
    height: 100%;
    width: 0%;
    background-color: var(--secondary-color);
    border-radius: 4px;
    transition: width 0.1s linear;
}

.audio-time {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    white-space: nowrap;
}

.transcript-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    display: none; /* Hidden by default */
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
}

.transcript-overlay.show {
    opacity: 1;
}

.transcript-content {
    background-color: var(--background-primary);
    padding: var(--spacing-xl);
    border-radius: var(--spacing-md);
    max-width: 80%;
    max-height: 80%;
    overflow-y: auto;
    position: relative;
    box-shadow: var(--shadow-heavy);
}

.transcript-content h3 {
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.transcript-content p {
    white-space: pre-wrap; /* Preserves whitespace and line breaks */
    font-size: var(--font-size-base);
    line-height: 1.8;
    color: var(--text-secondary);
}

.close-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--text-light);
}

.close-btn:hover {
    color: var(--text-primary);
}

/* ===================================
   通用幻灯片和内容样式
   ================================== */

.slide {
    padding: var(--spacing-2xl);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.slide-title {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
    line-height: 1.2;
}

.slide-subtitle {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-medium);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-2xl);
    max-width: 800px;
}

.content-grid {
    display: grid;
    gap: var(--spacing-xl);
    width: 100%;
    max-width: 1200px;
}

/* ===================================
   通用背景和装饰
   ================================== */

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    min-height: 100vh;
    z-index: -1;
    overflow: hidden;
}

.bg-image {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    opacity: 0.3;
}

.bg-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.1);
    z-index: 1;
}


.bg-pattern {
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: 
        radial-gradient(circle at 20% 20%, rgba(0, 115, 230, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(0, 166, 81, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 40% 60%, rgba(255, 107, 53, 0.02) 0%, transparent 50%);
    animation: float 20s ease-in-out infinite;
}

.bg-gradient {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        135deg,
        rgba(0, 115, 230, 0.02) 0%,
        rgba(255, 255, 255, 0) 50%,
        rgba(0, 166, 81, 0.02) 100%
    );
}

.hero-decorations {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: -1;
}

.decoration-element {
    position: absolute;
    opacity: 0.1;
}

/* ===================================
   通用组件
   ================================== */

.chapter-badge {
    display: inline-block;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-full);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow-md);
}

.chapter-overview {
    margin-top: var(--spacing-3xl);
    text-align: center;
}

.overview-title {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-2xl);
    position: relative;
}

.overview-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--radius-full);
}

.chapter-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-lg);
    width: 1200px;
    margin: 0 auto;
}

.chapter-item {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-light);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.chapter-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    transform: scaleX(0);
    transition: transform var(--transition-base);
}

.chapter-item:hover::before {
    transform: scaleX(1);
}

.chapter-item:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-alpha-20);
}

.chapter-number {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
    text-align: center;
}

.chapter-content h4 {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    text-align: center;
}

.chapter-content p {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    line-height: var(--line-height-relaxed);
    margin: 0;
    text-align: center;
}

.metrics-grid-single-row {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-lg);
    max-width: 1200px;
    margin: var(--spacing-2xl) auto;
}

.metrics-grid-single-row .metric-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-light);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-base);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.metrics-grid-single-row .metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    transform: scaleX(0);
    transition: transform var(--transition-base);
}

.metrics-grid-single-row .metric-card:hover::before {
    transform: scaleX(1);
}

.metrics-grid-single-row .metric-card:hover {
    transform: translateY(-6px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-alpha-20);
}

.metrics-grid-single-row .metric-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-md) auto;
    font-size: var(--font-size-lg);
    transition: all var(--transition-base);
}

.metrics-grid-single-row .metric-card:hover .metric-icon {
    transform: scale(1.1);
    box-shadow: var(--shadow-md);
}

.metrics-grid-single-row .metric-value {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.metrics-grid-single-row .metric-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
    margin-bottom: var(--spacing-xs);
}

.metrics-grid-single-row .metric-change {
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-semibold);
    padding: 2px 8px;
    border-radius: var(--radius-full);
}

.metrics-grid-single-row .metric-change.positive {
    background: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(40, 167, 69, 0.3);
}

/* ===================================
   动画
   ================================== */

@keyframes float {
    0% { transform: translate(0, 0) rotate(0deg); }
    25% { transform: translate(20px, 40px) rotate(45deg); }
    50% { transform: translate(-20px, -30px) rotate(90deg); }
    75% { transform: translate(30px, -20px) rotate(135deg); }
    100% { transform: translate(0, 0) rotate(0deg); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); box-shadow: var(--shadow-md); }
    50% { transform: scale(1.05); box-shadow: var(--shadow-lg); }
}

/* ===================================
   响应式设计
   ================================== */

@media (max-width: 768px) {
    :root {
        --font-size-base: 0.875rem;
        --font-size-lg: 1rem;
        --font-size-xl: 1.125rem;
        --font-size-2xl: 1.25rem;
        --font-size-3xl: 1.5rem;
        --font-size-4xl: 1.875rem;
    }
    
    .btn {
        padding: var(--spacing-xs) var(--spacing-md);
        font-size: var(--font-size-xs);
    }
    
    .card {
        border-radius: 8px;
    }
    
    .card-header,
    .card-body,
    .card-footer {
        padding: var(--spacing-md);
    }

    .slide {
        padding: var(--spacing-xl);
        min-height: 100vh;
    }

    .slide-title {
        font-size: var(--font-size-2xl);
    }

    .slide-subtitle {
        font-size: var(--font-size-lg);
    }

    .content-grid {
        grid-template-columns: 1fr !important;
        gap: var(--spacing-lg);
    }

    .chapter-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);
    }
    
    .chapter-item {
        padding: var(--spacing-md);
    }
    
    .chapter-number {
        font-size: var(--font-size-xl);
    }
    
    .chapter-content h4 {
        font-size: var(--font-size-sm);
    }
    
    .chapter-content p {
        font-size: 12px;
    }
    
    .metrics-grid-single-row {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);
        margin: var(--spacing-lg) auto;
    }
    
    .metrics-grid-single-row .metric-card {
        padding: var(--spacing-md);
    }
    
    .metrics-grid-single-row .metric-icon {
        width: 40px;
        height: 40px;
        font-size: var(--font-size-base);
    }
    
    .metrics-grid-single-row .metric-value {
        font-size: var(--font-size-xl);
    }
    
    .metrics-grid-single-row .metric-label {
        font-size: 12px;
    }
}

@media (max-width: 480px) {
    .chapter-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }
    
    .chapter-overview {
        margin-top: var(--spacing-2xl);
    }
    
    .overview-title {
        font-size: var(--font-size-lg);
        margin-bottom: var(--spacing-lg);
    }
    
    .metrics-grid-single-row {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }
    
    .metrics-grid-single-row .metric-card {
        padding: var(--spacing-sm);
    }
    
    .metrics-grid-single-row .metric-icon {
        width: 35px;
        height: 35px;
        font-size: var(--font-size-sm);
    }
    
    .metrics-grid-single-row .metric-value {
        font-size: var(--font-size-lg);
    }
}