/* 幻灯片通用样式 */
.slide {
    width: 100%;
    min-height: 100vh;
    padding: 3rem 1rem;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow-y: auto;
}

.slide-header {
    margin-bottom: var(--spacing-xl);
    text-align: center;
    position: relative;
}

.slide-title {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
    line-height: 1.2;
}

.slide-subtitle {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    font-weight: 400;
    max-width: 800px;
    margin: 0 auto;
    line-height: 1.4;
}

.slide-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    max-width: 1600px;
    margin: 0 auto;
    width: 100%;
}

/* 页面转场动效 */
.slide-enter {
    transform: translateX(100%);
    opacity: 0;
}

.slide-enter-active {
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateX(0);
    opacity: 1;
}

.slide-exit {
    transform: translateX(-100%);
    opacity: 1;
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 内容加载动画 */
.content-block {
    opacity: 0;
    transform: translateY(30px);
    animation: contentFadeIn 0.8s ease-out forwards;
}

.content-block:nth-child(1) { animation-delay: 0.1s; }
.content-block:nth-child(2) { animation-delay: 0.2s; }
.content-block:nth-child(3) { animation-delay: 0.3s; }
.content-block:nth-child(4) { animation-delay: 0.4s; }
.content-block:nth-child(5) { animation-delay: 0.5s; }

@keyframes contentFadeIn {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 卡片上浮效果 */
.card-float {
    transform: translateY(20px);
    opacity: 0;
    animation: cardFloat 0.6s ease-out forwards;
}

@keyframes cardFloat {
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* 数据展示动画 */
.number-counter {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    color: var(--primary-color);
    display: inline-block;
}

.number-counter.animate {
    animation: numberPulse 0.6s ease-out;
}

@keyframes numberPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* 关键数据高亮 */
.highlight-pulse {
    animation: highlightPulse 2s ease-in-out infinite;
}

@keyframes highlightPulse {
    0%, 100% { 
        background-color: transparent; 
        box-shadow: 0 0 0 0 rgba(0, 115, 230, 0.4);
    }
    50% { 
        background-color: rgba(0, 115, 230, 0.1); 
        box-shadow: 0 0 0 10px rgba(0, 115, 230, 0);
    }
}

/* 按钮交互动画 */
.btn-interactive {
    position: relative;
    overflow: hidden;
    transform: translateZ(0);
}

.btn-interactive::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.btn-interactive:active::before {
    width: 300px;
    height: 300px;
}

/* 图标动画 */
.icon-bounce {
    animation: iconBounce 2s ease-in-out infinite;
}

@keyframes iconBounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.icon-rotate {
    animation: iconRotate 2s linear infinite;
}

@keyframes iconRotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* 流程指示动画 */
.process-line {
    position: relative;
    height: 2px;
    background: var(--border-color);
    margin: var(--spacing-lg) 0;
    overflow: hidden;
}

.process-line::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
    animation: processFlow 2s ease-in-out infinite;
}

@keyframes processFlow {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* 滚动视差效果 */
.parallax-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 120%;
    background: linear-gradient(135deg, var(--light-blue) 0%, transparent 50%);
    z-index: -1;
    transform: translateY(0);
    transition: transform 0.1s ease-out;
}

/* 骨架屏效果 */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeletonLoading 1.5s infinite;
}

@keyframes skeletonLoading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

.skeleton-text {
    height: 1em;
    border-radius: 4px;
    margin-bottom: 0.5em;
}

.skeleton-text.short { width: 60%; }
.skeleton-text.medium { width: 80%; }
.skeleton-text.long { width: 100%; }

/* 特殊布局样式 */
.two-column {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-2xl);
    align-items: start;
}

.three-column {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-xl);
    align-items: start;
}

.center-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    min-height: 60vh;
}

/* 图表容器 */
.chart-container {
    background: var(--background-primary);
    border-radius: 12px;
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-light);
    margin: var(--spacing-lg) 0;
    position: relative;
    overflow: hidden;
}

.chart-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.chart-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.chart-subtitle {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
}

/* 数据表格 */
.data-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--background-primary);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--shadow-light);
}

.data-table th,
.data-table td {
    padding: var(--spacing-md);
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.data-table th {
    background: var(--background-secondary);
    font-weight: 600;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.data-table tr:hover {
    background: var(--light-blue);
}

.data-table .number {
    font-weight: 600;
    color: var(--primary-color);
}

.data-table .positive {
    color: var(--secondary-color);
}

.data-table .negative {
    color: var(--accent-color);
}

/* 第一章特定样式 */
.subtitle-container {
    text-align: center;
    margin: var(--spacing-2xl) 0;
}

.subtitle-text {
    font-size: var(--font-size-xl);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
}

.subtitle-meta {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-md);
    font-size: var(--font-size-sm);
    color: var(--text-light);
}

.separator {
    color: var(--border-color);
}

.key-insights {
    display: flex;
    justify-content: center;
    gap: var(--spacing-2xl);
    margin-top: var(--spacing-3xl);
}

/* 流程式洞察展示 */
.insights-grid {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    gap: var(--spacing-lg);
    margin-top: var(--spacing-md);
    position: relative;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 16px;
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
    width: 1200px;
}


.insight-item {
    flex: 1;
    display: flex;
    align-items: center;
    gap: var(--spacing-xl);
    padding: var(--spacing-lg);
    transition: all var(--transition-base);
    position: relative;
    z-index: 1;
}



.insight-number {
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-3xl);
    font-weight: 800;
    color: white;
    border-radius: 50%;
    flex-shrink: 0;
    box-shadow: var(--shadow-medium);
    position: relative;
}

.insight-item:nth-child(1) .insight-number {
    background: linear-gradient(135deg, var(--primary-color), #0099ff);
}

.insight-item:nth-child(2) .insight-number {
    background: linear-gradient(135deg, var(--secondary-color), #00d4aa);
}

.insight-item:nth-child(3) .insight-number {
    background: linear-gradient(135deg, var(--accent-color), #ff8c42);
}



.insight-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.insight-content p {
    font-size: var(--font-size-sm);
    color: var(--text-primary);
    line-height: 1.6;
    margin: 0;
}

.insight-content strong {
    color: var(--text-primary);
    font-weight: 500;
}

.insight-highlight {
    display: inline-block;
    background: linear-gradient(120deg, var(--light-blue) 0%, transparent 100%);
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 600;
    color: var(--primary-color);
    margin-left: 4px;
}


/* 业务模块卡片 */
.business-modules {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-xl);
    margin: var(--spacing-2xl) 0;
}

.module-card {
    background: var(--background-primary);
    border-radius: 16px;
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.module-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.module-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-heavy);
    border-color: var(--primary-color);
}

.module-icon {
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--light-blue);
    border-radius: 16px;
    margin-bottom: var(--spacing-lg);
}

.module-icon i {
    font-size: var(--font-size-2xl);
    color: var(--light-blue);
}

.module-content h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.module-subtitle {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    font-style: italic;
}



.asset-card {
    background: var(--background-primary);
    border-radius: 12px;
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
    transition: all var(--transition-base);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.asset-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-medium);
    border-color: var(--primary-color);
}

.asset-icon {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--light-blue);
    border-radius: 12px;
    flex-shrink: 0;
}

.asset-icon i {
    font-size: var(--font-size-xl);
    color: var(--light-blue);
}

.asset-content h3 {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.asset-format {
    display: inline-block;
    padding: 2px 8px;
    background: var(--secondary-color);
    color: white;
    border-radius: 4px;
    font-size: var(--font-size-xs);
    font-weight: 500;
    margin-bottom: var(--spacing-sm);
}

.asset-description {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin: 0;
}

.asset-value {
    text-align: center;
    padding: var(--spacing-xl);
    background: var(--light-blue);
    border-radius: 12px;
    margin: var(--spacing-xl) 0;
}

.value-statement {
    font-size: var(--font-size-lg);
    color: var(--text-primary);
    font-weight: 500;
    margin: 0;
}

/* 战略仪表板 */
.strategy-dashboard {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-xl);
    margin: var(--spacing-2xl) 0;
}

.dashboard-item {
    background: var(--background-primary);
    border-radius: 16px;
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.dashboard-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.dashboard-item:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-medium);
}

.dashboard-icon {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--light-blue);
    border-radius: 12px;
    margin-bottom: var(--spacing-lg);
}

.dashboard-icon i {
    font-size: var(--font-size-xl);
    color: var(--light-blue);
}

.dashboard-content h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

/* 渠道分配 */
.channel-allocation {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.channel-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.channel-name {
    min-width: 100px;
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-primary);
}

.channel-bar {
    flex: 1;
    height: 8px;
    background: var(--border-color);
    border-radius: 4px;
    overflow: hidden;
}

.channel-fill {
    height: 100%;
    border-radius: 4px;
    transition: width var(--transition-slow);
}

.channel-percent {
    min-width: 40px;
    text-align: right;
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-primary);
}

/* 策略要点 */
.strategy-points {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.strategy-point {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    border-radius: 8px;
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.strategy-point.urgent {
    background: rgba(255, 107, 53, 0.1);
    color: var(--accent-color);
    border: 1px solid rgba(255, 107, 53, 0.3);
}

.strategy-point.advantage {
    background: rgba(0, 166, 81, 0.1);
    color: var(--secondary-color);
    border: 1px solid rgba(0, 166, 81, 0.3);
}

.strategy-point i {
    font-size: var(--font-size-base);
}

/* 机会展示 */
.opportunity-text {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
}



/* ROI指标 */
.roi-metrics {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.metric {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--border-color);
}

.metric:last-child {
    border-bottom: none;
}

.metric-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.metric-value {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--primary-color);
}

/* 战略目标 */
.strategy-goal {
    text-align: center;
    padding: var(--spacing-xl);
    background: linear-gradient(135deg, var(--light-blue) 0%, rgba(0, 166, 81, 0.1) 100%);
    border-radius: 12px;
    margin: var(--spacing-xl) 0;
    border: 1px solid var(--border-color);
}

.goal-statement {
    font-size: var(--font-size-lg);
    color: var(--text-primary);
    font-weight: 500;
    margin: 0;
    line-height: 1.6;
}

/* 数据来源 */
.data-source {
    text-align: center;
    padding: var(--spacing-lg);
    background: var(--background-secondary);
    border-radius: 8px;
    margin-top: var(--spacing-xl);
    border-left: 4px solid var(--primary-color);
}

.data-source p {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin: 0;
    line-height: 1.5;
}

/* 第二章特定样式 */
.market-overview {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.market-insight {
    text-align: center;
    padding: var(--spacing-xl);
    background: var(--light-blue);
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

.insight-text {
    font-size: var(--font-size-lg);
    color: var(--text-primary);
    font-weight: 500;
    margin: 0;
    line-height: 1.6;
}

.market-chart-container {
    background: var(--background-primary);
    border-radius: 16px;
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
}

.chart-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.chart-header h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.combined-chart {
    margin: var(--spacing-xl) 0;
}

.gmv-bars {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.bar-item {
    display: grid;
    grid-template-columns: 120px 1fr 100px 120px;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--background-secondary);
    border-radius: 8px;
}

.bar-label {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--text-primary);
}

.bar-container {
    height: 24px;
    background: var(--border-color);
    border-radius: 12px;
    overflow: hidden;
    position: relative;
}

.bar-fill {
    height: 100%;
    border-radius: 12px;
    transition: width 1.5s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.bar-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.bar-value {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--primary-color);
    text-align: right;
}

.bar-growth {
    font-size: var(--font-size-sm);
    color: var(--secondary-color);
    font-weight: 600;
    text-align: right;
}

.chart-explanation {
    text-align: center;
    margin-top: var(--spacing-xl);
    padding: var(--spacing-lg);
    background: var(--background-secondary);
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
}

.chart-explanation p {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    margin: 0;
    font-style: italic;
}

/* Marimekko图表样式 */
.price-analysis {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.analysis-insight {
    text-align: center;
    padding: var(--spacing-xl);
    background: var(--light-blue);
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

.marimekko-chart {
    background: var(--background-primary);
    border-radius: 16px;
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
}

.marimekko-container {
    display: flex;
    height: 400px;
    gap: var(--spacing-sm);
    margin: var(--spacing-xl) 0;
}

.channel-column {
    display: flex;
    flex-direction: column;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--shadow-light);
}

.channel-header {
    background: var(--dark-blue);
    color: white;
    padding: var(--spacing-md);
    text-align: center;
    font-weight: 600;
    font-size: var(--font-size-base);
}

.price-segments {
    flex: 1;
    display: flex;
    flex-direction: column-reverse;
}

.segment {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: white;
    font-weight: 600;
    text-align: center;
    padding: var(--spacing-sm);
    position: relative;
    transition: all var(--transition-base);
}

.segment:hover {
    transform: scale(1.02);
    z-index: 10;
}

.segment.economic {
    background: var(--accent-color);
}

.segment.mid-range {
    background: var(--primary-color);
}

.segment.premium {
    background: var(--secondary-color);
}

.segment-label {
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-xs);
}

.segment-value {
    font-size: var(--font-size-lg);
    font-weight: 700;
}

.chart-legend {
    display: flex;
    justify-content: center;
    gap: var(--spacing-xl);
    margin-top: var(--spacing-xl);
    padding: var(--spacing-lg);
    background: var(--background-secondary);
    border-radius: 8px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 4px;
}

.legend-color.economic {
    background: var(--accent-color);
}

.legend-color.mid-range {
    background: var(--primary-color);
}

.legend-color.premium {
    background: var(--secondary-color);
}

/* 冰山对比图样式 */
.iceberg-comparison {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-2xl);
    max-width: 1000px;
    margin: 0 auto;
}

.iceberg-insight {
    text-align: center;
    padding: var(--spacing-xl);
    background: var(--light-blue);
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

.icebergs-container {
    display: flex;
    align-items: center;
    gap: var(--spacing-3xl);
    justify-content: center;
}

.iceberg {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
}

.iceberg.small {
    transform: scale(0.8);
}

.iceberg.large {
    transform: scale(1.2);
}

.iceberg-top {
    background: var(--background-primary);
    border: 3px solid var(--primary-color);
    border-radius: 12px 12px 0 0;
    padding: var(--spacing-lg);
    text-align: center;
    min-width: 200px;
    position: relative;
    z-index: 2;
    box-shadow: var(--shadow-light);
}

.iceberg-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
}

.iceberg-value {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--primary-color);
}

.iceberg-bottom {
    background: linear-gradient(180deg, var(--light-blue) 0%, var(--primary-color) 100%);
    border: 3px solid var(--primary-color);
    border-top: none;
    border-radius: 0 0 12px 12px;
    padding: var(--spacing-xl) var(--spacing-lg);
    text-align: center;
    min-width: 200px;
    position: relative;
    z-index: 1;
    transform: translateY(-3px);
}

.iceberg-hidden {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.iceberg.large .iceberg-top {
    border-color: var(--secondary-color);
    background: linear-gradient(135deg, var(--background-primary) 0%, rgba(0, 166, 81, 0.1) 100%);
}

.iceberg.large .iceberg-value {
    color: var(--secondary-color);
}

.iceberg.large .iceberg-bottom {
    background: linear-gradient(180deg, rgba(0, 166, 81, 0.2) 0%, var(--secondary-color) 100%);
    border-color: var(--secondary-color);
}

.vs-separator {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    background: var(--accent-color);
    color: white;
    border-radius: 50%;
    font-size: var(--font-size-xl);
    font-weight: 700;
    box-shadow: var(--shadow-medium);
}

.warning-message {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    background: rgba(255, 107, 53, 0.1);
    border: 1px solid rgba(255, 107, 53, 0.3);
    border-radius: 8px;
    color: var(--accent-color);
    max-width: 900px;
    text-align: center;
}

.warning-message i {
    font-size: var(--font-size-xl);
    flex-shrink: 0;
}

.warning-message p {
    margin: 0;
    font-weight: 500;
}

/* 第三章特定样式 */
.brand-ecosystem {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.ecosystem-insight {
    text-align: center;
    padding: var(--spacing-xl);
    background: var(--light-blue);
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

.brand-pyramids {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-xl);
    margin: var(--spacing-2xl) 0;
}

.pyramid-container {
    background: var(--background-primary);
    border-radius: 16px;
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
    transition: all var(--transition-base);
}

.pyramid-container:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-medium);
}

.pyramid-header {
    text-align: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 2px solid var(--border-color);
}

.pyramid-header h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--primary-color);
    margin: 0 0 var(--spacing-xs) 0;
}

.pyramid-subtitle {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin: 0;
    font-style: italic;
}

.brand-pyramid {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.pyramid-level {
    display: flex;
    justify-content: center;
    transition: all var(--transition-base);
}

.pyramid-level.top {
    transform: translateY(10px);
}

.pyramid-level.middle {
    transform: translateY(5px);
}

.pyramid-level.bottom {
    transform: translateY(0);
}

.brand-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-md);
    border-radius: 8px;
    text-align: center;
    transition: all var(--transition-base);
    min-height: 80px;
    justify-content: center;
}

.brand-item.premium {
    background: linear-gradient(135deg, var(--secondary-color), #00d4aa);
    color: white;
    width: 100%;
}

.brand-item.mid-tier {
    background: linear-gradient(135deg, var(--primary-color), #0099ff);
    color: white;
    width: 90%;
}

.brand-item.entry {
    background: linear-gradient(135deg, var(--accent-color), #ff8c42);
    color: white;
    width: 80%;
}

.brand-item:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-medium);
}

.brand-name {
    font-size: var(--font-size-sm);
    font-weight: 600;
    display: block;
    margin-bottom: var(--spacing-xs);
}

.brand-name.highlight {
    font-size: var(--font-size-base);
    text-decoration: underline;
    text-underline-offset: 2px;
}

.brand-desc {
    font-size: var(--font-size-xs);
    opacity: 0.9;
    font-style: italic;
}

.ecosystem-conclusion {
    text-align: center;
    padding: var(--spacing-lg);
    background: var(--background-secondary);
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
}

.ecosystem-conclusion p {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    margin: 0;
    font-style: italic;
}

/* 平台分析样式 */
.platform-analysis {
    margin: var(--spacing-xl) 0;
}

.analysis-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-xl);
}

.analysis-card {
    background: var(--background-primary);
    border-radius: 12px;
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
    overflow: hidden;
    transition: all var(--transition-base);
}

.analysis-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-medium);
}

.analysis-card .card-header {
    background: var(--primary-color);
    color: white;
    padding: var(--spacing-lg);
    text-align: center;
}

.analysis-card .card-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin: 0 0 var(--spacing-xs) 0;
}

.card-subtitle {
    font-size: var(--font-size-sm);
    opacity: 0.9;
    margin: 0;
}

.analysis-card .card-content {
    padding: var(--spacing-lg);
}

/* 分布项目 */
.distribution-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.item-label {
    min-width: 100px;
    font-size: var(--font-size-sm);
    color: var(--text-primary);
    font-weight: 500;
}

.item-bar {
    flex: 1;
    height: 8px;
    background: var(--border-color);
    border-radius: 4px;
    overflow: hidden;
}

.item-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 1.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.item-value {
    min-width: 50px;
    text-align: right;
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-primary);
}

/* 品牌排名 */
.brand-ranking {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.ranking-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-sm);
    border-radius: 6px;
    transition: all var(--transition-base);
}

.ranking-item:hover {
    background: var(--light-blue);
}

.ranking-item.highlight {
    background: rgba(0, 115, 230, 0.1);
    border: 1px solid var(--primary-color);
}

.ranking-item .rank {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    font-size: var(--font-size-xs);
    font-weight: 600;
    flex-shrink: 0;
}

.ranking-item .brand {
    flex: 1;
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-primary);
}

.ranking-item .percentage {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--primary-color);
}

.ranking-item .note {
    font-size: var(--font-size-xs);
    color: var(--text-light);
    font-style: italic;
}

/* 评分统计 */
.rating-stats {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.stat-item {
    text-align: center;
    padding: var(--spacing-md);
    background: var(--background-secondary);
    border-radius: 8px;
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
}

.stat-value {
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-sm);
}

.stat-value .number-counter {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--primary-color);
}

.stat-value .unit {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
}

.stat-detail {
    font-size: var(--font-size-xs);
    color: var(--text-light);
}

/* 抱怨分析 */
.complaints-analysis {
    border-top: 1px solid var(--border-color);
    padding-top: var(--spacing-md);
}

.complaints-analysis h4 {
    font-size: var(--font-size-base);
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    text-align: center;
}

.complaint-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
    background: rgba(255, 107, 53, 0.1);
    border-radius: 6px;
    border-left: 3px solid var(--accent-color);
}

.complaint-text {
    font-size: var(--font-size-sm);
    color: var(--text-primary);
}

.complaint-percent {
    font-size: var(--font-size-xs);
    color: var(--accent-color);
    font-weight: 600;
}

/* 平台结论 */
.platform-conclusion {
    text-align: center;
    padding: var(--spacing-xl);
    background: linear-gradient(135deg, var(--light-blue) 0%, rgba(0, 166, 81, 0.1) 100%);
    border-radius: 12px;
    margin: var(--spacing-xl) 0;
    border: 1px solid var(--border-color);
}

.platform-conclusion p {
    font-size: var(--font-size-base);
    color: var(--text-primary);
    margin: 0;
    line-height: 1.6;
}

/* 第四章特定样式 - 功能矩阵 */
.strategy-overview {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.feature-matrix {
    background: var(--background-primary);
    border-radius: 16px;
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
}

.matrix-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.matrix-header h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-sm) 0;
}

.matrix-header p {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin: 0;
}

.matrix-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg);
    margin: var(--spacing-xl) 0;
}

.matrix-quadrant {
    padding: var(--spacing-lg);
    border-radius: 12px;
    border: 2px solid;
    position: relative;
    transition: all var(--transition-base);
}

.matrix-quadrant:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-medium);
}

.matrix-quadrant.advantage {
    background: rgba(0, 166, 81, 0.1);
    border-color: var(--secondary-color);
}

.matrix-quadrant.weakness {
    background: rgba(255, 107, 53, 0.1);
    border-color: var(--accent-color);
}

.matrix-quadrant.basic {
    background: rgba(0, 115, 230, 0.1);
    border-color: var(--primary-color);
}

.matrix-quadrant.opportunity {
    background: rgba(128, 128, 128, 0.1);
    border-color: var(--text-light);
}

.quadrant-label {
    font-size: var(--font-size-sm);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: var(--spacing-md);
    text-align: center;
}

.matrix-quadrant.advantage .quadrant-label {
    color: var(--secondary-color);
}

.matrix-quadrant.weakness .quadrant-label {
    color: var(--accent-color);
}

.matrix-quadrant.basic .quadrant-label {
    color: var(--primary-color);
}

.matrix-quadrant.opportunity .quadrant-label {
    color: var(--text-light);
}

.feature-item {
    padding: var(--spacing-md);
    border-radius: 8px;
    margin-bottom: var(--spacing-sm);
    transition: all var(--transition-base);
}

.feature-item.highlight {
    background: rgba(0, 166, 81, 0.2);
    border: 1px solid var(--secondary-color);
}

.feature-item.urgent {
    background: rgba(255, 107, 53, 0.2);
    border: 1px solid var(--accent-color);
}

.feature-item:hover {
    transform: scale(1.02);
}

.feature-name {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.feature-stats {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.bejoan-rate {
    font-size: var(--font-size-sm);
    color: var(--primary-color);
    font-weight: 600;
}

.competitor-rate {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.feature-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.feature-list .feature-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.5);
    border: 1px solid var(--border-color);
}

.feature-rate {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--primary-color);
}

.matrix-explanation {
    text-align: center;
    margin-top: var(--spacing-xl);
    padding: var(--spacing-lg);
    background: var(--background-secondary);
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
}

.matrix-explanation p {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    margin: 0;
    font-style: italic;
}

/* 品牌定位样式 */
.brand-positioning {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-2xl);
    max-width: 1000px;
    margin: 0 auto;
}

.positioning-insight {
    text-align: center;
    padding: var(--spacing-xl);
    background: var(--light-blue);
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

.positioning-triangle {
    position: relative;
    width: 400px;
    height: 400px;
    margin: var(--spacing-2xl) 0;
}

.point-content h3 {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--primary-color);
    margin: 0 0 var(--spacing-sm) 0;
}

.point-content p {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin: 0;
}

.brand-core h2 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    margin: 0 0 var(--spacing-sm) 0;
}

.brand-core p {
    font-size: var(--font-size-sm);
    opacity: 0.9;
    margin: 0;
}

.value-proposition {
    background: var(--background-primary);
    border-radius: 16px;
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
    width: 100%;
}

.proposition-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.proposition-header h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.proposition-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.proposition-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
    border-radius: 12px;
    transition: all var(--transition-base);
}

.proposition-item.core {
    background: rgba(0, 166, 81, 0.1);
    border: 1px solid var(--secondary-color);
}

.proposition-item.advantage {
    background: rgba(0, 115, 230, 0.1);
    border: 1px solid var(--primary-color);
}

.proposition-item.emotion {
    background: rgba(255, 107, 53, 0.1);
    border: 1px solid var(--accent-color);
}

.proposition-item:hover {
    transform: translateX(8px);
    box-shadow: var(--shadow-medium);
}

.item-icon {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    flex-shrink: 0;
}

.proposition-item.core .item-icon {
    background: var(--secondary-color);
    color: white;
}

.proposition-item.advantage .item-icon {
    background: var(--primary-color);
    color: white;
}

.proposition-item.emotion .item-icon {
    background: var(--accent-color);
    color: white;
}

.item-icon i {
    font-size: var(--font-size-xl);
}

.item-content h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-sm) 0;
}

.item-content p {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    margin: 0;
    line-height: 1.5;
}

.positioning-conclusion {
    text-align: center;
    padding: var(--spacing-lg);
    background: var(--background-secondary);
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
}

.positioning-conclusion p {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    margin: 0;
    font-style: italic;
}

/* 第五章特定样式 - 战略路径 */
.strategic-paths {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.paths-insight {
    text-align: center;
    padding: var(--spacing-xl);
    background: var(--light-blue);
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

.paths-diagram {
    background: var(--background-primary);
    border-radius: 16px;
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
}

.path-flow {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
    margin: var(--spacing-xl) 0;
}

.path-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
    border-radius: 12px;
    border: 2px solid;
    transition: all var(--transition-base);
}

.path-item:hover {
    transform: translateX(8px);
    box-shadow: var(--shadow-medium);
}

.path-item.conservative {
    background: rgba(128, 128, 128, 0.1);
    border-color: var(--text-light);
}

.path-item.balanced {
    background: rgba(0, 115, 230, 0.1);
    border-color: var(--primary-color);
}

.path-item.aggressive {
    background: rgba(255, 107, 53, 0.1);
    border-color: var(--accent-color);
}

.path-icon {
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 16px;
    flex-shrink: 0;
}

.path-item.conservative .path-icon {
    background: var(--text-light);
    color: white;
}

.path-item.balanced .path-icon {
    background: var(--primary-color);
    color: white;
}

.path-item.aggressive .path-icon {
    background: var(--accent-color);
    color: white;
}

.path-icon i {
    font-size: var(--font-size-2xl);
}

.path-content {
    flex: 1;
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.path-content h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    min-width: 150px;
}

.path-flow-arrow {
    font-size: var(--font-size-xl);
    color: var(--text-light);
    font-weight: bold;
}

.path-outcome,
.path-result {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    font-weight: 500;
    min-width: 100px;
    text-align: center;
}

.paths-explanation {
    text-align: center;
    margin-top: var(--spacing-xl);
    padding: var(--spacing-lg);
    background: var(--background-secondary);
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
}

.paths-explanation p {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    margin: 0;
    font-style: italic;
}

/* 推荐徽章 */
.recommended-badge {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    background: linear-gradient(135deg, var(--secondary-color), #00d4aa);
    color: white;
    border-radius: 25px;
    font-size: var(--font-size-lg);
    font-weight: 600;
    box-shadow: var(--shadow-medium);
    margin: 0 auto;
}

.recommended-badge i {
    font-size: var(--font-size-xl);
}

.element-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
}

.element-value {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--primary-color);
}


.result-label {
    font-size: var(--font-size-sm);
    opacity: 0.9;
    margin-bottom: var(--spacing-xs);
}

.result-value {
    font-size: var(--font-size-lg);
    font-weight: 700;
}



.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-lg);
}

.metric-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: var(--spacing-lg);
    background: var(--background-secondary);
    border-radius: 12px;
    transition: all var(--transition-base);
}

.metric-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-medium);
}

.metric-card .metric-icon {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-color);
    color: white;
    border-radius: 10px;
    margin-bottom: var(--spacing-md);
}

.metric-card .metric-icon i {
    font-size: var(--font-size-lg);
    color: var(--light-blue);
}

.metric-card .metric-value {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.metric-card .metric-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}


.matrix-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.matrix-header h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.risk-items {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.risk-item {
    display: flex;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
    border-radius: 12px;
    border: 2px solid;
    transition: all var(--transition-base);
}

.risk-item:hover {
    transform: translateX(8px);
    box-shadow: var(--shadow-medium);
}

.risk-item.high {
    background: rgba(255, 107, 53, 0.1);
    border-color: var(--accent-color);
}

.risk-item.medium {
    background: rgba(255, 193, 7, 0.1);
    border-color: #ffc107;
}

.risk-item.low {
    background: rgba(0, 166, 81, 0.1);
    border-color: var(--secondary-color);
}


.risk-content {
    flex: 1;
}

.risk-content h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-sm) 0;
}







/* 第五章特定样式 - 战略路径 */
.strategic-paths {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.paths-insight {
    text-align: center;
    padding: var(--spacing-xl);
    background: var(--light-blue);
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

.paths-diagram {
    background: var(--background-primary);
    border-radius: 16px;
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
}

.path-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
    border-radius: 12px;
    border: 2px solid;
    transition: all var(--transition-base);
}

.path-item:hover {
    transform: translateX(8px);
    box-shadow: var(--shadow-medium);
}

.path-item.conservative {
    background: rgba(128, 128, 128, 0.1);
    border-color: var(--text-light);
}

.path-item.balanced {
    background: rgba(0, 115, 230, 0.1);
    border-color: var(--primary-color);
}

.path-item.aggressive {
    background: rgba(255, 107, 53, 0.1);
    border-color: var(--accent-color);
}

.path-icon {
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 16px;
    flex-shrink: 0;
}

.path-item.conservative .path-icon {
    background: var(--text-light);
    color: white;
}

.path-item.balanced .path-icon {
    background: var(--primary-color);
    color: white;
}

.path-item.aggressive .path-icon {
    background: var(--accent-color);
    color: white;
}

.path-icon i {
    font-size: var(--font-size-2xl);
}

.path-content {
    flex: 1;
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.path-content h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    min-width: 150px;
}


.path-outcome,
.path-result {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    font-weight: 500;
    min-width: 100px;
    text-align: center;
}

.paths-explanation {
    text-align: center;
    margin-top: var(--spacing-xl);
    padding: var(--spacing-lg);
    background: var(--background-secondary);
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
}

.paths-explanation p {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    margin: 0;
    font-style: italic;
}

/* 战略详情样式 */
.strategy-details {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.strategy-dashboard.conservative {
    border-left: 4px solid var(--text-light);
}

.strategy-dashboard.recommended {
    border-left: 4px solid var(--primary-color);
}

.dashboard-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-md);
    border-bottom: 2px solid var(--border-color);
}

.dashboard-header h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.dashboard-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
}

.metric-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: var(--spacing-lg);
    background: var(--background-secondary);
    border-radius: 12px;
    transition: all var(--transition-base);
}

.metric-item:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-medium);
}

.metric-item .metric-icon {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-color);
    color: white;
    border-radius: 12px;
    margin-bottom: var(--spacing-md);
}

.metric-item .metric-icon i {
    font-size: var(--font-size-xl);
}

.metric-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.metric-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 500;
}

.metric-value {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-color);
}

.metric-detail {
    font-size: var(--font-size-xs);
    color: var(--text-light);
}

/* 战略仪表板 */
.strategy-dashboard {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-xl);
    margin: var(--spacing-2xl) 0;
}

.dashboard-item {
    background: var(--background-primary);
    border-radius: 16px;
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.dashboard-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.dashboard-item:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-medium);
}

.dashboard-icon {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--light-blue);
    border-radius: 12px;
    margin-bottom: var(--spacing-lg);
}


.dashboard-content h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

/* 渠道分配 */
.channel-allocation {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.channel-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.channel-name {
    min-width: 100px;
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-primary);
}

.channel-bar {
    flex: 1;
    height: 8px;
    background: var(--border-color);
    border-radius: 4px;
    overflow: hidden;
}

.channel-fill {
    height: 100%;
    border-radius: 4px;
    transition: width var(--transition-slow);
}

.channel-percent {
    min-width: 40px;
    text-align: right;
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-primary);
}



/* 响应式调整 */
@media (max-width: 768px) {
    .slide {
        padding: var(--spacing-lg);
    }

    .slide-title {
        font-size: var(--font-size-2xl);
    }

    .slide-subtitle {
        font-size: var(--font-size-base);
    }

    .two-column,
    .three-column {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .chart-container {
        padding: var(--spacing-lg);
    }

    .data-table {
        font-size: var(--font-size-sm);
    }

    .data-table th,
    .data-table td {
        padding: var(--spacing-sm);
    }

    .business-modules {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .key-insights {
        flex-direction: column;
        gap: var(--spacing-lg);
    }

    /* 移动端洞察网格适配 */
    .insights-grid::before {
        left: 30px;
        width: 2px;
    }

    .insights-grid::after {
        left: 29px;
        width: 4px;
        height: 4px;
    }

    .insight-item {
        gap: var(--spacing-lg);
        padding: var(--spacing-md);
    }

    .insight-number {
        width: 60px;
        height: 60px;
        font-size: var(--font-size-2xl);
    }

    .insight-content p {
        font-size: var(--font-size-sm);
    }

    .verification-funnel {
        gap: var(--spacing-lg);
    }

    .funnel-level {
        flex-direction: column;
        text-align: center;
    }

    .asset-card {
        flex-direction: column;
        text-align: center;
    }

    .strategy-dashboard {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .channel-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }

    .channel-name {
        min-width: auto;
    }

    .roi-metrics {
        gap: var(--spacing-sm);
    }

    .metric {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
    }

    .bar-item {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
        text-align: center;
    }

    .marimekko-container {
        flex-direction: column;
        height: auto;
        gap: var(--spacing-md);
    }

    .channel-column {
        width: 100% !important;
        height: 120px;
    }

    .price-segments {
        flex-direction: row;
    }

    .segment {
        flex: 1;
        min-height: 80px;
    }

    .chart-legend {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: center;
    }

    .icebergs-container {
        flex-direction: column;
        gap: var(--spacing-xl);
    }

    .iceberg.small,
    .iceberg.large {
        transform: scale(1);
    }

    .vs-separator {
        transform: rotate(90deg);
    }

    .warning-message {
        flex-direction: column;
        text-align: center;
    }

    .brand-pyramids {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .analysis-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .distribution-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }

    .item-label {
        min-width: auto;
    }

    .ranking-item {
        flex-wrap: wrap;
        gap: var(--spacing-sm);
    }

    .rating-stats {
        gap: var(--spacing-md);
    }

    .stat-value {
        flex-direction: column;
        gap: 0;
    }

    .matrix-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .positioning-triangle {
        width: 300px;
        height: 300px;
    }

    .triangle-point {
        width: 120px;
        padding: var(--spacing-md);
    }

    .triangle-center {
        width: 150px;
        padding: var(--spacing-lg);
    }

    .proposition-content {
        gap: var(--spacing-md);
    }

    .proposition-item {
        flex-direction: column;
        text-align: center;
    }

    .path-flow {
        gap: var(--spacing-lg);
    }

    .path-item {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }

    .path-content {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .formula-equation {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .findings-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .plan-timeline::before {
        display: none;
    }

    .timeline-item {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }

    .timeline-marker {
        margin: 0 auto;
    }

    .metrics-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);
    }

    .risk-item {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }

    .closing-title {
        font-size: var(--font-size-3xl);
    }

    .closing-subtitle {
        font-size: var(--font-size-lg);
    }

    .closing-quote blockquote::before,
    .closing-quote blockquote::after {
        display: none;
    }
}
