/* ===================================
   McKinsey风格动效系统
   ================================== */

/* === 基础动画关键帧 === */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideLeft {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideRight {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-2px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(2px);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes cardFloat {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes progressBar {
    from {
        width: 0%;
    }
    to {
        width: var(--progress-width, 100%);
    }
}

@keyframes typewriter {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

@keyframes gradient {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* === 动画类 === */
.animate-fadeIn {
    animation: fadeIn 0.6s var(--ease-out) forwards;
}

.animate-fadeInUp {
    animation: fadeInUp 0.8s var(--ease-out) forwards;
}

.animate-fadeInDown {
    animation: fadeInDown 0.8s var(--ease-out) forwards;
}

.animate-fadeInLeft {
    animation: fadeInLeft 0.8s var(--ease-out) forwards;
}

.animate-fadeInRight {
    animation: fadeInRight 0.8s var(--ease-out) forwards;
}

.animate-scaleIn {
    animation: scaleIn 0.6s var(--ease-bounce) forwards;
}

.animate-slideUp {
    animation: slideUp 0.8s var(--ease-out) forwards;
}

.animate-slideDown {
    animation: slideDown 0.8s var(--ease-out) forwards;
}

.animate-slideLeft {
    animation: slideLeft 0.8s var(--ease-out) forwards;
}

.animate-slideRight {
    animation: slideRight 0.8s var(--ease-out) forwards;
}

.animate-zoomIn {
    animation: zoomIn 0.6s var(--ease-out) forwards;
}

.animate-bounceIn {
    animation: bounceIn 1s var(--ease-out) forwards;
}

.animate-cardFloat {
    animation: cardFloat 0.8s var(--ease-out) forwards;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

.animate-shake {
    animation: shake 0.5s ease-in-out;
}

/* === 延迟动画类 === */
.animate-delay-100 { animation-delay: 100ms; }
.animate-delay-200 { animation-delay: 200ms; }
.animate-delay-300 { animation-delay: 300ms; }
.animate-delay-400 { animation-delay: 400ms; }
.animate-delay-500 { animation-delay: 500ms; }
.animate-delay-600 { animation-delay: 600ms; }
.animate-delay-700 { animation-delay: 700ms; }
.animate-delay-800 { animation-delay: 800ms; }
.animate-delay-900 { animation-delay: 900ms; }
.animate-delay-1000 { animation-delay: 1000ms; }

/* === 动画持续时间类 === */
.animate-duration-fast { animation-duration: 300ms; }
.animate-duration-normal { animation-duration: 500ms; }
.animate-duration-slow { animation-duration: 800ms; }
.animate-duration-slower { animation-duration: 1200ms; }

/* === 悬停动效 === */
.hover-lift {
    transition: transform var(--transition-base), box-shadow var(--transition-base);
}

.hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.hover-scale {
    transition: transform var(--transition-base);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-glow {
    transition: box-shadow var(--transition-base);
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(0, 115, 230, 0.3);
}

.hover-slide-right {
    transition: transform var(--transition-base);
}

.hover-slide-right:hover {
    transform: translateX(4px);
}

/* === 加载动画 === */
.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-dots {
    display: inline-flex;
    gap: 4px;
}

.loading-dots span {
    width: 6px;
    height: 6px;
    background-color: var(--primary-color);
    border-radius: 50%;
    animation: bounce 1.4s ease-in-out infinite both;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes bounce {
    0%, 80%, 100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}

/* === 进度条动画 === */
.progress-bar {
    width: 100%;
    height: 4px;
    background-color: var(--background-tertiary);
    border-radius: var(--radius-full);
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    border-radius: var(--radius-full);
    animation: progressBar 2s var(--ease-out) forwards;
}

/* === 文字动效 === */
.typewriter {
    overflow: hidden;
    border-right: 2px solid var(--primary-color);
    white-space: nowrap;
    animation: typewriter 3s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes blink-caret {
    from, to {
        border-color: transparent;
    }
    50% {
        border-color: var(--primary-color);
    }
}

/* === 渐变动画 === */
.animated-gradient {
    background: linear-gradient(-45deg, var(--primary-color), var(--secondary-color), var(--accent-color), var(--primary-light));
    background-size: 400% 400%;
    animation: gradient 15s ease infinite;
}

/* === 响应式动画控制 === */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* === 滚动触发动画 === */
.scroll-reveal {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.8s var(--ease-out), transform 0.8s var(--ease-out);
}

.scroll-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
}
