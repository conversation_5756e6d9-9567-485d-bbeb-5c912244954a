<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第四章：Bejoan的差异化制胜策略</title>
    <link rel="stylesheet" href="../assets/css/common.css">
    <link rel="stylesheet" href="../assets/css/slide.css">
    <link rel="stylesheet" href="../assets/css/chapter4-enhanced.css">
    <link rel="stylesheet" href="../assets/css/charts.css">

    <!-- 图表库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/d3@7.8.5/dist/d3.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 幻灯片1：第4章首页 -->
    <div id="slide-1" class="slide">
        <div class="slide-content center-content">
            <!-- 背景图片 -->
            <div class="hero-background">
                <div class="bg-image"></div>
                <div class="bg-overlay"></div>
                <div class="bg-pattern animate-fadeIn animate-delay-100"></div>
                <div class="bg-gradient animate-fadeIn animate-delay-200"></div>
            </div>

            <!-- 主标题区域 -->
            <div class="hero-section animate-slideUp animate-delay-300" style="margin-bottom: 40px;">
                <div class="chapter-badge animate-scaleIn animate-delay-400">第四章</div>
                <h1 class="hero-title animate-slideUp animate-delay-500">
                    Bejoan差异化策略
                </h1>
                <p class="hero-subtitle animate-slideUp animate-delay-600">
                    基于竞争分析制定产品定位、功能创新与营销差异化方案
                </p>
                <div class="hero-description animate-slideUp animate-delay-700">
                    <p>通过深度竞争分析，为Bejoan制定"以按摩为矛，补齐除臭为盾"的差异化策略，在三大渠道实现精准定位和有效突围。</p>
                </div>
            </div>

            

            <!-- 核心洞察 -->
            <div class="content-block core-insights animate-slideUp animate-delay-1250">
                <h3 class="insights-title">核心洞察</h3>
                <div class="insights-grid">
                    <div class="insight-item animate-scaleIn animate-delay-1300">
                        <div class="insight-number">01</div>
                        <div class="insight-content">
                            <p>以"按摩"为矛(100%配备率 vs 竞品24%)，补齐"除臭"为盾的产品策略，针对智能马桶和马桶盖<span class="insight-highlight">两条产品线</span></p>
                        </div>
                    </div>
                    <div class="insight-item animate-scaleIn animate-delay-1350">
                        <div class="insight-number">02</div>
                        <div class="insight-content">
                            <p>不做"低价挑战者"，要做"高价值智能体验普及者"的<span class="insight-highlight">品牌定位</span></p>
                        </div>
                    </div>
                    <div class="insight-item animate-scaleIn animate-delay-1400">
                        <div class="insight-number">03</div>
                        <div class="insight-content">
                            <p><span class="insight-highlight">三大差异化策略方向</span>助力市场突破：功能、体验、品牌三位一体</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 章节概要 -->
            <div class="content-block chapter-overview animate-slideUp animate-delay-1300">
                <h3 class="overview-title">本章节内容概要</h3>
                <div class="chapter-grid">
                    <div class="chapter-item">
                        <div class="chapter-number">4.1</div>
                        <div class="chapter-content">
                            <h4>制胜产品组合</h4>
                            <p>以"按摩"为矛，补齐"除臭"为盾的产品策略</p>
                        </div>
                    </div>
                    <div class="chapter-item">
                        <div class="chapter-number">4.2</div>
                        <div class="chapter-content">
                            <h4>渠道定价策略</h4>
                            <p>基于竞争分析的差异化定价策略制定</p>
                        </div>
                    </div>
                    <div class="chapter-item">
                        <div class="chapter-number">4.3</div>
                        <div class="chapter-content">
                            <h4>营销差异化方案</h4>
                            <p>针对三大渠道的差异化营销策略组合</p>
                        </div>
                    </div>
                    <div class="chapter-item">
                        <div class="chapter-number">4.4</div>
                        <div class="chapter-content">
                            <h4>品牌定位策略</h4>
                            <p>基于用户洞察的品牌价值主张设计</p>
                        </div>
                    </div>
                    <div class="chapter-item">
                        <div class="chapter-number">4.5</div>
                        <div class="chapter-content">
                            <h4>竞争壁垒构建</h4>
                            <p>可持续竞争优势的构建路径与方法</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 装饰元素 -->
            <div class="hero-decorations">
                <div class="decoration-element circle animate-float animate-delay-1500"></div>
                <div class="decoration-element triangle animate-pulse animate-delay-1700"></div>
                <div class="decoration-element square animate-float animate-delay-1900"></div>
            </div>
        </div>
    </div>

    <!-- 幻灯片2：制胜产品组合 -->
    <div class="slide" id="slide-2" style="display: none;">
        <div class="slide-header">
            <h1 class="slide-title">Bejoan的制胜产品组合：以"按摩"为矛，补齐"除臭"为盾</h1>
            <p class="slide-subtitle">我们的产品策略必须基于数据洞察，做到"人无我有，人有我优"。</p>
        </div>
        <div class="slide-content">
            <div class="content-block" data-animate="slideUp">
                <div class="product-strategy-block">
                    <div class="strategy-insight">
                        <p class="insight-text">Bejoan当前最大的功能优势是"按摩"（100%配备率 vs 竞品24%），最大的短板是"除臭"（0% vs 竞品81%）。我们的产品策略应针对两条产品线制定差异化重点：</p>
                    </div>
                    
                    <div class="product-line-strategy">
                        <div class="product-line-item toilet-seat">
                            <div class="product-line-header">
                                <div class="product-line-icon">
                                    <i class="fas fa-toilet"></i>
                                </div>
                                <h4 class="product-line-title">智能马桶盖产品线</h4>
                            </div>
                            <div class="product-line-content">
                                <strong>策略重点：</strong>强化按摩清洗优势，快速补齐除臭功能短板，巩固在该品类的领先地位
                            </div>
                        </div>
                        
                        <div class="product-line-item toilet">
                            <div class="product-line-header">
                                <div class="product-line-icon">
                                    <i class="fas fa-bath"></i>
                                </div>
                                <h4 class="product-line-title">智能马桶产品线</h4>
                            </div>
                            <div class="product-line-content">
                                <strong>策略重点：</strong>以按摩功能为核心差异化卖点，同步开发除臭、抗菌等健康功能，建立全功能产品优势
                            </div>
                        </div>
                    </div>
                </div>
                    <div class="feature-matrix" data-animate="cardFloat" data-delay="200">
                        <div class="matrix-header">
                            <h3>功能价值矩阵</h3>
                            <p>基于用户关注度 × Bejoan配备率分析</p>
                        </div>
                        <div class="matrix-container">
                            <div class="matrix-quadrant advantage" data-animate="scaleIn" data-delay="300">
                                <div class="quadrant-label">优势区</div>
                                <div class="feature-item highlight">
                                    <div class="feature-name">按摩清洗</div>
                                    <div class="feature-stats">
                                        <span class="bejoan-rate">Bejoan: 100%</span>
                                        <span class="competitor-rate">竞品: 24%</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="matrix-quadrant weakness" data-animate="scaleIn" data-delay="400">
                                <div class="quadrant-label">短板区</div>
                                <div class="feature-item urgent">
                                    <div class="feature-name">除臭功能</div>
                                    <div class="feature-stats">
                                        <span class="bejoan-rate">Bejoan: 0%</span>
                                        <span class="competitor-rate">竞品: 81%</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="matrix-quadrant basic" data-animate="scaleIn" data-delay="500">
                                <div class="quadrant-label">基础功能区</div>
                                <div class="feature-list">
                                    <div class="feature-item">
                                        <span class="feature-name">座圈加热</span>
                                        <span class="feature-rate">87.5%</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">温水清洗</span>
                                        <span class="feature-rate">87.5%</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">暖风烘干</span>
                                        <span class="feature-rate">87.5%</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="matrix-quadrant opportunity" data-animate="scaleIn" data-delay="600">
                                <div class="quadrant-label">机会区</div>
                                <div class="feature-list">
                                    <div class="feature-item">
                                        <span class="feature-name">抗菌座圈</span>
                                        <span class="feature-rate">12.5%</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">数字显示</span>
                                        <span class="feature-rate">12.5%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="matrix-explanation">
                            <p>该矩阵清晰地指明了我们两条产品线的研发和营销优先级：智能马桶盖侧重巩固按摩优势并快速补齐除臭功能；智能马桶侧重建立全功能差异化优势，以按摩为核心卖点进入市场。</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="content-block" data-animate="fadeIn" data-delay="800">
                <div class="data-source">
                    <p><strong>数据来源：</strong>基于三大平台318款竞品功能特性统计分析（不含Bejoan产品数据）；Bejoan产品功能配备率基于其亚马逊产品线分析。</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 幻灯片3：品牌定位 -->
    <div class="slide" id="slide-3" style="display: none;">
        <div class="slide-header">
            <h1 class="slide-title">我们的品牌定位：不做"低价挑战者"，要做"高价值智能体验普及者"</h1>
            <p class="slide-subtitle">品牌定位决定了我们能走多远。我们不能陷入单纯的价格战。</p>
        </div>
        <div class="slide-content center-content">
            <div class="content-block" data-animate="slideUp">
                <div class="brand-positioning">
                    <div class="positioning-insight">
                        <p class="insight-text">我们的价格虽有竞争力，但我们的核心价值在于提供了同价位竞品所不具备的"高级按摩体验"。通过智能马桶盖和智能马桶两条产品线，我们的品牌故事应该围绕"用合理的价格，享受越级的舒适与健康"来展开，覆盖不同消费层次的需求。</p>
                    </div>
                    
                    <div class="positioning-triangle" data-animate="scaleIn" data-delay="300">
                        <div class="triangle-container">
                            <div class="triangle-point top" data-animate="fadeIn" data-delay="400">
                                <div class="point-content">
                                    <h3>目标客群</h3>
                                    <p>科技型男 & 品质主妇</p>
                                </div>
                            </div>
                            
                            <div class="triangle-center" data-animate="scaleIn" data-delay="600">
                                <div class="brand-core">
                                    <h2>Bejoan</h2>
                                    <p>高价值智能体验普及者</p>
                                </div>
                            </div>
                            
                            <div class="triangle-point bottom-left" data-animate="fadeIn" data-delay="500">
                                <div class="point-content">
                                    <h3>功能支撑</h3>
                                    <p>专业按摩 + 全面功能</p>
                                </div>
                            </div>
                            
                            <div class="triangle-point bottom-right" data-animate="fadeIn" data-delay="700">
                                <div class="point-content">
                                    <h3>情感共鸣</h3>
                                    <p>健康、舒适、关爱家人</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="value-proposition" data-animate="cardFloat" data-delay="800">
                        <div class="proposition-header">
                            <h3>品牌价值主张</h3>
                        </div>
                        <div class="proposition-content">
                            <div class="proposition-item core" style="align-items: center;"> 
                                <div class="item-icon">
                                    <i class="fas fa-heart"></i>
                                </div>
                                <div class="item-content">
                                    <h4 style="text-align: left;">核心承诺</h4>
                                    <p>"让每个家庭都能享受专业级按摩清洗体验"<br>
                                        <div style="font-size: 0.9em; color: #666;">智能马桶盖：升级现有卫浴，即装即享按摩体验</div>
                                        <div style="font-size: 0.9em; color: #666;">智能马桶：一体化解决方案，全功能健康体验</div>
                                    
                                    </p>
                                </div>
                            </div>
                            
                            <div class="proposition-item advantage" style="align-items: center;">
                                <div class="item-icon">
                                    <i class="fas fa-star"></i>
                                </div>
                                <div class="item-content">
                                    <h4 style="text-align: left;">差异化优势</h4>
                                    <p>独有的按摩清洗技术 + 亲民的价格定位 + 补齐除臭功能后的健康体验</p>
                                </div>
                            </div>
                            
                            <div class="proposition-item emotion" style="align-items: center;">
                                <div class="item-icon">
                                    <i class="fas fa-home"></i>
                                </div>
                                <div class="item-content">
                                    <h4 style="text-align: left;">情感连接</h4>
                                    <p>关爱家人健康，提升生活品质，适配不同家庭需求</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="positioning-conclusion" data-animate="fadeIn" data-delay="1000">
                        <p>这个定位帮助我们避开与HOROW的纯粹价格战，并与TOTO的高不可攀形成区隔。它强调了Bejoan在智能马桶盖领域的既有优势，并为智能马桶产品线提供了清晰的价值主张。</p>
                    </div>
                </div>
            </div>
            <div class="content-block" data-animate="fadeIn" data-delay="1200">
                <div class="data-source">
                    <p><strong>数据来源：</strong>基于用户评论情感分析及竞品品牌定位研究；目标客群基于现有用户画像分析；Bejoan产品数据来自亚马逊美国平台。</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 幻灯片4：基于竞争分析的差异化策略建议 -->
    <div class="slide" id="slide-4" style="display: none;">
        <div class="slide-header">
            <h1 class="slide-title">基于竞争分析的差异化策略建议：功能+体验+品牌三位一体</h1>
            <p class="slide-subtitle">三大策略方向助力Bejoan实现市场突破，采取"补短板+强优势"的差异化策略</p>
        </div>
        <div class="slide-content">
            <div class="content-block">
                <div class="strategy-insight" data-animate="fadeInUp" data-delay="200" style="background: #f8fafc; border-radius: 15px; padding: 25px; border: 2px solid #e2e8f0; margin-bottom: 30px;">
                    <p class="insight-text" style="margin: 0; font-size: 16px; color: #1e293b; line-height: 1.6; text-align: center;">通过深度竞争分析，我们应采取"补短板+强优势"的差异化策略，通过功能完善、服务优化和品牌建设三大举措，在激烈的竞争中实现突围。<strong style="margin-top: 6px;">该策略将同时推动智能马桶和马桶盖两条产品线在新渠道的增长。</strong></p>
                </div>

                <!-- 差异化策略框架 -->
                <div class="differentiation-framework" data-animate="slideUp" data-delay="400">
                    <h3 class="framework-title" style="text-align: center; margin-bottom: 30px; font-size: 22px; color: #1e293b; font-weight: 600;">Bejoan差异化竞争策略框架</h3>
                    <div class="framework-container" style="display: flex; gap: 30px; margin-top: 30px;">
                        <!-- 功能差异化 -->
                        <div class="strategy-pillar product-strategy" style="flex: 1; background: #f8fafc; border-radius: 15px; padding: 25px; border: 2px solid #e2e8f0;">
                            <div class="pillar-header" style="text-align: center; margin-bottom: 20px;">
                                <div class="pillar-icon" style="background: #3b82f6; color: white; width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 15px;">
                                    <i class="fas fa-cogs" style="font-size: 24px;"></i>
                                </div>
                                <h4 style="margin: 0; color: #1e40af; font-size: 18px; font-weight: 600;">功能差异化</h4>
                                <span class="pillar-subtitle" style="color: #64748b; font-size: 14px;">产品策略</span>
                            </div>
                            <div class="pillar-content">
                                <div class="product-line-section" style="margin-bottom: 20px;">
                                    <h5 style="color: #2563eb; margin-bottom: 10px; font-size: 14px; font-weight: 600;">智能马桶产品线：</h5>
                                    <div class="priority-item priority-1" style="background: #fff; padding: 12px; border-radius: 8px; margin-bottom: 8px; border-left: 4px solid #ef4444;">
                                        <div style="display: flex; align-items: center; gap: 8px;">
                                            <span class="priority-badge" style="background: #ef4444; color: white; padding: 2px 8px; border-radius: 4px; font-size: 12px;">优先级1</span>
                                            <span class="timeline" style="background: #f1f5f9; padding: 2px 8px; border-radius: 4px; font-size: 12px; color: #64748b;">0-4个月</span>
                                        </div>
                                        <h5 style="margin: 8px 0 4px; font-size: 14px; color: #1e293b;">补齐除臭功能</h5>
                                        <p style="margin: 0; font-size: 12px; color: #64748b;">预计提升竞争力30%</p>
                                    </div>
                                    <div class="priority-item priority-2" style="background: #fff; padding: 12px; border-radius: 8px; margin-bottom: 8px; border-left: 4px solid #f59e0b;">
                                        <div style="display: flex; align-items: center; gap: 8px;">
                                            <span class="priority-badge" style="background: #f59e0b; color: white; padding: 2px 8px; border-radius: 4px; font-size: 12px;">优先级2</span>
                                            <span class="timeline" style="background: #f1f5f9; padding: 2px 8px; border-radius: 4px; font-size: 12px; color: #64748b;">5-8个月</span>
                                        </div>
                                        <h5 style="margin: 8px 0 4px; font-size: 14px; color: #1e293b;">强化智能控制</h5>
                                        <p style="margin: 0; font-size: 12px; color: #64748b;">APP控制、语音集成</p>
                                    </div>
                                    <div class="priority-item priority-3" style="background: #fff; padding: 12px; border-radius: 8px; margin-bottom: 8px; border-left: 4px solid #10b981;">
                                        <div style="display: flex; align-items: center; gap: 8px;">
                                            <span class="priority-badge" style="background: #10b981; color: white; padding: 2px 8px; border-radius: 4px; font-size: 12px;">优先级3</span>
                                            <span class="timeline" style="background: #f1f5f9; padding: 2px 8px; border-radius: 4px; font-size: 12px; color: #64748b;">9-12个月</span>
                                        </div>
                                        <h5 style="margin: 8px 0 4px; font-size: 14px; color: #1e293b;">升级按摩技术</h5>
                                        <p style="margin: 0; font-size: 12px; color: #64748b;">多模式、个性化设置</p>
                                    </div>
                                </div>
                                <div class="product-line-section">
                                    <h5 style="color: #2563eb; margin-bottom: 10px; font-size: 14px; font-weight: 600;">智能马桶盖产品线：</h5>
                                    <div class="priority-item priority-1" style="background: #fff; padding: 12px; border-radius: 8px; margin-bottom: 8px; border-left: 4px solid #ef4444;">
                                        <div style="display: flex; align-items: center; gap: 8px;">
                                            <span class="priority-badge" style="background: #ef4444; color: white; padding: 2px 8px; border-radius: 4px; font-size: 12px;">优先级1</span>
                                            <span class="timeline" style="background: #f1f5f9; padding: 2px 8px; border-radius: 4px; font-size: 12px; color: #64748b;">0-4个月</span>
                                        </div>
                                        <h5 style="margin: 8px 0 4px; font-size: 14px; color: #1e293b;">快速补齐除臭功能</h5>
                                        <p style="margin: 0; font-size: 12px; color: #64748b;">巩固市场领先地位</p>
                                    </div>
                                    <div class="priority-item priority-2" style="background: #fff; padding: 12px; border-radius: 8px; margin-bottom: 8px; border-left: 4px solid #f59e0b;">
                                        <div style="display: flex; align-items: center; gap: 8px;">
                                            <span class="priority-badge" style="background: #f59e0b; color: white; padding: 2px 8px; border-radius: 4px; font-size: 12px;">优先级2</span>
                                            <span class="timeline" style="background: #f1f5f9; padding: 2px 8px; border-radius: 4px; font-size: 12px; color: #64748b;">5-8个月</span>
                                        </div>
                                        <h5 style="margin: 8px 0 4px; font-size: 14px; color: #1e293b;">持续强化按摩优势</h5>
                                        <p style="margin: 0; font-size: 12px; color: #64748b;">扩大差异化</p>
                                    </div>
                                    <div class="priority-item priority-3" style="background: #fff; padding: 12px; border-radius: 8px; margin-bottom: 8px; border-left: 4px solid #10b981;">
                                        <div style="display: flex; align-items: center; gap: 8px;">
                                            <span class="priority-badge" style="background: #10b981; color: white; padding: 2px 8px; border-radius: 4px; font-size: 12px;">优先级3</span>
                                            <span class="timeline" style="background: #f1f5f9; padding: 2px 8px; border-radius: 4px; font-size: 12px; color: #64748b;">9-12个月</span>
                                        </div>
                                        <h5 style="margin: 8px 0 4px; font-size: 14px; color: #1e293b;">增加智能化功能</h5>
                                        <p style="margin: 0; font-size: 12px; color: #64748b;">提升产品附加值</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 体验差异化 -->
                        <div class="strategy-pillar experience-strategy" style="flex: 1; background: #f0fdf4; border-radius: 15px; padding: 25px; border: 2px solid #bbf7d0;">
                            <div class="pillar-header" style="text-align: center; margin-bottom: 20px;">
                                <div class="pillar-icon" style="background: #10b981; color: white; width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 15px;">
                                    <i class="fas fa-heart" style="font-size: 24px;"></i>
                                </div>
                                <h4 style="margin: 0; color: #059669; font-size: 18px; font-weight: 600;">体验差异化</h4>
                                <span class="pillar-subtitle" style="color: #64748b; font-size: 14px;">服务策略</span>
                            </div>
                            <div class="pillar-content">
                                <div class="service-item" style="background: #fff; padding: 15px; border-radius: 8px; margin-bottom: 12px; display: flex; align-items: center; gap: 12px;flex-direction: column;">
                                    <div class="service-icon" style="background: #dcfce7; color: #059669; width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; flex-shrink: 0;">
                                        <i class="fas fa-tools" style="font-size: 16px;"></i>
                                    </div>
                                    <div class="service-content">
                                        <h5 style="margin: 0 0 6px; font-size: 14px; color: #1e293b; font-weight: 600;">安装服务优化</h5>
                                        <p style="margin: 0; font-size: 12px; color: #64748b; line-height: 1.4;">马桶盖10分钟快速安装，智能马桶专业安装服务</p>
                                    </div>
                                </div>
                                <div class="service-item" style="background: #fff; padding: 15px; border-radius: 8px; margin-bottom: 12px; display: flex; align-items: center; gap: 12px;flex-direction: column;">
                                    <div class="service-icon" style="background: #dcfce7; color: #059669; width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; flex-shrink: 0;">
                                        <i class="fas fa-headset" style="font-size: 16px;"></i>
                                    </div>
                                    <div class="service-content">
                                        <h5 style="margin: 0 0 6px; font-size: 14px; color: #1e293b; font-weight: 600;">售后服务强化</h5>
                                        <p style="margin: 0; font-size: 12px; color: #64748b; line-height: 1.4;">24小时响应，马桶盖2年质保，智能马桶3年质保</p>
                                    </div>
                                </div>
                                <div class="service-item" style="background: #fff; padding: 15px; border-radius: 8px; margin-bottom: 12px; display: flex; align-items: center; gap: 12px;flex-direction: column;">
                                    <div class="service-icon" style="background: #dcfce7; color: #059669; width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; flex-shrink: 0;">
                                        <i class="fas fa-graduation-cap" style="font-size: 16px;"></i>
                                    </div>
                                    <div class="service-content">
                                        <h5 style="margin: 0 0 6px; font-size: 14px; color: #1e293b; font-weight: 600;">用户教育体系</h5>
                                        <p style="margin: 0; font-size: 12px; color: #64748b; line-height: 1.4;">产品线差异化教程、在线客服支持</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 品牌差异化 -->
                        <div class="strategy-pillar brand-strategy" style="flex: 1; background: #fef3c7; border-radius: 15px; padding: 25px; border: 2px solid #fde68a;">
                            <div class="pillar-header" style="text-align: center; margin-bottom: 20px;">
                                <div class="pillar-icon" style="background: #f59e0b; color: white; width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 15px;">
                                    <i class="fas fa-star" style="font-size: 24px;"></i>
                                </div>
                                <h4 style="margin: 0; color: #d97706; font-size: 18px; font-weight: 600;">品牌差异化</h4>
                                <span class="pillar-subtitle" style="color: #64748b; font-size: 14px;">品牌策略</span>
                            </div>
                            <div class="pillar-content">
                                <div class="brand-item" style="background: #fff; padding: 15px; border-radius: 8px; margin-bottom: 12px; display: flex; align-items: center; gap: 12px;">
                                    <div class="brand-icon" style="background: #fef3c7; color: #d97706; width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; flex-shrink: 0;">
                                        <i class="fas fa-bullseye" style="font-size: 16px;"></i>
                                    </div>
                                    <div class="brand-content">
                                        <h5 style="margin: 0 0 6px; font-size: 14px; color: #1e293b; font-weight: 600;">价值主张确立</h5>
                                        <p style="margin: 0; font-size: 12px; color: #64748b; line-height: 1.4;">"按摩专家"品牌形象，覆盖两条产品线</p>
                                    </div>
                                </div>
                                <div class="brand-item" style="background: #fff; padding: 15px; border-radius: 8px; margin-bottom: 12px; display: flex; align-items: center; gap: 12px;">
                                    <div class="brand-icon" style="background: #fef3c7; color: #d97706; width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; flex-shrink: 0;">
                                        <i class="fas fa-store-alt" style="font-size: 16px;"></i>
                                    </div>
                                    <div class="brand-content">
                                        <h5 style="margin: 0 0 6px; font-size: 14px; color: #1e293b; font-weight: 600;">渠道品牌建设</h5>
                                        <p style="margin: 0; font-size: 12px; color: #64748b; line-height: 1.4;">差异化展示策略，针对不同产品线的专业培训</p>
                                    </div>
                                </div>
                                <div class="brand-item" style="background: #fff; padding: 15px; border-radius: 8px; margin-bottom: 12px; display: flex; align-items: center; gap: 12px;">
                                    <div class="brand-icon" style="background: #fef3c7; color: #d97706; width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; flex-shrink: 0;">
                                        <i class="fas fa-comments" style="font-size: 16px;"></i>
                                    </div>
                                    <div class="brand-content">
                                        <h5 style="margin: 0 0 6px; font-size: 14px; color: #1e293b; font-weight: 600;">口碑营销体系</h5>
                                        <p style="margin: 0; font-size: 12px; color: #64748b; line-height: 1.4;">用户推荐计划、KOL合作，突出产品线优势</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 实施时间轴和预期效果 -->
                <div class="implementation-roadmap" data-animate="cardFloat" data-delay="800" style="margin-top: 40px;">
                    <div class="roadmap-header" style="text-align: center; margin-bottom: 30px;">
                        <h3 style="margin: 0; font-size: 24px; color: #1e293b; font-weight: 600;">策略实施时间轴与预期效果</h3>
                    </div>
                    <div class="roadmap-content">
                        <div class="timeline-container" style="display: flex; gap: 20px; margin-bottom: 40px;">
                            <div class="timeline-phase short-term" style="flex: 1; background: #fef2f2; border-radius: 15px; padding: 25px; border: 2px solid #fecaca;">
                                <div class="phase-header" style="text-align: center; margin-bottom: 20px;flex-direction: column;">
                                    <div class="phase-icon" style="background: #ef4444; color: white; width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 15px;">
                                        <i class="fas fa-rocket" style="font-size: 20px;"></i>
                                    </div>
                                    <h4 style="margin: 0; color: #dc2626; font-size: 16px; font-weight: 600;">短期 (0-4个月)</h4>
                                </div>
                                <div class="phase-content">
                                    <ul style="list-style: none; padding: 0; margin: 0;">
                                        <li style="background: #fff; padding: 10px; border-radius: 6px; margin-bottom: 8px; font-size: 12px; color: #374151; line-height: 1.4;">智能马桶盖：快速补齐除臭功能，强化渠道合作</li>
                                        <li style="background: #fff; padding: 10px; border-radius: 6px; margin-bottom: 8px; font-size: 12px; color: #374151; line-height: 1.4;">智能马桶：除臭功能开发，品牌定位确立</li>
                                        <li style="background: #fff; padding: 10px; border-radius: 6px; margin-bottom: 8px; font-size: 12px; color: #374151; line-height: 1.4;">共同行动：渠道合作启动，服务体系建设</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="timeline-phase medium-term" style="flex: 1; background: #f0f9ff; border-radius: 15px; padding: 25px; border: 2px solid #bfdbfe;">
                                <div class="phase-header" style="text-align: center; margin-bottom: 20px;flex-direction: column;">
                                    <div class="phase-icon" style="background: #3b82f6; color: white; width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 15px;">
                                        <i class="fas fa-chart-line" style="font-size: 20px;"></i>
                                    </div>
                                    <h4 style="margin: 0; color: #2563eb; font-size: 16px; font-weight: 600;">中期 (5-8个月)</h4>
                                </div>
                                <div class="phase-content">
                                    <ul style="list-style: none; padding: 0; margin: 0;">
                                        <li style="background: #fff; padding: 10px; border-radius: 6px; margin-bottom: 8px; font-size: 12px; color: #374151; line-height: 1.4;">智能马桶盖：市场推广加强，用户教育深化</li>
                                        <li style="background: #fff; padding: 10px; border-radius: 6px; margin-bottom: 8px; font-size: 12px; color: #374151; line-height: 1.4;">智能马桶：智能控制功能上线，渠道逐步铺开</li>
                                        <li style="background: #fff; padding: 10px; border-radius: 6px; margin-bottom: 8px; font-size: 12px; color: #374151; line-height: 1.4;">共同行动：品牌建设加强，口碑营销启动</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="timeline-phase long-term" style="flex: 1; background: #f0fdf4; border-radius: 15px; padding: 25px; border: 2px solid #bbf7d0;">
                                <div class="phase-header" style="text-align: center; margin-bottom: 20px;flex-direction: column;">
                                    <div class="phase-icon" style="background: #10b981; color: white; width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 15px;">
                                        <i class="fas fa-trophy" style="font-size: 20px;"></i>
                                    </div>
                                    <h4 style="margin: 0; color: #059669; font-size: 16px; font-weight: 600;">长期 (9-12个月)</h4>
                                </div>
                                <div class="phase-content">
                                    <ul style="list-style: none; padding: 0; margin: 0;">
                                        <li style="background: #fff; padding: 10px; border-radius: 6px; margin-bottom: 8px; font-size: 12px; color: #374151; line-height: 1.4;">智能马桶盖：技术升级，市场份额巩固</li>
                                        <li style="background: #fff; padding: 10px; border-radius: 6px; margin-bottom: 8px; font-size: 12px; color: #374151; line-height: 1.4;">智能马桶：按摩技术升级，市场份额快速提升</li>
                                        <li style="background: #fff; padding: 10px; border-radius: 6px; margin-bottom: 8px; font-size: 12px; color: #374151; line-height: 1.4;">共同行动：双产品线协同增长，品牌影响力建立</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="expected-results" style="background: #f8fafc; border-radius: 15px; padding: 30px; border: 2px solid #e2e8f0;">
                            <h4 style="text-align: center; margin-bottom: 25px; font-size: 20px; color: #1e293b; font-weight: 600;">预期效果 (12个月)</h4>
                            <div class="results-grid" style="display: flex; gap: 20px; justify-content: center;">
                                <div class="result-item" style="background: #fff; padding: 20px; border-radius: 10px; text-align: center; flex: 1; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                                    <div class="result-icon" style="background: #3b82f6; color: white; width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 10px;">
                                        <i class="fas fa-arrow-up" style="font-size: 16px;"></i>
                                    </div>
                                    <div class="result-content">
                                        <span class="result-value" style="display: block; font-size: 24px; font-weight: 700; color: #1e293b; margin-bottom: 5px;">40%</span>
                                        <span class="result-label" style="font-size: 12px; color: #64748b;">产品竞争力提升</span>
                                    </div>
                                </div>
                                <div class="result-item" style="background: #fff; padding: 20px; border-radius: 10px; text-align: center; flex: 1; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                                    <div class="result-icon" style="background: #10b981; color: white; width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 10px;">
                                        <i class="fas fa-star" style="font-size: 16px;"></i>
                                    </div>
                                    <div class="result-content">
                                        <span class="result-value" style="display: block; font-size: 24px; font-weight: 700; color: #1e293b; margin-bottom: 5px;">4.5+</span>
                                        <span class="result-label" style="font-size: 12px; color: #64748b;">客户满意度提升至</span>
                                    </div>
                                </div>
                                <div class="result-item" style="background: #fff; padding: 20px; border-radius: 10px; text-align: center; flex: 1; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                                    <div class="result-icon" style="background: #f59e0b; color: white; width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 10px;">
                                        <i class="fas fa-eye" style="font-size: 16px;"></i>
                                    </div>
                                    <div class="result-content">
                                        <span class="result-value" style="display: block; font-size: 24px; font-weight: 700; color: #1e293b; margin-bottom: 5px;">25%</span>
                                        <span class="result-label" style="font-size: 12px; color: #64748b;">品牌认知度提升至</span>
                                    </div>
                                </div>
                                <div class="result-item" style="background: #fff; padding: 20px; border-radius: 10px; text-align: center; flex: 1; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                                    <div class="result-icon" style="background: #8b5cf6; color: white; width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 10px;">
                                        <i class="fas fa-chart-pie" style="font-size: 16px;"></i>
                                    </div>
                                    <div class="result-content">
                                        <span class="result-value" style="display: block; font-size: 24px; font-weight: 700; color: #1e293b; margin-bottom: 5px;">1.5%</span>
                                        <span class="result-label" style="font-size: 12px; color: #64748b;">市场份额达到</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="content-block" data-animate="fadeIn" data-delay="1000">
                <div class="data-source">
                    <p><strong>数据来源：</strong>基于竞品功能缺失分析及用户需求调研；时间轴基于产品开发周期及市场推广经验；<strong>Bejoan产品数据来自亚马逊美国平台。</strong></p>
                </div>
            </div>
        </div>
    </div>



    <script>
        // 幻灯片导航逻辑
        let currentSlide = 1;
        const totalSlides = 4;

        function showSlide(slideNumber) {
            document.querySelectorAll('.slide').forEach(slide => {
                slide.style.display = 'none';
            });

            const targetSlide = document.getElementById(`slide-${slideNumber}`);
            if (targetSlide) {
                targetSlide.style.display = 'block';
                _triggerAnimations(targetSlide);
            }

            currentSlide = slideNumber;
        }

        function nextSlide() {
            if (currentSlide < totalSlides) {
                showSlide(currentSlide + 1);
            }
        }

        function previousSlide() {
            if (currentSlide > 1) {
                showSlide(currentSlide - 1);
            }
        }

        function _triggerAnimations(slideElement) {
            const animatedElements = slideElement.querySelectorAll('[data-animate]');
            animatedElements.forEach((element, index) => {
                const animationType = element.dataset.animate;
                const delay = element.dataset.delay || (index * 200);

                setTimeout(() => {
                    element.classList.add('animate-' + animationType);
                }, delay);
            });
        }

        // 键盘导航
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'ArrowRight':
                case ' ':
                    e.preventDefault();
                    nextSlide();
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    previousSlide();
                    break;
            }
        });

        // 监听URL hash变化
        function handleHashChange() {
            const hash = window.location.hash;
            const match = hash.match(/#slide-(\d+)/);
            if (match) {
                const slideNumber = parseInt(match[1]);
                if (slideNumber >= 1 && slideNumber <= totalSlides) {
                    showSlide(slideNumber);
                }
            }
        }

        window.addEventListener('hashchange', handleHashChange);

        // 导出函数供父页面调用
        window.triggerAnimations = () => {
            const currentSlideElement = document.getElementById(`slide-${currentSlide}`);
            if (currentSlideElement) {
                _triggerAnimations(currentSlideElement);
            }
        };

        // 渠道策略雷达图初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化幻灯片 - 检查URL hash
            handleHashChange();
            if (!window.location.hash) {
                showSlide(1);
            }
        });
    </script>

    <script src="../assets/js/charts.js"></script>
    <script src="../assets/js/navigation.js"></script>
</body>
</html>
