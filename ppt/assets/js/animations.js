// 动画系统
class AnimationEngine {
    constructor() {
        this.animations = new Map();
        this.observers = [];
        this.isReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
        this.init();
    }
    
    init() {
        this.setupIntersectionObserver();
        this.setupScrollAnimations();
        this.setupHoverEffects();
        this.registerAnimations();
    }
    
    setupIntersectionObserver() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.triggerAnimation(entry.target);
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '50px'
        });
        
        this.observers.push(observer);
        
        // 观察所有需要动画的元素
        document.addEventListener('DOMContentLoaded', () => {
            this.observeElements();
        });
    }
    
    observeElements() {
        const animatedElements = document.querySelectorAll('[data-animate]');
        animatedElements.forEach(el => {
            this.observers[0].observe(el);
        });
    }
    
    triggerAnimation(element) {
        if (this.isReducedMotion) return;
        
        const animationType = element.dataset.animate;
        const delay = parseInt(element.dataset.delay) || 0;
        
        setTimeout(() => {
            this.playAnimation(element, animationType);
        }, delay);
    }
    
    playAnimation(element, type) {
        switch (type) {
            case 'fadeIn':
                this.fadeIn(element);
                break;
            case 'slideUp':
                this.slideUp(element);
                break;
            case 'slideDown':
                this.slideDown(element);
                break;
            case 'slideLeft':
                this.slideLeft(element);
                break;
            case 'slideRight':
                this.slideRight(element);
                break;
            case 'scaleIn':
                this.scaleIn(element);
                break;
            case 'rotateIn':
                this.rotateIn(element);
                break;
            case 'bounceIn':
                this.bounceIn(element);
                break;
            case 'typewriter':
                this.typewriter(element);
                break;
            case 'countUp':
                this.countUp(element);
                break;
            default:
                this.fadeIn(element);
        }
    }
    
    // 基础动画
    fadeIn(element, duration = 600) {
        element.style.opacity = '0';
        element.style.transition = `opacity ${duration}ms ease-out`;
        
        requestAnimationFrame(() => {
            element.style.opacity = '1';
        });
    }
    
    slideUp(element, duration = 600) {
        element.style.transform = 'translateY(30px)';
        element.style.opacity = '0';
        element.style.transition = `all ${duration}ms cubic-bezier(0.4, 0, 0.2, 1)`;
        
        requestAnimationFrame(() => {
            element.style.transform = 'translateY(0)';
            element.style.opacity = '1';
        });
    }
    
    slideDown(element, duration = 600) {
        element.style.transform = 'translateY(-30px)';
        element.style.opacity = '0';
        element.style.transition = `all ${duration}ms cubic-bezier(0.4, 0, 0.2, 1)`;
        
        requestAnimationFrame(() => {
            element.style.transform = 'translateY(0)';
            element.style.opacity = '1';
        });
    }
    
    slideLeft(element, duration = 600) {
        element.style.transform = 'translateX(30px)';
        element.style.opacity = '0';
        element.style.transition = `all ${duration}ms cubic-bezier(0.4, 0, 0.2, 1)`;
        
        requestAnimationFrame(() => {
            element.style.transform = 'translateX(0)';
            element.style.opacity = '1';
        });
    }
    
    slideRight(element, duration = 600) {
        element.style.transform = 'translateX(-30px)';
        element.style.opacity = '0';
        element.style.transition = `all ${duration}ms cubic-bezier(0.4, 0, 0.2, 1)`;
        
        requestAnimationFrame(() => {
            element.style.transform = 'translateX(0)';
            element.style.opacity = '1';
        });
    }
    
    scaleIn(element, duration = 400) {
        element.style.transform = 'scale(0.9)';
        element.style.opacity = '0';
        element.style.transition = `all ${duration}ms cubic-bezier(0.68, -0.55, 0.265, 1.55)`;
        
        requestAnimationFrame(() => {
            element.style.transform = 'scale(1)';
            element.style.opacity = '1';
        });
    }
    
    rotateIn(element, duration = 600) {
        element.style.transform = 'rotate(-10deg) scale(0.9)';
        element.style.opacity = '0';
        element.style.transition = `all ${duration}ms cubic-bezier(0.4, 0, 0.2, 1)`;
        
        requestAnimationFrame(() => {
            element.style.transform = 'rotate(0deg) scale(1)';
            element.style.opacity = '1';
        });
    }
    
    bounceIn(element, duration = 800) {
        element.style.transform = 'scale(0.3)';
        element.style.opacity = '0';
        element.style.transition = `all ${duration}ms cubic-bezier(0.68, -0.55, 0.265, 1.55)`;
        
        requestAnimationFrame(() => {
            element.style.transform = 'scale(1)';
            element.style.opacity = '1';
        });
    }
    
    // 特殊动画
    typewriter(element, speed = 50) {
        const text = element.textContent;
        element.textContent = '';
        element.style.opacity = '1';
        
        let i = 0;
        const timer = setInterval(() => {
            element.textContent += text.charAt(i);
            i++;
            if (i >= text.length) {
                clearInterval(timer);
            }
        }, speed);
    }
    
    countUp(element, duration = 2000) {
        const target = parseInt(element.dataset.target) || parseInt(element.textContent);
        const start = parseInt(element.dataset.start) || 0;
        const startTime = performance.now();
        
        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            const current = Math.floor(start + (target - start) * this.easeOutQuart(progress));
            element.textContent = current.toLocaleString();
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };
        
        requestAnimationFrame(animate);
    }
    
    // 缓动函数
    easeOutQuart(t) {
        return 1 - Math.pow(1 - t, 4);
    }
    
    easeInOutCubic(t) {
        return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;
    }
    
    // 悬停效果
    setupHoverEffects() {
        document.addEventListener('mouseover', (e) => {
            if (e.target.classList.contains('hover-lift')) {
                this.liftEffect(e.target);
            }
            if (e.target.classList.contains('hover-glow')) {
                this.glowEffect(e.target);
            }
            if (e.target.classList.contains('hover-shake')) {
                this.shakeEffect(e.target);
            }
        });
    }
    
    liftEffect(element) {
        element.style.transition = 'transform 0.3s ease-out, box-shadow 0.3s ease-out';
        element.style.transform = 'translateY(-5px)';
        element.style.boxShadow = '0 10px 25px rgba(0, 0, 0, 0.15)';
        
        element.addEventListener('mouseleave', () => {
            element.style.transform = 'translateY(0)';
            element.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)';
        }, { once: true });
    }
    
    glowEffect(element) {
        element.style.transition = 'box-shadow 0.3s ease-out';
        element.style.boxShadow = '0 0 20px rgba(0, 115, 230, 0.5)';
        
        element.addEventListener('mouseleave', () => {
            element.style.boxShadow = 'none';
        }, { once: true });
    }
    
    shakeEffect(element) {
        element.style.animation = 'shake 0.5s ease-in-out';
        
        setTimeout(() => {
            element.style.animation = '';
        }, 500);
    }
    
    // 滚动动画
    setupScrollAnimations() {
        let ticking = false;
        
        window.addEventListener('scroll', () => {
            if (!ticking) {
                requestAnimationFrame(() => {
                    this.updateScrollAnimations();
                    ticking = false;
                });
                ticking = true;
            }
        });
    }
    
    updateScrollAnimations() {
        const scrolled = window.pageYOffset;
        const parallaxElements = document.querySelectorAll('[data-parallax]');
        
        parallaxElements.forEach(element => {
            const speed = parseFloat(element.dataset.parallax) || 0.5;
            const yPos = -(scrolled * speed);
            element.style.transform = `translateY(${yPos}px)`;
        });
    }
    
    // 页面转场动画
    slideTransition(direction = 'next') {
        const container = document.querySelector('.slide-container');
        const iframe = document.getElementById('slideFrame');
        
        if (direction === 'next') {
            container.style.transform = 'translateX(-100%)';
        } else {
            container.style.transform = 'translateX(100%)';
        }
        
        setTimeout(() => {
            container.style.transition = 'transform 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
            container.style.transform = 'translateX(0)';
        }, 50);
        
        setTimeout(() => {
            container.style.transition = '';
        }, 650);
    }
    
    // 注册自定义动画
    registerAnimations() {
        // 波纹效果
        this.registerAnimation('ripple', (element, options = {}) => {
            const ripple = document.createElement('span');
            ripple.className = 'ripple-effect';
            
            const rect = element.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = (options.x || rect.width / 2) - size / 2;
            const y = (options.y || rect.height / 2) - size / 2;
            
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            
            element.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
        
        // 脉冲效果
        this.registerAnimation('pulse', (element, options = {}) => {
            const duration = options.duration || 1000;
            element.style.animation = `pulse ${duration}ms ease-in-out infinite`;
        });
    }
    
    registerAnimation(name, callback) {
        this.animations.set(name, callback);
    }
    
    playCustomAnimation(name, element, options) {
        const animation = this.animations.get(name);
        if (animation) {
            animation(element, options);
        }
    }
    
    // 批量动画
    staggerAnimation(elements, animationType, delay = 100) {
        elements.forEach((element, index) => {
            setTimeout(() => {
                this.playAnimation(element, animationType);
            }, index * delay);
        });
    }
    
    // 动画序列
    sequence(animations) {
        return animations.reduce((promise, animation) => {
            return promise.then(() => {
                return new Promise(resolve => {
                    animation.callback();
                    setTimeout(resolve, animation.duration || 0);
                });
            });
        }, Promise.resolve());
    }
}

// CSS动画关键帧
const animationStyles = `
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.05); opacity: 0.8; }
}

.ripple-effect {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple 0.6s linear;
    pointer-events: none;
}

@keyframes ripple {
    to {
        transform: scale(4);
        opacity: 0;
    }
}
`;

// 注入CSS样式
const styleSheet = document.createElement('style');
styleSheet.textContent = animationStyles;
document.head.appendChild(styleSheet);

// 初始化动画引擎
window.animationEngine = new AnimationEngine();

// 导出给其他模块使用
window.AnimationEngine = AnimationEngine;
