/* ===================================
   第2章增强样式 - 渠道战场
   ================================== */

/* === 第2章首页样式 === */

/* 背景装饰系统 - 第2章特定图片 */
.bg-image {
    background-image: url('https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80');
}




/* === 第6页：市场容量与增长潜力增强样式 === */

/* 市场概览增强 */
.market-overview {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2xl);
}

.market-insight {
    background: linear-gradient(135deg, var(--primary-alpha-10), var(--secondary-alpha-10));
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    border: 2px solid var(--border-light);
    position: relative;
    overflow: hidden;
}

.market-insight::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--accent-color));
}

.insight-text {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    line-height: var(--line-height-relaxed);
    margin: 0;
    text-align: center;
    font-style: italic;
}

/* 图表容器增强 */
.chart-container {
    background: var(--background-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    border: 2px solid var(--border-light);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-base);
}

.chart-container:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-alpha-20);
}

.chart-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.chart-title {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.chart-subtitle {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    margin: 0;
}

/* 仪表板网格增强 */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-lg);
    margin: var(--spacing-xl) 0;
}

.dashboard-metric {
    background: var(--background-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    border: 2px solid var(--border-light);
    box-shadow: var(--shadow-sm);
    text-align: center;
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.dashboard-metric::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    transition: all var(--transition-base);
    transform: scaleX(0);
}

.dashboard-metric:nth-child(1)::before {
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
}

.dashboard-metric:nth-child(2)::before {
    background: linear-gradient(90deg, var(--secondary-color), var(--secondary-light));
}

.dashboard-metric:nth-child(3)::before {
    background: linear-gradient(90deg, var(--accent-color), var(--accent-light));
}

.dashboard-metric:hover::before {
    transform: scaleX(1);
}

.dashboard-metric:hover {
    transform: translateY(-6px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-alpha-20);
}

.dashboard-metric .metric-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto var(--spacing-md);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--font-size-xl);
    transition: all var(--transition-base);
}

.dashboard-metric:nth-child(1) .metric-icon {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
}

.dashboard-metric:nth-child(2) .metric-icon {
    background: linear-gradient(135deg, var(--secondary-color), var(--secondary-light));
}

.dashboard-metric:nth-child(3) .metric-icon {
    background: linear-gradient(135deg, var(--accent-color), var(--accent-light));
}

.dashboard-metric:nth-child(4) .metric-icon {
    background: linear-gradient(135deg, var(--accent-color), var(--accent-light));
}

.dashboard-metric:hover .metric-icon {
    transform: scale(1.1);
    box-shadow: var(--shadow-lg);
}

.dashboard-metric .metric-value {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.dashboard-metric .metric-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
    font-weight: var(--font-weight-medium);
}

.dashboard-metric .metric-change {
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-semibold);
    padding: 4px 8px;
    border-radius: var(--radius-full);
    display: inline-block;
}

.dashboard-metric .metric-change.positive {
    background: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(40, 167, 69, 0.2);
}

.dashboard-metric .metric-change.negative {
    background: rgba(220, 53, 69, 0.1);
    color: var(--error-color);
    border: 1px solid rgba(220, 53, 69, 0.2);
}

/* 图表控制按钮增强 */
.chart-controls {
    display: flex;
    justify-content: center;
    gap: var(--spacing-sm);
    margin: var(--spacing-lg) 0;
}

.chart-control-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 2px solid var(--border-color);
    background: var(--background-primary);
    color: var(--text-secondary);
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.chart-control-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    opacity: 0;
    transition: opacity var(--transition-base);
    z-index: -1;
}

.chart-control-btn span {
    position: relative;
    z-index: 2;
}

.chart-control-btn:hover::before,
.chart-control-btn.active::before {
    opacity: 1;
}

.chart-control-btn:hover span,
.chart-control-btn.active span {
    color: white;
    position: relative;
    z-index: 3;
}

.chart-control-btn:hover,
.chart-control-btn.active {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
}

/* 数据来源增强 */
.data-source {
    background: var(--background-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md);
    border: 1px solid var(--border-light);
    margin-top: var(--spacing-xl);
}

.data-source p {
    font-size: var(--font-size-xs);
    color: var(--text-tertiary);
    margin: 0;
    line-height: var(--line-height-relaxed);
}

.data-source strong {
    color: var(--text-secondary);
    font-weight: var(--font-weight-semibold);
}

/* 响应式设计 - 第6页 */
@media (max-width: 768px) {
    .dashboard-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);
    }
}

@media (max-width: 480px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .dashboard-metric {
        padding: var(--spacing-md);
    }
    
    .dashboard-metric .metric-icon {
        width: 50px;
        height: 50px;
        font-size: var(--font-size-lg);
    }
    
    .dashboard-metric .metric-value {
        font-size: var(--font-size-xl);
    }
    
    .chart-controls {
        flex-direction: column;
        align-items: center;
    }
    
    .chart-control-btn {
        width: 100%;
        max-width: 200px;
    }
    
    .insight-text {
        font-size: var(--font-size-base);
    }
    
    .chart-title {
        font-size: var(--font-size-lg);
    }
}

/* === 第7页：价格分布分析增强样式 === */

/* 价格分析容器 */
.price-analysis {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2xl);
}

.analysis-insight {
    background: linear-gradient(135deg, var(--accent-alpha-10), var(--primary-alpha-10));
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    border: 2px solid var(--border-light);
    position: relative;
    overflow: hidden;
}

.analysis-insight::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--accent-color), var(--primary-color));
}

/* 价格段热力图 */
.price-heatmap {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-lg);
    margin: var(--spacing-xl) 0;
}

.price-segment {
    background: var(--background-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    border: 2px solid var(--border-light);
    text-align: center;
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.price-segment.hot {
    border-color: var(--accent-color);
    background: linear-gradient(135deg, rgba(255, 107, 53, 0.05), rgba(255, 107, 53, 0.1));
}

.price-segment.warm {
    border-color: var(--warning-color);
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.05), rgba(255, 193, 7, 0.1));
}

.price-segment.cool {
    border-color: var(--info-color);
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.05), rgba(23, 162, 184, 0.1));
}

.price-segment::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    transition: all var(--transition-base);
}

.price-segment.hot::before {
    background: linear-gradient(90deg, var(--accent-color), var(--accent-light));
}

.price-segment.warm::before {
    background: linear-gradient(90deg, var(--warning-color), #ffd54f);
}

.price-segment.cool::before {
    background: linear-gradient(90deg, var(--info-color), #4fc3f7);
}

.price-segment:hover {
    transform: translateY(-6px);
    box-shadow: var(--shadow-lg);
}

.price-segment:hover::before {
    height: 6px;
}

.segment-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.segment-range {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
}

.segment-value {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-xs);
}

.price-segment.hot .segment-value {
    color: var(--accent-color);
}

.price-segment.warm .segment-value {
    color: var(--warning-color);
}

.price-segment.cool .segment-value {
    color: var(--info-color);
}

.segment-label {
    font-size: var(--font-size-xs);
    color: var(--text-tertiary);
    margin-bottom: var(--spacing-sm);
}

.segment-products {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-secondary);
}

/* 价格段图标 */
.segment-icon {
    font-size: var(--font-size-3xl);
    text-align: center;
    margin-bottom: var(--spacing-md);
    display: block;
}

/* 竞争强度指示器 */
.competition-level {
    margin-top: var(--spacing-md);
}

.level-text {
    font-size: var(--font-size-xs);
    color: var(--text-tertiary);
    display: block;
    text-align: center;
    margin-bottom: var(--spacing-xs);
}

.level-bar {
    width: 100%;
    height: 6px;
    background: var(--background-tertiary);
    border-radius: var(--radius-full);
    overflow: hidden;
    margin-bottom: var(--spacing-sm);
}

.level-fill {
    height: 100%;
    border-radius: var(--radius-full);
    transition: width 2s ease-out;
}

.price-segment.hot .level-fill {
    background: linear-gradient(90deg, var(--accent-color), var(--accent-light));
}

.price-segment.warm .level-fill {
    background: linear-gradient(90deg, var(--warning-color), #ffd54f);
}

.price-segment.cool .level-fill {
    background: linear-gradient(90deg, var(--info-color), #4fc3f7);
}

/* 策略建议标签 */
.segment-strategy {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-full);
    text-align: center;
    margin-top: var(--spacing-sm);
}

.segment-strategy.hot {
    background: rgba(255, 107, 53, 0.1);
    color: var(--accent-color);
    border: 1px solid rgba(255, 107, 53, 0.3);
}

.segment-strategy.warm {
    background: rgba(255, 193, 7, 0.1);
    color: var(--warning-color);
    border: 1px solid rgba(255, 193, 7, 0.3);
}

.segment-strategy.cool {
    background: rgba(23, 162, 184, 0.1);
    color: var(--info-color);
    border: 1px solid rgba(23, 162, 184, 0.3);
}

/* 价格机会地图 */
.price-opportunity-map {
    background: var(--background-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    border: 2px solid var(--border-light);
    box-shadow: var(--shadow-md);
    margin: var(--spacing-xl) 0;
}

.opportunity-header {
    text-align: center;
    margin-bottom: var(--spacing-lg);
}

.opportunity-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.opportunity-subtitle {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
}

.opportunity-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg);
}

.opportunity-item {
    background: var(--background-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-light);
    transition: all var(--transition-base);
}

.opportunity-item:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-sm);
    border-color: var(--primary-color);
}

.opportunity-item h4 {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
}

.opportunity-item p {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    line-height: var(--line-height-relaxed);
    margin: 0;
}

/* 响应式设计 - 第7页 */
@media (max-width: 768px) {
    .price-heatmap {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .opportunity-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .price-segment {
        padding: var(--spacing-md);
    }

    .segment-title {
        font-size: var(--font-size-base);
    }

    .segment-value {
        font-size: var(--font-size-xl);
    }

    .price-opportunity-map {
        padding: var(--spacing-lg);
    }
}

/* === 第8页：Menards认知盲点增强样式 === */

/* 认知对比容器 */
.perception-comparison {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-2xl);
    margin: var(--spacing-2xl) 0;
}

.perception-card {
    background: var(--background-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    border: 2px solid var(--border-light);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.perception-card.online {
    border-color: var(--error-color);
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.05), rgba(220, 53, 69, 0.1));
}

.perception-card.offline {
    border-color: var(--success-color);
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.05), rgba(40, 167, 69, 0.1));
}

.perception-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
}

.perception-card.online::before {
    background: linear-gradient(90deg, var(--error-color), #ff6b6b);
}

.perception-card.offline::before {
    background: linear-gradient(90deg, var(--success-color), #51cf66);
}

.perception-card:hover {
    transform: translateY(-6px);
    box-shadow: var(--shadow-xl);
}

.perception-header {
    text-align: center;
    margin-bottom: var(--spacing-lg);
}

.perception-title {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--spacing-sm);
}

.perception-card.online .perception-title {
    color: var(--error-color);
}

.perception-card.offline .perception-title {
    color: var(--success-color);
}

.perception-subtitle {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
}

/* === 第9页：品牌竞争格局增强样式 === */

/* 品牌生态系统 */
.brand-ecosystem {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2xl);
}

.ecosystem-platform {
    background: var(--background-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    border: 2px solid var(--border-light);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-base);
}

.ecosystem-platform:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.platform-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.platform-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--font-size-xl);
}

.platform-icon.home-depot {
    background: linear-gradient(135deg, #f96302, #ff8533);
}

.platform-icon.lowes {
    background: linear-gradient(135deg, #004990, #0066cc);
}

.platform-icon.menards {
    background: linear-gradient(135deg, #ffcc00, #ffd633);
}

.platform-info h3 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.platform-info p {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin: 0;
}

/* 品牌层级 */
.brand-tiers {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-lg);
}

.tier {
    background: var(--background-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-light);
    transition: all var(--transition-base);
}

.tier:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-sm);
    border-color: var(--primary-color);
}

.tier h5 {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    text-align: center;
}

.brand-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-sm);
}

.brand-item {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    padding: var(--spacing-xs);
    background: var(--background-primary);
    border-radius: var(--radius-sm);
    text-align: center;
    transition: all var(--transition-base);
}

.brand-item.leader {
    color: var(--primary-color);
    font-weight: var(--font-weight-semibold);
    background: var(--primary-alpha-10);
    border: 1px solid var(--primary-alpha-20);
}

.brand-item:hover {
    transform: translateX(4px);
}

.tier-metrics {
    display: flex;
    justify-content: space-between;
    font-size: var(--font-size-xs);
    color: var(--text-tertiary);
}

/* === 第10页：品牌生态多维表格样式 === */

/* 品牌生态表格容器 */
.brand-ecosystem-table {
    margin: var(--spacing-2xl) 0;
}

.table-container {
    background: var(--background-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    border: 2px solid var(--border-light);
    box-shadow: var(--shadow-md);
    overflow-x: auto;
}

/* 生态表格样式 */
.ecosystem-table {
    width: 100%;
    border-collapse: collapse;
    font-family: var(--font-family-primary);
}

.ecosystem-table th,
.ecosystem-table td {
    padding: var(--spacing-lg);
    text-align: left;
    vertical-align: top;
    border: 1px solid var(--border-light);
}

/* 表头样式 */
.ecosystem-table thead th {
    background: var(--background-secondary);
    font-weight: var(--font-weight-semibold);
    position: sticky;
    top: 0;
    z-index: 10;
}

.category-header {
    background: var(--background-tertiary) !important;
    font-size: var(--font-size-base);
    color: var(--text-primary);
    width: 150px;
    min-width: 150px;
}

.channel-header {
    text-align: center;
    width: 200px;
    min-width: 200px;
    position: relative;
}

.channel-header.home-depot {
    background: linear-gradient(135deg, rgba(249, 99, 2, 0.1), rgba(255, 133, 51, 0.1)) !important;
    border-top: 4px solid #f96302;
}

.channel-header.lowes {
    background: linear-gradient(135deg, rgba(0, 73, 144, 0.1), rgba(0, 102, 204, 0.1)) !important;
    border-top: 4px solid #004990;
}

.channel-header.menards {
    background: linear-gradient(135deg, rgba(255, 204, 0, 0.1), rgba(255, 214, 51, 0.1)) !important;
    border-top: 4px solid #ffcc00;
}

.channel-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    height: 100%;
    padding: var(--spacing-sm) 0;
}

.channel-info i {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-xs);
}

.channel-header.home-depot .channel-info i {
    color: #f96302;
}

.channel-header.lowes .channel-info i {
    color: #004990;
}

.channel-header.menards .channel-info i {
    color: #ffcc00;
}

.channel-info span {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
}

.channel-info small {
    font-size: var(--font-size-xs);
    color: var(--text-tertiary);
}

/* 行样式 */
.tier-row {
    transition: all var(--transition-base);
}

.tier-row:hover {
    background: var(--background-secondary);
}

.tier-row.premium {
    border-left: 4px solid var(--accent-color);
}

.tier-row.mid-range {
    border-left: 4px solid var(--primary-color);
}

.tier-row.economy {
    border-left: 4px solid var(--secondary-color);
}

/* 价格段标签 */
.tier-label {
    background: var(--background-secondary);
    font-weight: var(--font-weight-semibold);
}

.tier-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.tier-name {
    font-size: var(--font-size-base);
    color: var(--text-primary);
}

.tier-range {
    font-size: var(--font-size-xs);
    color: var(--text-tertiary);
    font-weight: var(--font-weight-normal);
}

/* 品牌单元格 */
.brand-cell {
    position: relative;
    transition: all var(--transition-base);
}

.brand-cell:hover {
    background: var(--primary-alpha-10);
    transform: scale(1.02);
}

.brand-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.brand-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.brand-item {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    padding: var(--spacing-xs);
    background: var(--background-secondary);
    border-radius: var(--radius-sm);
    transition: all var(--transition-base);
}

.brand-item.leader {
    color: var(--primary-color);
    font-weight: var(--font-weight-semibold);
    background: var(--primary-alpha-10);
    border: 1px solid var(--primary-alpha-20);
}

.brand-item.opportunity {
    color: var(--success-color);
    font-weight: var(--font-weight-semibold);
    background: rgba(40, 167, 69, 0.1);
    border: 1px solid rgba(40, 167, 69, 0.2);
}

.brand-item:hover {
    transform: translateX(2px);
}

/* 指标显示 */
.metrics {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: space-between;
    margin-top: var(--spacing-xs);
}

.metric-item {
    font-size: var(--font-size-xs);
    color: var(--text-tertiary);
    background: var(--background-tertiary);
    padding: 2px 6px;
    border-radius: var(--radius-sm);
    font-weight: var(--font-weight-medium);
}

/* 空白状态 */
.brand-cell.empty {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.05), rgba(255, 193, 7, 0.1));
}

.brand-cell.special {
    background: linear-gradient(135deg, rgba(0, 166, 81, 0.05), rgba(0, 166, 81, 0.1));
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    text-align: center;
}

.empty-text {
    font-size: var(--font-size-sm);
    color: var(--text-tertiary);
    font-style: italic;
}

.opportunity-text {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--warning-color);
}

.special-note {
    font-size: var(--font-size-xs);
    color: var(--text-tertiary);
    font-style: italic;
    text-align: center;
    margin-top: var(--spacing-xs);
}

/* 表格图例 */
.table-legend {
    display: flex;
    justify-content: center;
    gap: var(--spacing-lg);
    margin-top: var(--spacing-lg);
    padding: var(--spacing-md);
    background: var(--background-secondary);
    border-radius: var(--radius-lg);
}

.legend-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.legend-icon {
    font-size: var(--font-size-sm);
}

.legend-text {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
}

.channel-brands:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-alpha-20);
}

.channel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 2px solid var(--border-light);
}

.channel-header h4 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin: 0;
}

.channel-count {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    background: var(--background-secondary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-full);
    border: 1px solid var(--border-light);
}

/* 品牌层级网格 */
.brand-tiers {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.tier {
    background: var(--background-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md);
    border: 1px solid var(--border-light);
    transition: all var(--transition-base);
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.tier:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
    border-color: var(--primary-color);
}

.tier-title {
    flex: 0 0 200px;
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin: 0;
}

.tier h5 {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin: 0;
}

.brand-list {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
    align-items: center;
}

.brand-item {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--background-primary);
    border-radius: var(--radius-sm);
    text-align: center;
    transition: all var(--transition-base);
    white-space: nowrap;
    flex-shrink: 0;
}

.brand-item.leader {
    color: var(--primary-color);
    font-weight: var(--font-weight-semibold);
    background: var(--primary-alpha-10);
    border: 1px solid var(--primary-alpha-20);
}

.brand-item.opportunity {
    color: var(--success-color);
    font-weight: var(--font-weight-semibold);
    background: rgba(40, 167, 69, 0.1);
    border: 1px solid rgba(40, 167, 69, 0.2);
}

.brand-item:hover {
    transform: translateX(4px);
}

.tier-metrics {
    flex: 0 0 150px;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    font-size: var(--font-size-xs);
    color: var(--text-tertiary);
    text-align: right;
}

.tier-metrics .metric {
    font-weight: var(--font-weight-medium);
    white-space: nowrap;
}

/* 生态对比网格 */
.ecosystem-comparison {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-xl);
    margin: var(--spacing-2xl) 0;
}

.ecosystem-card {
    background: var(--background-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    border: 2px solid var(--border-light);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.ecosystem-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    transition: all var(--transition-base);
}

.ecosystem-card:nth-child(1)::before {
    background: linear-gradient(90deg, #f96302, #ff8533);
}

.ecosystem-card:nth-child(2)::before {
    background: linear-gradient(90deg, #004990, #0066cc);
}

.ecosystem-card:nth-child(3)::before {
    background: linear-gradient(90deg, #ffcc00, #ffd633);
}

.ecosystem-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.ecosystem-card:hover::before {
    height: 6px;
}

/* 响应式设计 - 第8-10页 */
@media (max-width: 768px) {
    .perception-comparison {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .ecosystem-comparison {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .platform-header {
        flex-direction: column;
        text-align: center;
    }

    .platform-icon {
        width: 50px;
        height: 50px;
        font-size: var(--font-size-lg);
    }

    /* 表格响应式 */
    .table-container {
        padding: var(--spacing-md);
        overflow-x: auto;
    }

    .ecosystem-table {
        min-width: 600px;
    }

    .ecosystem-table th,
    .ecosystem-table td {
        padding: var(--spacing-sm);
    }

    .category-header {
        width: 120px;
        min-width: 120px;
    }

    .channel-header {
        width: 160px;
        min-width: 160px;
    }

    .channel-info span {
        font-size: var(--font-size-sm);
    }

    .channel-info small {
        font-size: 10px;
    }

    .brand-item {
        font-size: 11px;
        padding: 2px 4px;
    }

    .metric-item {
        font-size: 10px;
        padding: 1px 4px;
    }

    .table-legend {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: center;
    }

    .legend-item {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .table-container {
        padding: var(--spacing-sm);
    }

    .ecosystem-table {
        min-width: 500px;
    }

    .category-header {
        width: 100px;
        min-width: 100px;
    }

    .channel-header {
        width: 130px;
        min-width: 130px;
    }

    .tier-name {
        font-size: var(--font-size-sm);
    }

    .tier-range {
        font-size: 10px;
    }
}

/* === 第11页：价格守门员产品分析样式 === */

/* 价格守门员表格 */
.price-gatekeepers-table {
    margin: var(--spacing-2xl) 0;
}

.gatekeepers-table {
    width: 100%;
    border-collapse: collapse;
    font-family: var(--font-family-primary);
    table-layout: fixed;
}

.gatekeepers-table th,
.gatekeepers-table td {
    padding: var(--spacing-lg);
    text-align: left;
    vertical-align: middle;
    border: 1px solid var(--border-light);
    box-sizing: border-box; /* Ensure consistent box model */
}

/* 表头样式 */
.gatekeepers-table thead th {
    background: var(--background-secondary);
    font-weight: var(--font-weight-semibold);
    position: sticky;
    top: 0;
    z-index: 10;
    height: 80px;
    vertical-align: middle;
    line-height: 1.2;
    padding: var(--spacing-md) var(--spacing-lg); /* Adjust padding for headers */
}

.gatekeepers-table thead tr {
    height: 80px;
}

.gatekeepers-table .category-header {
    background: var(--background-tertiary) !important;
    font-size: var(--font-size-base);
    color: var(--text-primary);
    width: 150px;
    min-width: 150px;
    text-align: center;
    vertical-align: middle;
    display: table-cell; /* Ensure it behaves like a table cell */
}

.gatekeepers-table .channel-header {
    text-align: center;
    width: 200px;
    min-width: 200px;
    position: relative;
    vertical-align: middle;
    display: table-cell; /* Ensure it behaves like a table cell */
}

/* 守门员产品卡片 */
.gatekeeper-cell {
    position: relative;
    transition: all var(--transition-base);
    min-height: 120px;
    padding: var(--spacing-md); /* Adjust padding for content cells */
}

.gatekeeper-cell:hover {
    background: var(--primary-alpha-10);
    transform: scale(1.02);
}

.product-card {
    background: var(--background-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md);
    border: 2px solid var(--border-light);
    transition: all var(--transition-base);
    height: 100%;
}

.product-card:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
}

.product-header {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-sm);
}

.product-name {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    line-height: var(--line-height-tight);
}

.product-badge {
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-semibold);
    padding: 2px 6px;
    border-radius: var(--radius-full);
    align-self: flex-start;
}

.product-badge.killer {
    background: rgba(255, 107, 53, 0.1);
    color: var(--accent-color);
    border: 1px solid rgba(255, 107, 53, 0.3);
}

.product-badge.traditional {
    background: rgba(0, 73, 144, 0.1);
    color: #004990;
    border: 1px solid rgba(0, 73, 144, 0.3);
}

.product-badge.benchmark {
    background: rgba(0, 115, 230, 0.1);
    color: var(--primary-color);
    border: 1px solid rgba(0, 115, 230, 0.3);
}

.product-badge.leader {
    background: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(40, 167, 69, 0.3);
}

.product-badge.premium {
    background: rgba(138, 43, 226, 0.1);
    color: #8a2be2;
    border: 1px solid rgba(138, 43, 226, 0.3);
}

.product-metrics {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
    flex-wrap: wrap;
}

.product-metrics .metric {
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    padding: 2px 6px;
    border-radius: var(--radius-sm);
    background: var(--background-secondary);
    color: var(--text-secondary);
}

.product-metrics .metric.price {
    background: var(--primary-alpha-10);
    color: var(--primary-color);
    font-weight: var(--font-weight-semibold);
}

.product-insight {
    font-size: var(--font-size-xs);
    color: var(--text-tertiary);
    font-style: italic;
    line-height: var(--line-height-relaxed);
}

/* 竞争策略建议 */
.strategy-recommendations {
    background: var(--background-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    border: 2px solid var(--border-light);
    box-shadow: var(--shadow-md);
    margin-top: var(--spacing-2xl);
}

.recommendations-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    text-align: center;
    margin-bottom: var(--spacing-lg);
}

.recommendations-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-lg);
}

.recommendation-card {
    background: var(--background-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    border: 2px solid var(--border-light);
    transition: all var(--transition-base);
}

.recommendation-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-md);
}

.recommendation-card.home-depot {
    border-color: #f96302;
    background: linear-gradient(135deg, rgba(249, 99, 2, 0.05), rgba(255, 133, 51, 0.05));
}

.recommendation-card.lowes {
    border-color: #004990;
    background: linear-gradient(135deg, rgba(0, 73, 144, 0.05), rgba(0, 102, 204, 0.05));
}

.recommendation-card.menards {
    border-color: #ffcc00;
    background: linear-gradient(135deg, rgba(255, 204, 0, 0.05), rgba(255, 214, 51, 0.05));
}

.card-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-light);
}

.card-header i {
    font-size: var(--font-size-lg);
}

.recommendation-card.home-depot .card-header i {
    color: #f96302;
}

.recommendation-card.lowes .card-header i {
    color: #004990;
}

.recommendation-card.menards .card-header i {
    color: #ffcc00;
}

.card-header span {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
}

.card-content p {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    line-height: var(--line-height-relaxed);
    margin-bottom: var(--spacing-sm);
}

.card-content p:last-child {
    margin-bottom: 0;
}

.card-content strong {
    color: var(--text-primary);
    font-weight: var(--font-weight-semibold);
}

/* 响应式设计 - 第11页 */
@media (max-width: 768px) {
    .recommendations-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .gatekeepers-table {
        min-width: 600px;
    }

    .product-card {
        padding: var(--spacing-sm);
    }

    .product-name {
        font-size: 11px;
    }

    .product-metrics {
        gap: var(--spacing-xs);
    }

    .product-metrics .metric {
        font-size: 10px;
        padding: 1px 4px;
    }
}

@media (max-width: 480px) {
    .gatekeepers-table {
        min-width: 500px;
    }

    .recommendation-card {
        padding: var(--spacing-md);
    }

    .card-content p {
        font-size: 12px;
    }

}

/* === 幻灯片6：各渠道价格策略与竞争态势分析样式 === */

/* 价格策略分析容器 */
.price-strategy-analysis {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2xl);
}

.strategy-insight {
    background: linear-gradient(135deg, var(--primary-alpha-10), var(--secondary-alpha-10));
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    border: 2px solid var(--border-light);
    position: relative;
    overflow: hidden;
}

.strategy-insight::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--accent-color));
}

.strategy-insight .insight-text {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    line-height: var(--line-height-relaxed);
    margin: 0;
    text-align: center;
    font-style: italic;
}

/* 价格分布区域 */
.price-distribution-section {
    background: var(--background-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    border: 2px solid var(--border-light);
    box-shadow: var(--shadow-md);
    margin: var(--spacing-xl) 0;
}

.price-distribution-section h3 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    text-align: center;
    margin-bottom: var(--spacing-lg);
}

/* 渠道价格网格 */
.channel-price-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-lg);
}

.channel-price-card {
    background: var(--background-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    border: 2px solid var(--border-light);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.channel-price-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    transition: all var(--transition-base);
}

.channel-price-card.home-depot::before {
    background: linear-gradient(90deg, #f96302, #ff8533);
}

.channel-price-card.lowes::before {
    background: linear-gradient(90deg, #004990, #0066cc);
}

.channel-price-card.menards::before {
    background: linear-gradient(90deg, #ffcc00, #ffd633);
}

.channel-price-card:hover {
    transform: translateY(-6px);
    box-shadow: var(--shadow-lg);
}

.channel-price-card:hover::before {
    height: 6px;
}

.channel-price-card h4 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.channel-price-card.home-depot h4 i {
    color: #f96302;
}

.channel-price-card.lowes h4 i {
    color: #004990;
}

.channel-price-card.menards h4 i {
    color: #ffcc00;
}

/* 产品价格分解 */
.product-price-breakdown {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.product-type {
    background: var(--background-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md);
    border: 1px solid var(--border-light);
}

.product-type h5 {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    text-align: center;
}

.price-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.price-range-slide6 {
    padding: var(--spacing-sm) var(--spacing-lg);
    background: var(--primary-color);
    color: white;
    border-radius: 20px;
    font-size: var(--font-size-sm);
    font-weight: 600;
    margin: var(--spacing-sm) auto;
    width: fit-content;
}

.price-average {
    font-size: var(--font-size-sm);
    color: var(--primary-color);
    font-weight: var(--font-weight-semibold);
}

/* Bejoan策略区域 */
.bejoan-strategy-section {
    background: var(--background-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    border: 2px solid var(--border-light);
    box-shadow: var(--shadow-md);
    margin: var(--spacing-xl) 0;
}

.bejoan-strategy-section h3 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    text-align: center;
    margin-bottom: var(--spacing-lg);
}

.strategy-recommendations {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg);
}

.strategy-card {
    background: var(--background-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    border: 2px solid var(--border-light);
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.strategy-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    transition: all var(--transition-base);
}

.strategy-card.toilet-seat::before {
    background: linear-gradient(90deg, var(--secondary-color), var(--secondary-light));
}

.strategy-card.smart-toilet::before {
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
}

.strategy-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-md);
}

.strategy-card:hover::before {
    height: 6px;
}

.strategy-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-light);
}

.strategy-header h4 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin: 0;
}

.recommended-price {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--success-color);
    background: rgba(40, 167, 69, 0.1);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-full);
    border: 1px solid rgba(40, 167, 69, 0.2);
}

.strategy-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.strategy-approach,
.strategy-rationale {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    line-height: var(--line-height-relaxed);
}

.strategy-approach strong,
.strategy-rationale strong {
    color: var(--text-primary);
    font-weight: var(--font-weight-semibold);
}

/* 竞品分析区域 */
.competitor-analysis-section {
    background: var(--background-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    border: 2px solid var(--border-light);
    box-shadow: var(--shadow-md);
    margin: var(--spacing-xl) 0;
}

.competitor-analysis-section h3 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    text-align: center;
    margin-bottom: var(--spacing-lg);
}

.competitor-cards {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-lg);
}

.competitor-card {
    background: var(--background-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    border: 2px solid var(--border-light);
    text-align: center;
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.competitor-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    transition: all var(--transition-base);
}

.competitor-card.toto::before {
    background: linear-gradient(90deg, var(--accent-color), var(--accent-light));
}

.competitor-card.kohler::before {
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
}

.competitor-card.anzzi::before {
    background: linear-gradient(90deg, var(--secondary-color), var(--secondary-light));
}

.competitor-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-md);
}

.competitor-card:hover::before {
    height: 6px;
}

.brand-info {
    margin-bottom: var(--spacing-md);
}

.brand-info h4 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}



.positioning {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-style: italic;
}

/* 核心观点区域 */
.core-insight-section {
    background: linear-gradient(135deg, var(--accent-alpha-10), var(--primary-alpha-10));
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    border: 2px solid var(--border-light);
    position: relative;
    overflow: hidden;
    margin: var(--spacing-xl) 0;
}

.core-insight-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--accent-color), var(--primary-color));
}

.core-insight-section h3 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    text-align: center;
    margin-bottom: var(--spacing-lg);
}



/* 响应式设计 - 幻灯片6 */
@media (max-width: 768px) {
    .channel-price-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .strategy-recommendations {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .competitor-cards {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .channel-price-card,
    .strategy-card,
    .competitor-card {
        padding: var(--spacing-md);
    }
    
    .price-distribution-section,
    .bejoan-strategy-section,
    .competitor-analysis-section,
    .core-insight-section {
        padding: var(--spacing-lg);
    }
}

@media (max-width: 480px) {
    .product-price-breakdown {
        gap: var(--spacing-md);
    }
    
    .product-type {
        padding: var(--spacing-sm);
    }
    
    .strategy-header {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: flex-start;
    }
    
    .recommended-price {
        align-self: flex-start;
    }
}

/* === 幻灯片7：Home Depot价格守门员产品分析样式 === */

/* 守门员分析容器 */
.gatekeeper-analysis {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2xl);
}

.gatekeeper-analysis h3 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    text-align: center;
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    background: linear-gradient(135deg, var(--primary-alpha-10), var(--secondary-alpha-10));
    border-radius: var(--radius-lg);
    border: 2px solid var(--border-light);
    position: relative;
    overflow: hidden;
}

.gatekeeper-analysis h3::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

/* 守门员卡片网格 */
.gatekeeper-cards {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
}

.gatekeeper-card {
    background: var(--background-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    border: 2px solid var(--border-light);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
    min-height: 250px;
}

.gatekeeper-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    transition: all var(--transition-base);
}

.gatekeeper-card.economy::before {
    background: linear-gradient(90deg, var(--success-color), #51cf66);
}

.gatekeeper-card.mid-range::before {
    background: linear-gradient(90deg, var(--warning-color), #ffd54f);
}

.gatekeeper-card.premium::before {
    background: linear-gradient(90deg, var(--accent-color), var(--accent-light));
}

.gatekeeper-card:hover {
    transform: translateY(-6px);
    box-shadow: var(--shadow-lg);
}

.gatekeeper-card:hover::before {
    height: 6px;
}

.gatekeeper-card h4 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    text-align: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--border-light);
}

.gatekeeper-card.economy h4 {
    color: var(--success-color);
}

.gatekeeper-card.mid-range h4 {
    color: var(--warning-color);
}

.gatekeeper-card.premium h4 {
    color: var(--accent-color);
}

/* 产品信息区域 */
.product-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    text-align: center;
}

.product-info h5 {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-sm);
    background: var(--background-secondary);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-light);
}

.product-info p {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin: 0;
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--background-tertiary);
    border-radius: var(--radius-sm);
    border: 1px solid var(--border-light);
}

.product-info p:first-of-type {
    color: var(--primary-color);
    font-weight: var(--font-weight-semibold);
    background: var(--primary-alpha-10);
    border-color: var(--primary-alpha-20);
}

.product-info p:last-of-type {
    font-style: italic;
    background: linear-gradient(135deg, var(--accent-alpha-10), var(--primary-alpha-10));
    border-color: var(--accent-alpha-20);
    color: var(--text-primary);
    font-weight: var(--font-weight-medium);
}

/* 策略建议区域 */
.strategy-recommendation {
    background: var(--background-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    border: 2px solid var(--border-light);
    box-shadow: var(--shadow-md);
    margin-top: var(--spacing-2xl);
    position: relative;
    overflow: hidden;
}

.strategy-recommendation::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
}

.strategy-recommendation h3 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    text-align: center;
    margin-bottom: var(--spacing-lg);
}

.strategy-points {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg);
}

.strategy-point {
    background: var(--background-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    border: 2px solid var(--border-light);
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
    flex-direction: column;
}

.strategy-point::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    transition: all var(--transition-base);
}

.strategy-point:nth-child(1)::before {
    background: linear-gradient(90deg, var(--secondary-color), var(--secondary-light));
}

.strategy-point:nth-child(2)::before {
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
}

.strategy-point:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-md);
}

.strategy-point:hover::before {
    height: 6px;
}

.strategy-point h4 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    text-align: center;
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-light);
}

.strategy-point:nth-child(1) h4 {
    color: var(--secondary-color);
}

.strategy-point:nth-child(2) h4 {
    color: var(--primary-color);
}

.strategy-point p {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    line-height: var(--line-height-relaxed);
    margin: 0;
    text-align: center;
}

/* 响应式设计 - 幻灯片7 */
@media (max-width: 768px) {
    .gatekeeper-cards {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .strategy-points {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .gatekeeper-card {
        padding: var(--spacing-md);
        min-height: auto;
    }
    
    .strategy-point {
        padding: var(--spacing-md);
    }
    
    .strategy-recommendation {
        padding: var(--spacing-lg);
    }
}

@media (max-width: 480px) {
    .gatekeeper-card h4 {
        font-size: var(--font-size-base);
    }
    
    .product-info h5 {
        font-size: var(--font-size-sm);
        padding: var(--spacing-xs);
    }
    
    .product-info p {
        font-size: 12px;
        padding: 4px var(--spacing-xs);
    }
    
    .strategy-point h4 {
        font-size: var(--font-size-base);
    }
    
    .strategy-point p {
        font-size: 12px;
    }
}

/* === 幻灯片10：基于渠道特性的营销策略建议样式 === */

/* 营销策略容器 */
.marketing-strategy {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2xl);
}

.marketing-strategy h3 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    text-align: center;
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    background: linear-gradient(135deg, var(--primary-alpha-10), var(--secondary-alpha-10));
    border-radius: var(--radius-lg);
    border: 2px solid var(--border-light);
    position: relative;
    overflow: hidden;
}

.marketing-strategy h3::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

/* 策略矩阵 */
.strategy-matrix {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
}

.strategy-card {
    background: var(--background-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    border: 2px solid var(--border-light);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.strategy-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    transition: all var(--transition-base);
}

.strategy-card.home-depot::before {
    background: linear-gradient(90deg, #f96302, #ff8533);
}

.strategy-card.lowes::before {
    background: linear-gradient(90deg, #004990, #0066cc);
}

.strategy-card.menards::before {
    background: linear-gradient(90deg, #ffcc00, #ffd633);
}

.strategy-card:hover {
    transform: translateY(-6px);
    box-shadow: var(--shadow-lg);
}

.strategy-card:hover::before {
    height: 6px;
}

.strategy-card h4 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    text-align: center;
    /* margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--border-light); */
}

.strategy-card.home-depot h4 {
    color: #f96302;
}

.strategy-card.lowes h4 {
    color: #004990;
}

.strategy-card.menards h4 {
    color: #ffcc00;
}

/* 策略详情 */
.strategy-details {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.strategy-item {
    background: var(--background-secondary);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    border: 1px solid var(--border-light);
    transition: all var(--transition-base);
}

.strategy-item:hover {
    transform: translateX(4px);
    box-shadow: var(--shadow-sm);
}

.strategy-item h5 {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    text-align: center;
    padding-bottom: var(--spacing-xs);
    border-bottom: 1px solid var(--border-light);
}

.strategy-item p {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    line-height: var(--line-height-relaxed);
    margin: 0;
    text-align: center;
}

/* 整合营销区域 */
.integrated-marketing {
    background: var(--background-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    border: 2px solid var(--border-light);
    box-shadow: var(--shadow-md);
    margin: var(--spacing-xl) 0;
    position: relative;
    overflow: hidden;
}

.integrated-marketing::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--accent-color), var(--primary-color));
}

.integrated-marketing h3 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    text-align: center;
    margin-bottom: var(--spacing-lg);
}

/* 营销阶段 */
.marketing-phases {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg);
}

.phase-card {
    background: var(--background-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    border: 2px solid var(--border-light);
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.phase-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    transition: all var(--transition-base);
}

.phase-card:nth-child(1)::before {
    background: linear-gradient(90deg, var(--success-color), #51cf66);
}

.phase-card:nth-child(2)::before {
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
}

.phase-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-md);
}

.phase-card:hover::before {
    height: 6px;
}

.phase-card h4 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    text-align: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--border-light);
}

.phase-card:nth-child(1) h4 {
    color: var(--success-color);
}

.phase-card:nth-child(2) h4 {
    color: var(--primary-color);
}

/* 阶段详情 */
.phase-details {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.phase-goals,
.phase-strategies,
.phase-budget {
    background: var(--background-primary);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    border: 1px solid var(--border-light);
}

.phase-goals h5,
.phase-strategies h5,
.phase-budget h5 {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    text-align: center;
    padding-bottom: var(--spacing-xs);
    border-bottom: 1px solid var(--border-light);
}

.phase-goals ul,
.phase-strategies ul {
    list-style-position: inside;
    margin: 0;
    padding: 0;
}

.phase-goals li,
.phase-strategies li {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    line-height: var(--line-height-relaxed);
    margin-bottom: var(--spacing-xs);
    padding-left: var(--spacing-sm);
}

.phase-budget p {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    line-height: var(--line-height-relaxed);
    margin: 0;
    text-align: center;
    font-weight: var(--font-weight-medium);
}

/* 营销指标区域 */
.marketing-metrics {
    background: var(--background-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    border: 2px solid var(--border-light);
    box-shadow: var(--shadow-md);
    margin: var(--spacing-xl) 0;
    position: relative;
    overflow: hidden;
}

.marketing-metrics::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--secondary-color), var(--accent-color));
}

.marketing-metrics h3 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    text-align: center;
    margin-bottom: var(--spacing-lg);
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-lg);
}

.metric-item {
    background: var(--background-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    text-align: center;
    border: 2px solid var(--border-light);
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.metric-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    transition: all var(--transition-base);
}

.metric-item:nth-child(1)::before {
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
}

.metric-item:nth-child(2)::before {
    background: linear-gradient(90deg, var(--secondary-color), var(--secondary-light));
}

.metric-item:nth-child(3)::before {
    background: linear-gradient(90deg, var(--accent-color), var(--accent-light));
}

.metric-item:nth-child(4)::before {
    background: linear-gradient(90deg, var(--success-color), #51cf66);
}

.metric-item:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-md);
}

.metric-item:hover::before {
    height: 6px;
}

.metric-item h5 {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    padding-bottom: var(--spacing-xs);
    border-bottom: 1px solid var(--border-light);
}

.metric-item p {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    line-height: var(--line-height-relaxed);
    margin: 0;
    font-weight: var(--font-weight-medium);
}

/* 核心观点区域 */
.core-insight {
    background: linear-gradient(135deg, var(--accent-alpha-10), var(--primary-alpha-10));
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    border: 2px solid var(--border-light);
    position: relative;
    overflow: hidden;
    margin: var(--spacing-xl) 0;
}

.core-insight::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--accent-color), var(--primary-color));
}

.core-insight h3 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    text-align: center;
    margin-bottom: var(--spacing-lg);
}

.core-insight p {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    line-height: var(--line-height-relaxed);
    margin: 0;
    text-align: center;
    font-style: italic;
}

/* 响应式设计 - 幻灯片10 */
@media (max-width: 768px) {
    .strategy-matrix {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .marketing-phases {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .metrics-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);
    }
    
    .strategy-card,
    .phase-card {
        padding: var(--spacing-md);
    }
    
    .integrated-marketing,
    .marketing-metrics,
    .core-insight {
        padding: var(--spacing-lg);
    }
}

@media (max-width: 480px) {
    .metrics-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .strategy-item,
    .metric-item {
        padding: var(--spacing-sm);
    }
    
    .strategy-item h5,
    .metric-item h5 {
        font-size: var(--font-size-sm);
    }
    
    .strategy-item p,
    .metric-item p,
    .phase-goals li,
    .phase-strategies li,
    .phase-budget p {
        font-size: 12px;
    }
    
    .phase-details {
        gap: var(--spacing-md);
    }
}
