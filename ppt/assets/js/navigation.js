// 导航系统
class NavigationSystem {
    constructor() {
        this.history = [];
        this.currentIndex = -1;
        this.bookmarks = this.loadBookmarks();
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.createBreadcrumb();
    }
    
    bindEvents() {
        // 监听幻灯片变化
        document.addEventListener('slideChanged', (e) => {
            this.addToHistory(e.detail.slideNumber);
            this.updateBreadcrumb();
        });
        
        // 浏览器前进后退
        window.addEventListener('popstate', (e) => {
            if (e.state && e.state.slide) {
                window.presentationApp.goToSlide(e.state.slide);
            }
        });
    }
    
    addToHistory(slideNumber) {
        // 如果当前不在历史记录的末尾，删除后面的记录
        if (this.currentIndex < this.history.length - 1) {
            this.history = this.history.slice(0, this.currentIndex + 1);
        }
        
        // 添加新记录
        this.history.push(slideNumber);
        this.currentIndex = this.history.length - 1;
        
        // 更新浏览器历史
        history.pushState({ slide: slideNumber }, '', `#slide-${slideNumber}`);
        
        // 限制历史记录长度
        if (this.history.length > 50) {
            this.history.shift();
            this.currentIndex--;
        }
    }
    
    goBack() {
        if (this.currentIndex > 0) {
            this.currentIndex--;
            const slideNumber = this.history[this.currentIndex];
            window.presentationApp.goToSlide(slideNumber);
            return true;
        }
        return false;
    }
    
    goForward() {
        if (this.currentIndex < this.history.length - 1) {
            this.currentIndex++;
            const slideNumber = this.history[this.currentIndex];
            window.presentationApp.goToSlide(slideNumber);
            return true;
        }
        return false;
    }
    
    createBreadcrumb() {
        // 禁用面包屑导航功能
        return;
    }
    
    updateBreadcrumb() {
        // 禁用面包屑导航功能
        return;
    }
    
    getChapterName(chapterNumber) {
        const chapterNames = {
            1: '战略背景',
            2: '渠道战场',
            3: '核心玩家',
            4: '差异化策略',
            5: '战略选择',
            6: '总结行动'
        };
        return chapterNames[chapterNumber] || '未知章节';
    }
    
    toggleBookmark() {
        const currentSlide = window.presentationApp.currentSlide;
        const bookmarkIndex = this.bookmarks.findIndex(b => b.slide === currentSlide);
        
        if (bookmarkIndex > -1) {
            // 移除书签
            this.bookmarks.splice(bookmarkIndex, 1);
            this.showNotification('书签已移除');
        } else {
            // 添加书签
            const bookmark = {
                slide: currentSlide,
                title: this.getSlideTitle(currentSlide),
                timestamp: Date.now()
            };
            this.bookmarks.push(bookmark);
            this.showNotification('书签已添加');
        }
        
        this.saveBookmarks();
        this.updateBookmarkButton();
    }
    
    getSlideTitle(slideNumber) {
        // 这里可以根据实际情况获取幻灯片标题
        return `第 ${slideNumber} 页`;
    }
    
    updateBookmarkButton() {
        const bookmarkBtn = document.getElementById('bookmarkBtn');
        const currentSlide = window.presentationApp.currentSlide;
        const isBookmarked = this.bookmarks.some(b => b.slide === currentSlide);
        
        if (isBookmarked) {
            bookmarkBtn.classList.add('bookmarked');
            bookmarkBtn.title = '移除书签';
        } else {
            bookmarkBtn.classList.remove('bookmarked');
            bookmarkBtn.title = '添加书签';
        }
    }
    
    loadBookmarks() {
        try {
            const saved = localStorage.getItem('presentation-bookmarks');
            return saved ? JSON.parse(saved) : [];
        } catch (e) {
            return [];
        }
    }
    
    saveBookmarks() {
        try {
            localStorage.setItem('presentation-bookmarks', JSON.stringify(this.bookmarks));
        } catch (e) {
            console.warn('无法保存书签');
        }
    }
    
    showNotification(message) {
        const notification = document.createElement('div');
        notification.className = 'notification';
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        // 触发动画
        setTimeout(() => notification.classList.add('show'), 10);
        
        // 自动移除
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => document.body.removeChild(notification), 300);
        }, 2000);
    }
    
    createBookmarkPanel() {
        const panel = document.createElement('div');
        panel.className = 'bookmark-panel';
        panel.innerHTML = `
            <div class="bookmark-header">
                <h3>书签</h3>
                <button class="close-btn" id="closeBookmarkPanel">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="bookmark-list" id="bookmarkList"></div>
        `;
        
        document.body.appendChild(panel);
        
        // 绑定事件
        document.getElementById('closeBookmarkPanel').addEventListener('click', () => {
            panel.classList.remove('show');
        });
        
        this.updateBookmarkList();
        return panel;
    }
    
    updateBookmarkList() {
        const listElement = document.getElementById('bookmarkList');
        if (!listElement) return;
        
        if (this.bookmarks.length === 0) {
            listElement.innerHTML = '<div class="empty-bookmarks">暂无书签</div>';
            return;
        }
        
        listElement.innerHTML = this.bookmarks.map(bookmark => `
            <div class="bookmark-item" data-slide="${bookmark.slide}">
                <div class="bookmark-info">
                    <div class="bookmark-title">${bookmark.title}</div>
                    <div class="bookmark-time">${new Date(bookmark.timestamp).toLocaleString()}</div>
                </div>
                <button class="bookmark-delete" data-slide="${bookmark.slide}">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `).join('');
        
        // 绑定点击事件
        listElement.querySelectorAll('.bookmark-item').forEach(item => {
            item.addEventListener('click', (e) => {
                if (!e.target.closest('.bookmark-delete')) {
                    const slide = parseInt(item.dataset.slide);
                    window.presentationApp.goToSlide(slide);
                    document.querySelector('.bookmark-panel').classList.remove('show');
                }
            });
        });
        
        // 绑定删除事件
        listElement.querySelectorAll('.bookmark-delete').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const slide = parseInt(btn.dataset.slide);
                const index = this.bookmarks.findIndex(b => b.slide === slide);
                if (index > -1) {
                    this.bookmarks.splice(index, 1);
                    this.saveBookmarks();
                    this.updateBookmarkList();
                    this.updateBookmarkButton();
                }
            });
        });
    }
}

// Initialize navigation system
document.addEventListener('DOMContentLoaded', () => {
    window.navigationSystem = new NavigationSystem();
});


