
/* 背景装饰系统 - 第5章特定图片 */
.bg-image {
    background-image: url('https://images.unsplash.com/photo-1584622650111-993a426fbf0a?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D');
}

/* 第六章特定样式 - 总结与行动 */
.final-conclusions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.conclusion-insight {
    text-align: center;
    padding: var(--spacing-xl);
    background: var(--light-blue);
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

.key-findings {
    background: var(--background-primary);
    border-radius: 16px;
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
}

.findings-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.findings-header h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.findings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.finding-item {
    padding: var(--spacing-lg);
    border-radius: 12px;
    border: 2px solid;
    transition: all var(--transition-base);
}

.finding-item:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-medium);
}

.finding-item.market {
    background: rgba(0, 166, 81, 0.1);
    border-color: var(--secondary-color);
}

.finding-item.channels {
    background: rgba(0, 115, 230, 0.1);
    border-color: var(--primary-color);
}

.finding-item.competition {
    background: rgba(255, 107, 53, 0.1);
    border-color: var(--accent-color);
}

.finding-item.product {
    background: rgba(128, 0, 128, 0.1);
    border-color: #8000ff;
}

.finding-item.positioning {
    background: rgba(255, 193, 7, 0.1);
    border-color: #ffc107;
}

.finding-content h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-sm) 0;
}

.finding-content p {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin: 0;
    line-height: 1.5;
}

.finding-icon {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    margin: auto;
    margin-bottom: var(--spacing-md);

}

.finding-item.market .finding-icon {
    background: var(--secondary-color);
    color: white;
}

.finding-item.channels .finding-icon {
    background: var(--primary-color);
    color: white;
}

.finding-item.competition .finding-icon {
    background: var(--accent-color);
    color: white;
}

.finding-item.product .finding-icon {
    background: #8000ff;
    color: white;
}

.finding-item.positioning .finding-icon {
    background: #ffc107;
    color: white;
}

.finding-icon i {
    font-size: var(--font-size-xl);
}

/* 成功公式 */
.success-formula {
    background: var(--background-primary);
    border-radius: 16px;
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
}

.formula-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.formula-header h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.formula-equation {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-lg);
    flex-wrap: wrap;
}

.formula-element {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: var(--spacing-lg);
    background: var(--light-blue);
    border-radius: 12px;
    border: 1px solid var(--primary-color);
    min-width: 120px;
}

.formula-operator {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--text-light);
}

.formula-result {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: var(--spacing-xl);
    background: var(--secondary-color);
    color: white;
    border-radius: 12px;
    min-width: 150px;
}


/* 行动计划样式 */
.action-plan {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.plan-insight {
    text-align: center;
    padding: var(--spacing-xl);
    background: var(--light-blue);
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

.ninety-days-plan {
    background: var(--background-primary);
    border-radius: 16px;
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
}

.plan-timeline {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2xl);
    position: relative;
}

.plan-timeline::before {
    content: '';
    position: absolute;
    left: 30px;
    top: 60px;
    bottom: 60px;
    width: 2px;
    background: var(--border-color);
}

.timeline-content {
    flex: 1;
    padding: var(--spacing-lg);
    background: var(--background-secondary);
    border-radius: 12px;
    border-left: 4px solid var(--primary-color);
}

.timeline-content h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-md) 0;
}

.timeline-tasks {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}


/* 风险管理样式 */
.risk-management {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.risk-insight {
    text-align: center;
    padding: var(--spacing-xl);
    background: var(--light-blue);
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

.risk-matrix {
    background: var(--background-primary);
    border-radius: 16px;
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
}


.risk-level {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    min-width: 80px;
}

.level-indicator {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-bottom: var(--spacing-sm);
}

.level-indicator.high {
    background: var(--accent-color);
}

.level-indicator.medium {
    background: #ffc107;
}

.level-indicator.low {
    background: var(--secondary-color);
}

.level-text {
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
}

.risk-item.high .level-text {
    color: var(--accent-color);
}

.risk-item.medium .level-text {
    color: #ffc107;
}

.risk-item.low .level-text {
    color: var(--secondary-color);
}


.risk-description {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
    line-height: 1.5;
}

.risk-mitigation {
    padding: var(--spacing-md);
    background: var(--background-secondary);
    border-radius: 8px;
    border-left: 3px solid var(--primary-color);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    line-height: 1.4;
}

.risk-mitigation strong {
    color: var(--primary-color);
}

.algc{
    align-items: center;
}

/* 应急预案 */
.contingency-plan {
    background: var(--background-primary);
    border-radius: 16px;
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
}

.plan-header {
    text-align: center;
    margin-bottom: var(--spacing-lg);
}

.plan-header h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.plan-content {
    text-align: center;
    padding: var(--spacing-lg);
    background: var(--light-blue);
    border-radius: 8px;
    border: 1px solid var(--primary-color);
}

.plan-content p {
    font-size: var(--font-size-base);
    color: var(--text-primary);
    margin: 0;
    line-height: 1.6;
}

/* 结语样式 */
.closing-statement {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.closing-title {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
    line-height: 1.2;
}

.closing-subtitle {
    font-size: var(--font-size-xl);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-2xl);
    line-height: 1.4;
}

.closing-quote {
    margin: var(--spacing-3xl) 0;
    padding: var(--spacing-2xl);
    background: linear-gradient(135deg, var(--light-blue) 0%, rgba(0, 166, 81, 0.1) 100%);
    border-radius: 16px;
    border: 1px solid var(--border-color);
}

.closing-quote blockquote {
    font-size: var(--font-size-lg);
    font-style: italic;
    color: var(--text-primary);
    margin: 0;
    line-height: 1.6;
    position: relative;
}

.closing-quote blockquote::before {
    content: '"';
    font-size: var(--font-size-4xl);
    color: var(--primary-color);
    position: absolute;
    left: -20px;
    top: -10px;
}

.closing-quote blockquote::after {
    content: '"';
    font-size: var(--font-size-4xl);
    color: var(--primary-color);
    position: absolute;
    right: -20px;
    bottom: -20px;
}

.closing-cta {
    margin-top: var(--spacing-3xl);
}

/* 调研价值样式 */
.research-value {
    background: var(--background-primary);
    border-radius: 16px;
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
    margin: var(--spacing-xl) 0;
}

.value-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.value-header h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.value-points {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg);
}

.value-point {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: var(--background-secondary);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    transition: all var(--transition-base);
}

.value-point:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
    border-color: var(--primary-color);
}

.point-icon {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-color);
    color: white;
    border-radius: 12px;
    flex-shrink: 0;
}

.point-icon i {
    font-size: var(--font-size-xl);
}

.point-content {
    flex: 1;
}

.point-content h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-xs) 0;
    line-height: 1.3;
    text-align: left;
}

.point-content p {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin: 0;
    line-height: 1.5;
    text-align: left;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .value-point {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }
    
    .point-icon {
        width: 50px;
        height: 50px;
    }
    
    .point-icon i {
        font-size: var(--font-size-lg);
    }
}
