# 任务文档：Markdown 与 HTML 语义差异核对

## 任务目标

确保 content/ 目录下的 Markdown 文件（chapter1.md 至 chapter6.md）中的内容要点在对应的 HTML 文件（chapters/chapter1_background.html 至 chapters/chapter6_summary.html）中以语义一致的方式实现。识别并记录任何语义不匹配、缺失或冗余的内容，生成清晰的核对报告。

## 任务概述

### 输入
- **Markdown 文件**：位于 content/ 目录，包含每章的核心内容要点（chapter1.md 至 chapter6.md）
- **HTML 文件**：位于 chapters/ 目录，包含每章的实现内容（chapter1_background.html 至 chapter6_summary.html）

### 输出
- **差异记录文件**：@check_point.md，记录所有语义不匹配、缺失或冗余的内容，注明章节、要点和问题
- **最终报告**：final_report.md，汇总所有章节的核对结果，列出问题点及修复建议

## 任务步骤

### 步骤 1：提取 Markdown 内容要点

**操作：**
- 读取 content/chapterX.md（X 为章节编号 1-6）
- 解析 Markdown 文件，提取核心内容要点（段落、列表、表格等）
- 将要点整理为列表，记录上下文（章节、段落或行号）

**检查点：**
- 确认所有 Markdown 文件可读取且解析无误
- 确保提取的要点完整，覆盖所有关键信息（无遗漏标题、段落或列表项）
- 验证每条要点是否附带明确的上下文（如行号或段落编号）

### 步骤 2：提取 HTML 内容

**操作：**
- 读取 chapters/chapterX_*.html
- 解析 HTML 文件，提取语义相关内容（`<p>`、`<li>`、`<table>` 等中的文本），忽略格式化标签（如 `<style>`、`<div>`）
- 将 HTML 内容整理为可与 Markdown 要点对比的格式

**检查点：**
- 确认 HTML 文件可读取且解析正确
- 确保提取的内容仅包含语义相关部分，无无关样式或格式干扰
- 验证提取的内容是否覆盖 HTML 文件中的所有关键信息

### 步骤 3：语义比对

**操作：**
- 逐条对比 Markdown 的要点与 HTML 的内容，判断语义是否一致：
  - 使用自然语言处理能力识别同义表达，允许措辞差异但需确保语义等价
  - 例如，Markdown 中的"项目包含三个目标：A、B、C"与 HTML 中的"项目目标为 A、B 和 C"视为语义一致
- 检查 Markdown 中的要点是否在 HTML 中有对应实现，记录缺失内容
- 检查 HTML 中是否存在未在 Markdown 中提及的内容，记录冗余内容

**检查点：**
- 确认每条 Markdown 要点均已与 HTML 内容进行语义比对
- 验证语义一致的判断准确（无因措辞差异导致的误判）
- 确保所有缺失或冗余内容被正确识别

### 步骤 4：记录差异

**操作：**
- 在 @check_point.md 中记录比对结果，每条记录包含：
  - 章节编号和标题：如 "Chapter 1: 背景"
  - Markdown 要点：引用具体内容（包括行号或上下文）
  - HTML 实现情况：描述对应内容（或注明缺失/冗余）
  - 问题描述：说明语义不匹配、缺失或冗余的具体问题
  - 建议修复：提出如何调整 HTML 以匹配 Markdown 语义

**检查点：**
- 确认记录格式一致，包含所有必要字段（章节、要点、问题、建议）
- 验证每条记录的可追溯性（引用具体行号或上下文）
- 确保记录清晰，问题描述和建议可操作

### 步骤 5：生成最终报告

**操作：**
- 汇总 @check_point.md 中的所有差异记录
- 创建 final_report.md，包含：
  - **概述**：总结核对任务，统计问题总数
  - **问题列表**：按章节列出所有问题，引用 @check_point.md
  - **优先级分类**：
    - 高优先级：缺失核心要点或语义完全不匹配
    - 中优先级：措辞差异导致潜在歧义
    - 低优先级：轻微措辞差异，语义基本一致
  - **行动计划**：为每个问题提供修复建议，优先解决高优先级问题

**检查点：**
- 确认报告覆盖所有章节的差异，无遗漏
- 验证优先级分类合理，修复建议具体且可行
- 确保报告格式清晰，使用 Markdown 标题、列表或表格便于阅读

## 注意事项

- **语义优先**：只关注语义一致性，允许措辞、句式差异，重点识别同义表达
- **上下文记录**：差异记录需明确 Markdown 行号或段落，以及 HTML 对应内容位置
- **问题分类**：清晰区分缺失、不匹配和冗余问题，确保记录可操作
- **自动化辅助**：建议使用脚本或工具解析 Markdown 和 HTML，提高效率
- **可追溯性**：确保输出文件中的记录可追溯到原始文件，方便验证和修复

## 输出格式示例

### @check_point.md

```markdown
# 语义差异记录

## Chapter 1: 背景
- **要点**：`content/chapter1.md` 第 10 行："项目包含三个核心目标：A、B、C"
  - **HTML 实现**：`chapters/chapter1_background.html`："项目目标为 A 和 B。"
  - **问题**：缺失目标 C 的描述，语义不完整。
  - **建议**：在 HTML 的 `<p>` 标签中添加目标 C 的描述。

## Chapter 2: 战场
- **要点**：`content/chapter2.md` 第 25 行："战斗区域分为四个象限"
  - **HTML 实现**：`chapters/chapter2_battlefield.html`："区域划分为三个部分。"
  - **问题**：区域数量不一致，语义不匹配。
  - **建议**：修改 HTML 为"四个象限"，确保语义一致。
```

### final_report.md

```markdown
# 核对报告

## 概述
核对 6 个章节，发现 8 个问题：高优先级 2 个，中优先级 3 个，低优先级 3 个。

## 问题列表
### Chapter 1: 背景
- 问题 1：缺失目标 C 的描述（高优先级）。
  - 建议：添加目标 C 的描述。
- 问题 2：措辞略有差异但语义一致（低优先级）。
  - 建议：无需调整。

### Chapter 2: 战场
- 问题 1：区域数量不一致（中优先级）。
  - 建议：修改 HTML 为"四个象限"。

## 行动计划
1. **高优先级**：立即修复缺失的核心内容。
2. **中优先级**：调整导致歧义的措辞。
3. **低优先级**：可选择性优化轻微差异。
```

## 工作进度步骤

### Task 1: 准备工作
**目标**：验证文件结构，确保所有待核对文件存在
**执行步骤**：
1. 检查 content/ 目录是否包含 chapter1.md 至 chapter6.md
2. 检查 chapters/ 目录是否包含对应的 HTML 文件
3. 创建 @check_point.md 文件用于记录差异
4. 准备 final_report.md 的基本结构

### Task 2: 提取并整理 Markdown 内容
**目标**：从每个 Markdown 文件提取所有内容要点
**执行步骤**：
1. 逐个读取 content/chapter1.md 至 chapter6.md
2. 解析每个文件的结构（标题、段落、列表、表格等）
3. 记录每个要点的行号和内容
4. 整理成结构化列表，便于后续比对

### Task 3: 提取并整理 HTML 内容
**目标**：从每个 HTML 文件提取语义内容
**执行步骤**：
1. 逐个读取 chapters/ 目录下的 HTML 文件
2. 解析 HTML 结构，提取文本内容（忽略样式和格式标签）
3. 识别段落、列表项、表格等语义元素
4. 整理成与 Markdown 要点可比对的格式

### Task 4: 执行语义比对
**目标**：逐章比对 Markdown 要点与 HTML 内容的语义一致性
**执行步骤**：
1. 对每个章节，逐条比对 Markdown 要点与 HTML 内容
2. 判断语义是否一致（允许措辞差异）
3. 识别缺失的要点（Markdown 有但 HTML 无）
4. 识别冗余的内容（HTML 有但 Markdown 无）
5. 记录所有差异和问题

### Task 5: 生成差异记录
**目标**：在 @check_point.md 中详细记录所有差异
**执行步骤**：
1. 按章节组织差异记录
2. 对每个差异，记录：
   - 具体的 Markdown 要点（含行号）
   - 对应的 HTML 内容（或缺失说明）
   - 问题描述和严重程度
   - 修复建议
3. 确保记录格式统一，便于追溯

### Task 6: 生成最终报告
**目标**：创建 final_report.md 总结所有发现
**执行步骤**：
1. 统计各类问题的数量
2. 按优先级分类问题（高、中、低）
3. 为每个章节总结主要问题
4. 提供具体的行动计划和修复建议
5. 确保报告格式清晰、专业