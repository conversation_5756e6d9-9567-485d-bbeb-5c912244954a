<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第三章：核心玩家竞争地图</title>
    <link rel="stylesheet" href="../assets/css/common.css">
    <link rel="stylesheet" href="../assets/css/slide.css">
    <link rel="stylesheet" href="../assets/css/charts.css">
    <link rel="stylesheet" href="../assets/css/chapter3-enhanced.css">

    <!-- 图表库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.2.0/dist/chartjs-plugin-datalabels.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/d3@7.8.5/dist/d3.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 幻灯片1：第3章首页 -->
    <div id="slide-1" class="slide">
        <div class="slide-content center-content">
            <!-- 背景图片 -->
            <div class="hero-background">
                <div class="bg-image"></div>
                <div class="bg-overlay"></div>
                <div class="bg-pattern animate-fadeIn animate-delay-100"></div>
                <div class="bg-gradient animate-fadeIn animate-delay-200"></div>
            </div>

            <!-- 主标题区域 -->
            <div class="hero-section animate-slideUp animate-delay-300" style="margin-bottom: 40px;">
                <div class="chapter-badge animate-scaleIn animate-delay-400">第三章</div>
                <h1 class="hero-title animate-slideUp animate-delay-500">
                    核心玩家地图
                </h1>
                <p class="hero-subtitle animate-slideUp animate-delay-600">
                    深度解析关键竞争对手的产品功能、营销策略与市场定位
                </p>
                <div class="hero-description animate-slideUp animate-delay-700">
                    <p>通过对TOTO、KOHLER、HOROW等核心玩家的全面分析，揭示各品牌的差异化策略，为Bejoan制定精准的竞争策略和市场定位。</p>
                </div>
            </div>

            <!-- 核心洞察 -->
            <div class="content-block core-insights animate-slideUp animate-delay-1250">
                <h3 class="insights-title">核心洞察</h3>
                <div class="insights-grid">
                    <div class="insight-item animate-scaleIn animate-delay-1300">
                        <div class="insight-number">01</div>
                        <div class="insight-content">
                            <p>三大渠道品牌梯队截然不同，Home Depot是主战场，Lowe's是高端护城河</p>
                        </div>
                    </div>
                    <div class="insight-item animate-scaleIn animate-delay-1350">
                        <div class="insight-number">02</div>
                        <div class="insight-content">
                            <p>新兴品牌靠极致性价比取胜，传统巨头依赖品牌和线下渠道</p>
                        </div>
                    </div>
                    <div class="insight-item animate-scaleIn animate-delay-1400">
                        <div class="insight-number">03</div>
                        <div class="insight-content">
                            <p>Bejoan在智能马桶盖市场表现突出，<strong>需针对智能马桶和马桶盖两条产品线制定<span class="insight-highlight">差异化竞争策略</span></strong></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 章节概要 -->
            <div class="content-block chapter-overview animate-slideUp animate-delay-1300">
                <h3 class="overview-title">本章节内容概要</h3>
                <div class="chapter-grid">
                    <div class="chapter-item">
                        <div class="chapter-number">3.1</div>
                        <div class="chapter-content">
                            <h4>品牌生态金字塔</h4>
                            <p>三大渠道的品牌层级分布与竞争格局分析</p>
                        </div>
                    </div>
                    <div class="chapter-item">
                        <div class="chapter-number">3.2</div>
                        <div class="chapter-content">
                            <h4>核心玩家功能对比</h4>
                            <p>TOTO、KOHLER、HOROW等品牌的功能差异化分析</p>
                        </div>
                    </div>
                    <div class="chapter-item">
                        <div class="chapter-number">3.3</div>
                        <div class="chapter-content">
                            <h4>营销策略研究</h4>
                            <p>各品牌的营销定位、传播策略与用户触达方式</p>
                        </div>
                    </div>
                    <div class="chapter-item">
                        <div class="chapter-number">3.4</div>
                        <div class="chapter-content">
                            <h4>价格策略分析</h4>
                            <p>竞争对手的定价逻辑与价格竞争策略</p>
                        </div>
                    </div>
                    <div class="chapter-item">
                        <div class="chapter-number">3.5</div>
                        <div class="chapter-content">
                            <h4>差异化机会识别</h4>
                            <p>基于竞争分析的Bejoan差异化策略建议</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 装饰元素 -->
            <div class="hero-decorations">
                <div class="decoration-element circle animate-float animate-delay-1500"></div>
                <div class="decoration-element triangle animate-pulse animate-delay-1700"></div>
                <div class="decoration-element square animate-float animate-delay-1900"></div>
            </div>
        </div>
    </div>

    <!-- 幻灯片2：品牌梯队地图 -->
    <div class="slide" id="slide-2">
        <div class="slide-header">
            <h1 class="slide-title">三大渠道的品牌梯队地图揭示了截然不同的"游戏规则"</h1>
            <p class="slide-subtitle">每个渠道都有自己的"王者"和"地头蛇"，我们必须理解并适应它们的生态。</p>
        </div>
        <div class="slide-content">
            <div class="content-block" data-animate="slideUp">
                <div class="brand-ecosystem">
                    <div class="ecosystem-insight">
                        <p class="insight-text">Home Depot是智能马桶的"主战场"，Lowe's是高端智能马桶的"护城河"，而Menards则以智能马桶盖为主导，品牌格局尚不清晰。</p>
                    </div>
                    
                    <div class="brand-pyramids">
                        <div class="pyramid-container" data-animate="cardFloat" data-delay="200">
                            <div class="pyramid-header">
                                <h3>Home Depot</h3>
                                <p class="pyramid-subtitle">智能马桶为主</p>
                            </div>
                            <div class="brand-pyramid">
                                <div class="pyramid-level top" data-animate="slideDown" data-delay="300">
                                    <div class="brand-item premium">
                                        <span class="brand-name">TOTO</span>
                                        <span class="brand-name">KOHLER</span>
                                    </div>
                                </div>
                                <div class="pyramid-level middle" data-animate="slideDown" data-delay="400">
                                    <div class="brand-item mid-tier">
                                        <span class="brand-name">ANZZI</span>
                                        <span class="brand-name">HOROW</span>
                                        <span class="brand-name">WOODBRIDGE</span>
                                    </div>
                                </div>
                                <div class="pyramid-level bottom" data-animate="slideDown" data-delay="500">
                                    <div class="brand-item entry">
                                        <span class="brand-name">Brondell</span>
                                        <span class="brand-name">Bio Bidet</span>
                                        <span class="brand-desc">Bejoan智能马桶盖具备进入潜力</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="pyramid-container" data-animate="cardFloat" data-delay="300">
                            <div class="pyramid-header">
                                <h3>Lowe's</h3>
                                <p class="pyramid-subtitle">智能马桶为主</p>
                            </div>
                            <div class="brand-pyramid">
                                <div class="pyramid-level top" data-animate="slideDown" data-delay="400">
                                    <div class="brand-item premium">
                                        <span class="brand-name">KOHLER</span>
                                        <span class="brand-name">American Standard</span>
                                    </div>
                                </div>
                                <div class="pyramid-level middle" data-animate="slideDown" data-delay="500">
                                    <div class="brand-item mid-tier">
                                        <span class="brand-name">Swiss Madison</span>
                                        <span class="brand-name">传统品牌占据</span>
                                    </div>
                                </div>
                                <div class="pyramid-level bottom" data-animate="slideDown" data-delay="600">
                                    <div class="brand-item entry">
                                        <span class="brand-desc">Bejoan智能马桶盖具备进入潜力</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="pyramid-container" data-animate="cardFloat" data-delay="400">
                            <div class="pyramid-header">
                                <h3>Menards</h3>
                                <p class="pyramid-subtitle">智能马桶盖为主</p>
                            </div>
                            <div class="brand-pyramid">
                                <div class="pyramid-level top" data-animate="slideDown" data-delay="500">
                                    <div class="brand-item premium">
                                        <span class="brand-desc">Bejoan智能马桶盖具备成为领导者的巨大潜力</span>
                                    </div>
                                </div>
                                <div class="pyramid-level middle" data-animate="slideDown" data-delay="600">
                                    <div class="brand-item mid-tier">
                                        <span class="brand-name">其他品牌</span>
                                    </div>
                                </div>
                                <div class="pyramid-level bottom" data-animate="slideDown" data-delay="700">
                                    <div class="brand-item entry">
                                        <span class="brand-desc">智能马桶品牌稀少，结构扁平</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="content-block" data-animate="fadeIn" data-delay="800">
                <div class="ecosystem-conclusion">
                    <p>这三张地图告诉我们，在不同渠道需要与不同类型的产品和对手竞争，不能一概而论。<strong>Bejoan作为新进入者，需精准定位其智能马桶和马桶盖产品线的市场机会。</strong></p>
                </div>
            </div>
            <div class="content-block" data-animate="fadeIn" data-delay="1000">
                <div class="data-source">
                    <p><strong>数据来源：</strong>基于三大平台318款产品的品牌分布、SKU数量、总评价数分析（不含Bejoan产品数据）；区分智能马桶和马桶盖产品类型；Bejoan产品数据来自亚马逊美国平台。</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 幻灯片3：Home Depot详细分析 -->
    <div class="slide" id="slide-3" style="display: none;">
        <div class="slide-header">
            <h1 class="slide-title">Home Depot：智能马桶为主导，TOTO与KOHLER双强格局，价格覆盖$168-$5,646</h1>
            <p class="slide-subtitle">基于248款Home Depot产品深度分析+1.28万+用户评价，揭示一体式智能马桶平均评分4.4分，马桶盖4.4分。</p>
        </div>
        <div class="slide-content">
            <div class="content-block" data-animate="slideUp">
                <div class="platform-analysis">
                    <div class="analysis-grid" style="grid-template-columns: repeat(4, 1fr);">
                        <div class="analysis-card" data-animate="cardFloat" data-delay="100">
                            <div class="card-header">
                                <h3>产品类型分布</h3>
                            </div>
                            <div class="card-content">
                                <div id="productTypeChartContainer" style="height: 320px;"></div>
                            </div>
                        </div>
                        
                        <div class="analysis-card" data-animate="cardFloat" data-delay="200">
                            <div class="card-header">
                                <h3>品牌市场占有率</h3>
                                <p class="card-subtitle">按产品数量</p>
                            </div>
                            <div class="card-content">
                                <div class="brand-ranking">
                                    <div class="ranking-item">
                                        <span class="rank">1</span>
                                        <span class="brand">HOROW</span>
                                        <span class="percentage">6.5%</span>
                                    </div>
                                    <div class="ranking-item">
                                        <span class="rank">2</span>
                                        <span class="brand">WOODBRIDGE</span>
                                        <span class="percentage">5.6%</span>
                                    </div>
                                    <div class="ranking-item">
                                        <span class="rank">3</span>
                                        <span class="brand">ANZZI</span>
                                        <span class="percentage">4.4%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="analysis-card" data-animate="cardFloat" data-delay="300">
                            <div class="card-header">
                                <h3>用户评分分析</h3>
                            </div>
                            <div class="card-content">
                                <div class="rating-stats">
                                    <div class="stat-item">
                                        <div class="stat-label">智能马桶平均评分</div>
                                        <div class="stat-value">
                                            <span class="number-counter" data-target="4.4">4.4</span>
                                            <span class="unit">分</span>
                                        </div>
                                        <div class="stat-detail">10,308条评论</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-label">智能马桶盖平均评分</div>
                                        <div class="stat-value">
                                            <span class="number-counter" data-target="4.4">4.4</span>
                                            <span class="unit">分</span>
                                        </div>
                                        <div class="stat-detail">2,425条评论</div>
                                    </div>
                                </div>
                                <div class="complaints-analysis">
                                    <h4>主要抱怨点</h4>
                                    <div class="complaint-item">
                                        <span class="complaint-text">除臭功能缺失</span>
                                        <span class="complaint-percent">38%提及</span>
                                    </div>
                                    <div class="complaint-item">
                                        <span class="complaint-text">安装复杂</span>
                                        <span class="complaint-percent">25%提及</span>
                                    </div>
                                    <div class="complaint-item">
                                        <span class="complaint-text">水压不足</span>
                                        <span class="complaint-percent">15%提及</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="analysis-card" data-animate="cardFloat" data-delay="400">
                            <div class="card-header">
                                <h3>价格区间分布</h3>
                                <p class="card-subtitle">智能马桶</p>
                            </div>
                            <div class="card-content">
                                <div id="priceRangeChartContainer" style="height: 400px;"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="brand-performance-section" data-animate="slideUp" data-delay="500">
                        <h3>主要品牌表现对比</h3>
                        <p class="section-subtitle">不含Bejoan，因其为新进入者</p>
                        <div class="brand-comparison-grid">
                            <div class="brand-card">
                                <h4>TOTO</h4>
                                <div class="brand-metrics">
                                    <div class="metric">市场份额领先</div>
                                    <div class="metric">平均价格: $2,200</div>
                                    <div class="metric">用户评分: 4.5分</div>
                                    <div class="metric highlight">优势：技术领先</div>
                                </div>
                            </div>
                            <div class="brand-card">
                                <h4>KOHLER</h4>
                                <div class="brand-metrics">
                                    <div class="metric">市场份额靠前</div>
                                    <div class="metric">平均价格: $1,800</div>
                                    <div class="metric">用户评分: 4.2分</div>
                                    <div class="metric highlight">优势：品牌知名度</div>
                                </div>
                            </div>
                            <div class="brand-card">
                                <h4>ANZZI</h4>
                                <div class="brand-metrics">
                                    <div class="metric">性价比品牌</div>
                                    <div class="metric">平均价格: $1,200</div>
                                    <div class="metric">用户评分: 4.1分</div>
                                    <div class="metric highlight">优势：性价比</div>
                                </div>
                            </div>
                            <div class="brand-card">
                                <h4>HOROW</h4>
                                <div class="brand-metrics">
                                    <div class="metric">性价比品牌</div>
                                    <div class="metric">平均价格: $632</div>
                                    <div class="metric">用户评分: 4.5分</div>
                                    <div class="metric highlight">优势：高销量高评分</div>
                                </div>
                            </div>
                        </div>
                        <div class="bejoan-reference">
                            <h4>Bejoan在亚马逊平台表现 (供参考)</h4>
                            <p><strong>Bejoan(马桶盖):</strong> 平均价格$356，用户评分4.2分，优势：高销量高评分</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="content-block" data-animate="fadeIn" data-delay="600">
                <div class="platform-conclusion">
                    <p><strong>核心观点：</strong>Home Depot平台以智能马桶为主，呈现明显的品牌分化格局。TOTO凭借技术优势占据高端市场，中低端市场竞争激烈，为新品牌提供了差异化机会。<strong>Bejoan在亚马逊智能马桶盖市场表现突出，可作为其进入Home Depot智能马桶盖市场的切入点，并逐步拓展智能马桶产品线。</strong></p>
                </div>
            </div>
            <div class="content-block" data-animate="fadeIn" data-delay="800">
                <div class="data-source">
                    <p><strong>数据来源：</strong>基于Home Depot 248款产品完整数据分析（不含Bejoan产品数据）；用户评论基于12816条真实评价的文本挖掘；Bejoan产品数据来自亚马逊美国平台。</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 幻灯片4：HOROW/ANZZI竞争对手分析 -->
    <div class="slide" id="slide-4" style="display: none;">
        <div class="slide-header">
            <h1 class="slide-title">在Home Depot中端市场，我们的核心对手是HOROW和ANZZI，而非TOTO；Bejoan在马桶盖市场表现突出</h1>
            <p class="slide-subtitle">我们必须精准定位对手，避免将资源浪费在与我们不直接竞争的品牌上。</p>
        </div>
        <div class="slide-content">
            <div class="content-block" data-animate="slideUp">
                <div class="competitor-analysis">
                    <div class="analysis-insight">
                        <p class="insight-text">尽管TOTO和KOHLER是市场领导者，但在我们主攻的Home Depot中端价格带（智能马桶），真正的威胁来自HOROW T3和ANZZI Aura这两款"爆品"。同时，Bejoan在智能马桶盖市场拥有高销量高评分产品，具备切入优势。</p>
                    </div>

                    <div class="chart-container" data-animate="cardFloat" data-delay="200">
                        <div class="chart-header">
                            <h3 class="chart-title">竞争定位图</h3>
                            <p class="chart-subtitle">价格 vs 用户评分（气泡大小代表评价数量）</p>
                        </div>

                        <div id="competitorChart" style="height: 400px; margin: 20px 0;"></div>

                        <div class="competitor-insights">
                            <div class="insight-grid">
                                <div class="insight-card" style="background-color: #f7fafc;">
                                    <h4>智能马桶市场</h4>
                                    <div class="competitor-list">
                                        <div class="competitor-item">
                                            <span class="name">HOROW T3</span>
                                            <span class="price">$632</span>
                                            <span class="rating">4.5分</span>
                                        </div>
                                        <div class="competitor-item">
                                            <span class="name">ANZZI Aura</span>
                                            <span class="price">$1,200</span>
                                            <span class="rating">4.1分</span>
                                        </div>
                                        <div class="competitor-item">
                                            <span class="name">TOTO同价位产品</span>
                                            <span class="price">$2,200+</span>
                                            <span class="rating">4.5分</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="insight-card" style="background-color: #f7fafc;">
                                    <h4>智能马桶盖市场</h4>
                                    <div class="competitor-list">
                                        <div class="competitor-item highlight">
                                            <span class="name">Bejoan X1 (亚马逊)</span>
                                            <span class="price">$356</span>
                                            <span class="rating">4.2分</span>
                                        </div>
                                        <div class="competitor-item">
                                            <span class="name">其他品牌马桶盖</span>
                                            <span class="price">$200-500</span>
                                            <span class="rating">4.0-4.4分</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="strategic-implication" data-animate="slideUp" data-delay="400">
                        <p><strong>图表解释：</strong>Bejoan在亚马逊智能马桶盖市场具备竞争力，而在Home Depot智能马桶中端市场，需要针对性地与HOROW和ANZZI等性价比品牌竞争，并寻找智能马桶盖的进入机会。</p>
                    </div>
                </div>
            </div>
            <div class="content-block" data-animate="fadeIn" data-delay="600">
                <div class="data-source">
                    <p><strong>数据来源：</strong>基于Home Depot竞品数据库中价格、评分、评论数量的综合分析（不含Bejoan产品数据）；Bejoan X1产品数据来自亚马逊美国平台。</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 幻灯片5：Lowe's详细分析 -->
    <div class="slide" id="slide-5" style="display: none;">
        <div class="slide-header">
            <h1 class="slide-title">Lowe's：智能马桶为主，品牌集中度更高，KOHLER领先优势明显</h1>
            <p class="slide-subtitle">相比Home Depot，Lowe's平台品牌更集中，头部效应更显著。</p>
        </div>
        <div class="slide-content">
            <div class="content-block" data-animate="slideUp">
                <div class="platform-comparison">
                    <div class="comparison-grid">
                        <div class="comparison-card" data-animate="cardFloat" data-delay="100">
                            <div class="card-header">
                                <h3>平台对比分析</h3>
                                <p class="card-subtitle">Home Depot vs Lowe's</p>
                            </div>
                            <div class="card-content">
                                <div class="comparison-item">
                                    <span class="metric-label">产品数量</span>
                                    <div class="metric-comparison">
                                        <span class="metric-value hd">HD: 248款</span>
                                        <span class="metric-value lowes">Lowe's: 105款</span>
                                    </div>
                                </div>
                                <div class="comparison-item">
                                    <span class="metric-label">价格区间</span>
                                    <div class="metric-comparison">
                                        <span class="metric-value hd">HD: $168-$5,646</span>
                                        <span class="metric-value lowes">Lowe's: $109-$7,999</span>
                                    </div>
                                </div>
                                <div class="comparison-item">
                                    <span class="metric-label">平均评分</span>
                                    <div class="metric-comparison">
                                        <span class="metric-value hd">HD: 4.4分</span>
                                        <span class="metric-value lowes">Lowe's: 智能马桶4.5分，马桶盖4.4分</span>
                                    </div>
                                </div>
                                <div class="comparison-item">
                                    <span class="metric-label">品牌集中度</span>
                                    <div class="metric-comparison">
                                        <span class="metric-value hd">HD: 分散</span>
                                        <span class="metric-value lowes">Lowe's: 集中</span>
                                    </div>
                                </div>
                                <div class="comparison-item">
                                    <span class="metric-label">主导品牌</span>
                                    <div class="metric-comparison">
                                        <span class="metric-value hd">HD: HOROW, ANZZI</span>
                                        <span class="metric-value lowes">Lowe's: KOHLER, Swiss Madison</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="comparison-card" data-animate="cardFloat" data-delay="200">
                            <div class="card-header">
                                <h3>Lowe's产品类型分布</h3>
                            </div>
                            <div class="card-content">
                                <div id="lowesProductTypeChartContainer" style="height: 400px;"></div>
                            </div>
                        </div>
                    </div>

                    <div class="function-gap-analysis" data-animate="slideUp" data-delay="300">
                        <div class="gap-header">
                            <h3>功能缺失机会分析</h3>
                        </div>
                        <div class="gap-content">
                            <div class="gap-item">
                                <div class="gap-icon">
                                    <i class="fas fa-wind"></i>
                                </div>
                                <div class="gap-info">
                                    <h4>除臭功能普及率低</h4>
                                    <p>仅25.7%产品配备除臭功能(Lowe's整体)，但高销量高评分产品中43.8%配备</p>
                                </div>
                            </div>
                            <div class="gap-item">
                                <div class="gap-icon">
                                    <i class="fas fa-spa"></i>
                                </div>
                                <div class="gap-info">
                                    <h4>按摩清洗技术差异化</h4>
                                    <p>仅23.8%产品配备按摩清洗功能(Lowe's整体)，但高销量高评分产品中37.5%配备</p>
                                </div>
                            </div>
                            <div class="gap-item">
                                <div class="gap-icon">
                                    <i class="fas fa-mobile-alt"></i>
                                </div>
                                <div class="gap-info">
                                    <h4>智能控制普及度</h4>
                                    <p>缺乏APP控制功能，无语音控制集成</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="competitive-positioning" data-animate="slideUp" data-delay="400">
                        <div class="positioning-header">
                            <h3>竞争定位地图(Lowe's)</h3>
                            <p class="positioning-subtitle">不含Bejoan，因其为新进入者</p>
                        </div>
                        <div class="positioning-matrix">
                            <div class="matrix-quadrant high-price-high-function">
                                <div class="quadrant-label">高价格/高功能</div>
                                <div class="quadrant-content">
                                    <div class="brand-position">KOHLER(技术领导者)</div>
                                </div>
                            </div>
                            <div class="matrix-quadrant low-price-high-function">
                                <div class="quadrant-label">低价格/高功能</div>
                                <div class="quadrant-content">
                                    <div class="opportunity-position">Bejoan潜在定位(智能马桶盖)</div>
                                </div>
                            </div>
                            <div class="matrix-quadrant high-price-low-function">
                                <div class="quadrant-label">高价格/基础功能</div>
                                <div class="quadrant-content">
                                    <div class="brand-position">KOHLER(品牌溢价)</div>
                                </div>
                            </div>
                            <div class="matrix-quadrant low-price-low-function">
                                <div class="quadrant-label">低价格/基础功能</div>
                                <div class="quadrant-content">
                                    <div class="brand-position">Swiss Madison, Woodbridge(价格竞争)</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="content-block" data-animate="fadeIn" data-delay="600">
                <div class="platform-conclusion">
                    <p><strong>核心观点：</strong>Lowe's平台以智能马桶为主，品牌集中度高，但存在明显的功能缺失机会。除臭功能、按摩清洗技术的普及率低，为Bejoan提供了差异化切入点，尤其在智能马桶盖市场Bejoan有<strong>巨大进入潜力</strong>。</p>
                </div>
            </div>
            <div class="content-block" data-animate="fadeIn" data-delay="600">
                <div class="data-source">
                    <p><strong>数据来源：</strong>基于Lowe's 105款产品的功能特性、价格、评分综合分析（不含Bejoan产品数据）；功能普及率基于产品特性标签统计；Bejoan产品数据来自亚马逊美国平台。</p>
                </div>
            </div>
        </div>
    </div>

    

    <!-- 幻灯片6：品牌策略对比 -->
    <div class="slide" id="slide-6" style="display: none;">
        <div class="slide-header">
            <h1 class="slide-title">新兴品牌靠极致性价比取胜，而传统巨头依赖品牌和线下渠道</h1>
            <p class="slide-subtitle">我们的两类主要对手，其打法和优势完全不同。</p>
        </div>
        <div class="slide-content">
            <div class="content-block" data-animate="slideUp">
                <div class="brand-strategy-comparison">
                    <div class="comparison-insight">
                        <p class="insight-text">我们需要学习新兴品牌的线上运营效率，同时也要应对传统巨头的品牌压制。</p>
                    </div>

                    <div class="strategy-comparison-table" data-animate="slideUp" data-delay="200">
                        <div class="comparison-header">
                            <h3>两类对手策略对比</h3>
                        </div>
                        <div class="comparison-columns">
                            <div class="strategy-column emerging-brands">
                                <h4>新兴品牌</h4>
                                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px;">
                                    <div class="strategy-section">
                                        <h5>策略</h5>
                                        <ul>
                                            <li>线上单点突破</li>
                                            <li>极致性价比</li>
                                            <li>功能堆料</li>
                                        </ul>
                                    </div>
                                    <div class="strategy-section">
                                        <h5>优势</h5>
                                        <ul>
                                            <li>运营效率高</li>
                                            <li>决策快</li>
                                        </ul>
                                    </div>
                                    <div class="strategy-section">
                                        <h5>劣势</h5>
                                        <ul>
                                            <li>品牌力弱</li>
                                            <li>线下无根基</li>
                                        </ul>
                                    </div>
                                </div>
                                
                            </div>
                            <div class="strategy-column traditional-giants">
                                <h4>传统巨头</h4>
                                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px;">
                                    <div class="strategy-section">
                                        <h5>策略</h5>
                                        <ul>
                                            <li>全渠道布局</li>
                                            <li>品牌溢价</li>
                                            <li>设计驱动</li>
                                        </ul>
                                    </div>
                                    <div class="strategy-section">
                                        <h5>优势</h5>
                                        <ul>
                                            <li>品牌力强</li>
                                            <li>渠道深厚</li>
                                        </ul>
                                    </div>
                                    <div class="strategy-section">
                                        <h5>劣势</h5>
                                        <ul>
                                            <li>线上反应慢</li>
                                            <li>性价比低</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="case-studies" data-animate="slideUp" data-delay="400">
                        <div class="case-header">
                            <h3>具体案例分析</h3>
                        </div>
                        <div class="case-grid">
                            <div class="case-section">
                                <h4>新兴品牌代表</h4>
                                <div class="brand-case">
                                    <div class="brand-item">
                                        <span class="brand-name">HOROW</span>
                                        <span class="brand-detail">平均价格$632, 高销量</span>
                                    </div>
                                    <div class="brand-item">
                                        <span class="brand-name">ANZZI</span>
                                        <span class="brand-detail">技术创新导向</span>
                                    </div>
                                    <div class="brand-item">
                                        <span class="brand-name">Brondell</span>
                                        <span class="brand-detail">线上营销专家</span>
                                    </div>
                                </div>
                            </div>
                            <div class="case-section">
                                <h4>传统巨头代表</h4>
                                <div class="brand-case">
                                    <div class="brand-item">
                                        <span class="brand-name">TOTO</span>
                                        <span class="brand-detail">技术领导者</span>
                                    </div>
                                    <div class="brand-item">
                                        <span class="brand-name">KOHLER</span>
                                        <span class="brand-detail">品牌溢价</span>
                                    </div>
                                    <div class="brand-item">
                                        <span class="brand-name">American Standard</span>
                                        <span class="brand-detail">渠道优势</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="bejoan-positioning">
                            <h4>Bejoan定位</h4>
                            <p>介于新兴品牌与传统巨头之间，具备新兴品牌的灵活性和性价比优势，同时在智能马桶盖领域有独特技术（按摩）和市场基础（亚马逊）。</p>
                        </div>
                    </div>

                    <div class="strategy-conclusion" data-animate="slideUp" data-delay="600">
                        <p><strong>图表解释：</strong>这个对比清晰地展示了Bejoan所处的竞争环境，我们需要制定一个能"兼顾两端"的混合策略，并充分利用其在智能马桶盖领域的既有优势。</p>
                    </div>
                </div>
            </div>
            <div class="content-block" data-animate="fadeIn" data-delay="600">
                <div class="data-source">
                    <p><strong>数据来源：</strong>基于各品牌产品线、定价策略及营销活动的综合分析；结合Arizton 2024年智能马桶市场竞争格局报告；Bejoan产品数据来自亚马逊美国平台。</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 幻灯片7：Bejoan SWOT分析 -->
    <div class="slide" id="slide-7" style="display: none;">
        <div class="slide-header">
            <h1 class="slide-title">Bejoan北美市场SWOT分析</h1>
            <p class="slide-subtitle">基于竞争环境分析，识别Bejoan的竞争优势与改进空间。</p>
        </div>
        <div class="slide-content">
            <div class="content-block" data-animate="slideUp">
                <div class="swot-analysis">
                    <div class="swot-grid">
                        <div class="swot-quadrant strengths">
                            <h3><i class="fas fa-plus-circle"></i> 优势 (Strengths)</h3>
                            <ul>
                                <li><strong>智能马桶盖产品线丰富，在亚马逊平台有高销量高评分产品(如X1 Electric Bidet Seat)，具备进入新渠道的良好基础。</strong></li>
                                <li>按摩清洗技术独特(Bejoan产品100%配备 vs 竞品24%)，且用户满意度高。</li>
                                <li>中端价位优势($744中位数)，具备性价比。</li>
                            </ul>
                        </div>
                        <div class="swot-quadrant weaknesses">
                            <h3><i class="fas fa-minus-circle"></i> 劣势 (Weaknesses)</h3>
                            <ul>
                                <li><strong>在Home Depot、Lowe's、Menards等主流渠道尚无销售记录，品牌知名度为零。</strong></li>
                                <li><strong>智能马桶产品线相对较少，且缺乏除臭功能(Bejoan产品0%配备 vs 竞品81%)，是用户普遍期望的功能。</strong></li>
                                <li>智能化程度不足，缺乏APP控制和语音控制集成。</li>
                            </ul>
                        </div>
                        <div class="swot-quadrant opportunities">
                            <h3><i class="fas fa-star"></i> 机会 (Opportunities)</h3>
                            <ul>
                                <li>智能马桶市场快速增长(11.6% CAGR)，渗透率仍有巨大提升空间。</li>
                                <li>功能缺口明显(除臭/按摩普及率低)，为Bejoan提供差异化切入点，尤其是在智能马桶产品线。</li>
                                <li>线上线下融合趋势为新品牌提供更多触达用户的机会。</li>
                            </ul>
                        </div>
                        <div class="swot-quadrant threats">
                            <h3><i class="fas fa-exclamation-triangle"></i> 威胁 (Threats)</h3>
                            <ul>
                                <li>强势品牌竞争(TOTO/KOHLER在智能马桶市场占主导地位)。</li>
                                <li>价格竞争激烈，中端市场红海。</li>
                                <li>技术迭代风险，需要持续研发投入。</li>
                                <li>市场准入壁垒，包括法规合规和渠道门槛。</li>
                            </ul>
                        </div>
                    </div>
                    </div>

                    <div class="swot-strategies" data-animate="slideUp" data-delay="300">
                        <h3>SWOT分析战略启示</h3>
                        <div class="strategy-grid">
                            <div class="strategy-item so-strategy" style="flex-direction: column;">
                                <h4>SO策略</h4>
                                <p>利用亚马逊平台积累的智能马桶盖优势和按摩清洗技术，抢占新渠道的智能马桶盖市场，并以此为基础拓展智能马桶产品线。</p>
                            </div>
                            <div class="strategy-item wo-strategy" style="flex-direction: column;">
                                <h4>WO策略</h4>
                                <p>优先补齐智能马桶的除臭功能，提升产品竞争力；加强智能化功能开发。</p>
                            </div>
                            <div class="strategy-item st-strategy" style="flex-direction: column;">
                                <h4>ST策略</h4>
                                <p>强化智能马桶和马桶盖的技术差异化（按摩+除臭），避免同质化竞争；提升性价比优势，应对价格竞争。</p>
                            </div>
                            <div class="strategy-item wt-strategy" style="flex-direction: column;">
                                <h4>WT策略</h4>
                                <p>寻找细分市场机会（如Menards的智能马桶蓝海），避开正面竞争；加大品牌投入，提升新渠道市场认知度。</p>
                            </div>
                        </div>
                    </div>

                    <div class="swot-conclusion" data-animate="fadeIn" data-delay="500">
                        <p><strong>核心观点：</strong>Bejoan具备技术差异化优势，尤其在智能马桶盖市场有良好基础，但作为新进入者，需要在智能马桶产品线、品牌建设、功能完善和渠道拓展方面加大投入，以实现新渠道的突破。</p>
                    </div>
                </div>
            </div>
            <div class="content-block" data-animate="fadeIn" data-delay="600">
                <div class="data-source">
                    <p><strong>数据来源：</strong>基于三大平台318款竞品功能对比分析；Bejoan产品特性基于亚马逊美国平台产品规格书；市场增长率来源KBV Research 2024年报告。</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 幻灯片导航逻辑
        let currentSlide = 1;
        const totalSlides = 7;

        function showSlide(slideNumber) {
            // 隐藏所有幻灯片
            document.querySelectorAll('.slide').forEach(slide => {
                slide.style.display = 'none';
            });

            // 显示指定幻灯片
            const targetSlide = document.getElementById(`slide-${slideNumber}`);
            if (targetSlide) {
                targetSlide.style.display = 'block';
                currentSlide = slideNumber;

                // 触发动画
                setTimeout(() => {
                    triggerAnimations(targetSlide);
                }, 100);

                // 如果是 slide-3，初始化饼图
                if (slideNumber === 3) {
                    setTimeout(() => {
                        if (window.chartManager) {
                            window.chartManager.createProductTypePieChart('productTypeChartContainer', {
                                labels: ['智能马桶', '智能马桶盖'],
                                values: [92.3, 7.6]
                            });
                            window.chartManager.createPriceRangePieChart('priceRangeChartContainer', {
                                labels: ['经济型 (<$800)', '中端型 ($801-$1500)', '高端型 (>$1501)'],
                                values: [36.4, 50.5, 10.9]
                            });
                        }
                    }, 200);
                }

                // 如果是 slide-5，初始化饼图
                if (slideNumber === 5) {
                    setTimeout(() => {
                        if (window.chartManager) {
                            window.chartManager.createProductTypePieChart('lowesProductTypeChartContainer', {
                                labels: ['智能马桶', '智能马桶盖'],
                                values: [81.0, 19.0]
                            });
                        }
                    }, 200);
                }

                // 如果是 slide-4，初始化竞争对手图表
                if (slideNumber === 4) {
                    setTimeout(() => {
                        initCompetitorChart();
                    }, 1000);
                }
            }
        }

        function nextSlide() {
            if (currentSlide < totalSlides) {
                showSlide(currentSlide + 1);
            }
        }

        function previousSlide() {
            if (currentSlide > 1) {
                showSlide(currentSlide - 1);
            }
        }

        function triggerAnimations(slideElement) {
            const animatedElements = slideElement.querySelectorAll('[data-animate]');
            animatedElements.forEach((element, index) => {
                const animationType = element.dataset.animate;
                const delay = element.dataset.delay || (index * 200);

                setTimeout(() => {
                    element.classList.add('animate-' + animationType);
                    
                    // 特殊处理数字动画
                    if (element.classList.contains('number-counter')) {
                        animateNumber(element);
                    }
                }, delay);
            });
        }

        function animateNumber(element) {
            const target = parseFloat(element.dataset.target);
            const duration = 2000;
            const start = 0;
            const startTime = performance.now();
            
            function update(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);
                const current = start + (target - start) * easeOutQuart(progress);
                element.textContent = current.toFixed(target % 1 === 0 ? 0 : 1);
                
                if (progress < 1) {
                    requestAnimationFrame(update);
                }
            }
            
            requestAnimationFrame(update);
        }
        
        function easeOutQuart(t) {
            return 1 - Math.pow(1 - t, 4);
        }

        // 监听URL hash变化
        function handleHashChange() {
            const hash = window.location.hash;
            const match = hash.match(/#slide-(\d+)/);
            if (match) {
                const slideNumber = parseInt(match[1]);
                if (slideNumber >= 1 && slideNumber <= totalSlides) {
                    showSlide(slideNumber);
                }
            }
        }

        // 初始化竞争对手图表
        function initCompetitorChart() {
            const chartContainer = document.getElementById('competitorChart');
            if (!chartContainer) return;

            chartContainer.innerHTML = '';
            const canvas = document.createElement('canvas');
            canvas.style.maxHeight = '400px';
            chartContainer.appendChild(canvas);

            const ctx = canvas.getContext('2d');

            const chartConfig = {
                type: 'bubble',
                data: {
                    datasets: [
                        {
                            label: '智能马桶',
                            data: [
                                { x: 632, y: 4.5, r: 15 }, // HOROW T3
                                { x: 1200, y: 4.1, r: 12 }, // ANZZI Aura
                                { x: 2200, y: 4.5, r: 10 }, // TOTO
                            ],
                            backgroundColor: 'rgba(0, 115, 230, 0.6)',
                            borderColor: '#0073e6'
                        },
                        {
                            label: '智能马桶盖',
                            data: [
                                { x: 356, y: 4.2, r: 18 }, // Bejoan X1
                                { x: 350, y: 4.0, r: 10 }, // 其他品牌
                            ],
                            backgroundColor: 'rgba(0, 166, 81, 0.6)',
                            borderColor: '#00a651'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: '价格 ($)'
                            },
                            min: 0,
                            max: 2500
                        },
                        y: {
                            title: {
                                display: true,
                                text: '用户评分'
                            },
                            min: 3.5,
                            max: 5
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return `价格: $${context.raw.x}, 评分: ${context.raw.y}分`;
                                }
                            }
                        }
                    }
                }
            };

            new Chart(ctx, chartConfig);
        }

        // 键盘导航
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'ArrowRight':
                case ' ':
                    e.preventDefault();
                    nextSlide();
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    previousSlide();
                    break;
            }
        });

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            // 监听URL hash变化
            window.addEventListener('load', () => {
                handleHashChange();
                if (!window.location.hash) {
                    showSlide(1);
                }
            });
            
            window.addEventListener('hashchange', handleHashChange);
        });

        // 导出函数供父页面调用
        window.triggerAnimations = () => {
            const currentSlideElement = document.getElementById(`slide-${currentSlide}`);
            if (currentSlideElement) {
                triggerAnimations(currentSlideElement);
            }
        };
    </script>

    <script src="../assets/js/charts.js"></script>
    <script src="../assets/js/navigation.js"></script>
</body>
</html>
