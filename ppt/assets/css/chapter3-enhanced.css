/* ===================================
   第3章增强样式 - 核心玩家地图
   ================================== */

/* === 第3章首页样式 === */

/* 背景装饰系统 - 第3章特定图片 */
.bg-image {
    background-image: url('https://images.unsplash.com/photo-1695002817411-203c7f19dfa3?q=80&w=2532&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D');
}

/* 第3章特定装饰元素 */
.decoration-element.circle {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    top: 10%;
    right: 10%;
}

.decoration-element.triangle {
    width: 0;
    height: 0;
    border-left: 40px solid transparent;
    border-right: 40px solid transparent;
    border-bottom: 70px solid var(--accent-color);
    bottom: 20%;
    left: 15%;
}

.decoration-element.square {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    transform: rotate(45deg);
    top: 60%;
    right: 20%;
}

.opportunity-content{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-sm);
}

/* ===================================
   矩阵表格样式系统
   ================================== */

/* 矩阵表格容器 */
.matrix-table {
    width: 100%;
    max-width: 100%;
    margin: var(--spacing-lg) 0;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    background: #ffffff;
    border: 1px solid #e1e8ed;
}

/* 矩阵行样式 */
.matrix-row {
    display: grid;
    grid-template-columns: 2fr 1.2fr 1.5fr 2fr;
    gap: 0;
    transition: all var(--transition-base);
    position: relative;
}

.matrix-row:not(.header):hover {
    background: #f8f9fa;
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* 矩阵单元格基础样式 */
.matrix-cell {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: 1rem;
    line-height: 1.5;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    /* border-right: 1px solid #d1d9e0; */
    transition: all var(--transition-base);
    position: relative;
    color: #2c3e50;
}

.matrix-cell:last-child {
    border-right: none;
}

/* 表头样式 */
.matrix-row.header {
    color: var(--text-primary);
    font-weight: 700;
    font-size: 1.1rem;
    position: sticky;
    top: 0;
    z-index: 10;
}

.matrix-row.header .matrix-cell {
    color: var(--text-primary);
    text-align: center;
    justify-content: center;
    /* border-right: 1px solid #d1d9e0; */
}

/* 优势行样式 */
.matrix-row.advantage {
    background: linear-gradient(90deg, rgba(39, 174, 96, 0.15), rgba(39, 174, 96, 0.05));
    border-left: 4px solid #27ae60;
    color: #2c3e50;
}

.matrix-row.advantage:hover {
    background: linear-gradient(90deg, rgba(39, 174, 96, 0.25), rgba(39, 174, 96, 0.08));
}

/* 劣势行样式 */
.matrix-row.weakness {
    background: linear-gradient(90deg, rgba(231, 76, 60, 0.15), rgba(231, 76, 60, 0.05));
    border-left: 4px solid #e74c3c;
    color: #2c3e50;
}

.matrix-row.weakness:hover {
    background: linear-gradient(90deg, rgba(231, 76, 60, 0.25), rgba(231, 76, 60, 0.08));
}

/* 标准行样式 */
.matrix-row.standard {
    background: linear-gradient(90deg, rgba(52, 152, 219, 0.12), rgba(52, 152, 219, 0.03));
    border-left: 4px solid #3498db;
    color: #2c3e50;
}

.matrix-row.standard:hover {
    background: linear-gradient(90deg, rgba(52, 152, 219, 0.2), rgba(52, 152, 219, 0.06));
}

/* 特殊单元格样式 */


.matrix-cell.negative {
    color: #bd2130;
    font-weight: 700;
    position: relative;
    background: rgba(231, 76, 60, 0.1);
    border-radius: 4px;
    margin: 1px;
}

.matrix-cell.negative::before {
    content: '↘';
    position: absolute;
    left: 8px;
    color: #bd2130;
    font-size: 1.3rem;
    font-weight: bold;
}

.matrix-cell.negative {
    padding-left: 32px;
}

.matrix-cell.neutral {
    color: #1f5582;
    font-weight: 600;
    position: relative;
    background: rgba(52, 152, 219, 0.08);
    border-radius: 4px;
    margin: 1px;
}

.matrix-cell.neutral::before {
    content: '→';
    position: absolute;
    left: 8px;
    color: #1f5582;
    font-size: 1.3rem;
    font-weight: bold;
}

.matrix-cell.neutral {
    padding-left: 32px;
}

/* 响应式设计 */
@media (max-width: 992px) {
    .matrix-row {
        grid-template-columns: 1.5fr 1fr 1.2fr 1.8fr;
    }
    
    .matrix-cell {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: 0.9rem;
    }
    
    .matrix-row.header .matrix-cell {
        font-size: 1rem;
    }
}

@media (max-width: 768px) {
    .matrix-table {
        font-size: 0.85rem;
        border-radius: var(--border-radius-md);
    }
    
    .matrix-row {
        grid-template-columns: 1fr;
        gap: 1px;
        background: var(--border-color);
    }
    
    .matrix-cell {
        background: var(--surface-color);
        padding: var(--spacing-sm);
        border-right: none;
        border-bottom: 1px solid var(--border-color);
    }
    
    .matrix-cell::before {
        display: none;
    }
    
    .matrix-cell.positive,
    .matrix-cell.negative,
    .matrix-cell.neutral {
        padding-left: var(--spacing-sm);
    }
    
    /* 移动端标签显示 */
    .matrix-cell:not(.matrix-row.header .matrix-cell)::before {
        content: attr(data-label) ': ';
        font-weight: 600;
        color: var(--text-secondary);
        margin-right: var(--spacing-xs);
    }
}

/* 动画效果 */
@keyframes matrix-fade-in {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.matrix-table {
    animation: matrix-fade-in 0.6s ease-out;
}

.matrix-row:not(.header) {
    animation: matrix-fade-in 0.4s ease-out;
    animation-fill-mode: both;
}

.matrix-row:nth-child(2) { animation-delay: 0.1s; }
.matrix-row:nth-child(3) { animation-delay: 0.2s; }
.matrix-row:nth-child(4) { animation-delay: 0.3s; }
.matrix-row:nth-child(5) { animation-delay: 0.4s; }
.matrix-row:nth-child(6) { animation-delay: 0.5s; }

/* ===================================
   品牌领导地位样式系统
   ================================== */

/* 品牌领导地位容器 */
.brand-leadership {
    width: 100%;
    max-width: 100%;
    margin: var(--spacing-xl) 0;
    padding: var(--spacing-xl);
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.95) 0%, 
        rgba(248, 250, 252, 0.95) 100%);
    border-radius: 16px;
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
    border: 1px solid rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.brand-leadership::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, 
        var(--primary-color) 0%, 
        var(--secondary-color) 50%, 
        var(--accent-color) 100%);
}

/* 领导地位头部 */
.leadership-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
    position: relative;
}

.leadership-header h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
    letter-spacing: -0.02em;
    position: relative;
    display: inline-block;
}

.leadership-header h3::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 2px;
}

/* 领导地位内容区域 */
.leadership-content {
    display: flex;
    justify-content: center;
    align-items: stretch;
    gap: var(--spacing-lg);
}

/* 领导者项目 */
.leader-item {
    flex: 1;
    max-width: 500px;
    padding: var(--spacing-xl);
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.9) 0%, 
        rgba(250, 252, 254, 0.9) 100%);
    border-radius: 12px;
    position: relative;
    transition: all var(--transition-smooth);
    overflow: hidden;
}

.leader-item::before {
    content: '';
    position: absolute;
    inset: 0;
    padding: 2px;
    background: linear-gradient(135deg, 
        var(--primary-color), 
        var(--secondary-color), 
        var(--accent-color));
    border-radius: inherit;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: xor;
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
}

.leader-item.kohler {
    border-color: var(--primary-color);
}



/* 领导者图标 */
.leader-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--spacing-lg) auto;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    position: relative;
    overflow: hidden;
}

.leader-icon::before {
    content: '';
    position: absolute;
    inset: 3px;
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.3) 0%, 
        rgba(255, 255, 255, 0.1) 100%);
    border-radius: 50%;
}

.leader-icon i {
    font-size: 2rem;
    color: #ffffff;
    position: relative;
    z-index: 1;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* 领导者信息 */
.leader-info {
    text-align: center;
}

.leader-info h4 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-sm) 0;
    letter-spacing: -0.01em;
}

.leader-info p {
    font-size: 1rem;
    color: var(--text-secondary);
    line-height: 1.6;
    margin: 0 0 var(--spacing-lg) 0;
    max-width: 280px;
    margin-left: auto;
    margin-right: auto;
}

/* 领导者统计信息 */
.leader-stats {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    align-items: center;
}

.stat {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-md);
    background: linear-gradient(135deg, 
        rgba(var(--primary-color-rgb), 0.1) 0%, 
        rgba(var(--secondary-color-rgb), 0.1) 100%);
    color: var(--primary-color);
    font-size: 0.875rem;
    font-weight: 600;
    border-radius: 20px;
    border: 1px solid rgba(var(--primary-color-rgb), 0.2);
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.stat::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, 
        rgba(var(--primary-color-rgb), 0.05) 0%, 
        rgba(var(--secondary-color-rgb), 0.05) 100%);
    opacity: 0;
    transition: opacity var(--transition-base);
}

.stat:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.25);
    border-color: var(--primary-color);
}

.stat:hover::before {
    opacity: 1;
}

/* 响应式设计 */
@media (max-width: 992px) {
    .brand-leadership {
        padding: var(--spacing-lg);
    }
    
    .leadership-header h3 {
        font-size: 1.75rem;
    }
    
    .leader-icon {
        width: 70px;
        height: 70px;
    }
    
    .leader-icon i {
        font-size: 1.75rem;
    }
    
    .leader-info h4 {
        font-size: 1.25rem;
    }
}

@media (max-width: 768px) {
    .brand-leadership {
        margin: var(--spacing-lg) 0;
        padding: var(--spacing-md);
    }
    
    .leadership-header {
        margin-bottom: var(--spacing-lg);
    }
    
    .leadership-header h3 {
        font-size: 1.5rem;
    }
    
    .leadership-content {
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .leader-item {
        padding: var(--spacing-lg);
    }
    
    .leader-icon {
        width: 60px;
        height: 60px;
        margin-bottom: var(--spacing-md);
    }
    
    .leader-icon i {
        font-size: 1.5rem;
    }
    
    .leader-info p {
        max-width: none;
    }
    
    .leader-stats {
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: center;
        gap: var(--spacing-xs);
    }
    
    .stat {
        font-size: 0.8rem;
        padding: var(--spacing-xs) var(--spacing-sm);
    }
}

/* 动画效果 */
@keyframes leadership-fade-in {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes icon-pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.brand-leadership {
    animation: leadership-fade-in 0.8s ease-out;
}

.leader-icon {
    animation: icon-pulse 3s ease-in-out infinite;
}

.leader-item:hover .leader-icon {
    animation-play-state: paused;
    transform: scale(1.05);
}

.stat {
    animation: leadership-fade-in 0.6s ease-out;
    animation-fill-mode: both;
}

.stat:nth-child(1) { animation-delay: 0.1s; }
.stat:nth-child(2) { animation-delay: 0.2s; }
.stat:nth-child(3) { animation-delay: 0.3s; }

/* ===================================
   战略启示样式系统
   ================================== */

/* 战略启示容器 */
.strategic-implications {
    background: var(--background-primary);
    border-radius: 16px;
    padding: var(--spacing-xl);
    border: 2px solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
    margin: var(--spacing-xl) 0;
    position: relative;
    overflow: hidden;
}

.strategic-implications::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, 
        var(--primary-color) 0%, 
        var(--secondary-color) 50%, 
        var(--accent-color) 100%);
}

/* 启示头部 */
.implications-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
    position: relative;
}

.implications-header h3 {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
    letter-spacing: -0.02em;
    position: relative;
    display: inline-block;
}

.implications-header h3::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 2px;
}

/* 启示内容区域 */
.implications-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

/* 启示项目基础样式 */
.implication-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-lg);
    padding: var(--spacing-xl);
    border-radius: 12px;
    border: 2px solid transparent;
    transition: all var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

.implication-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    transition: all var(--transition-base);
}

.implication-item:hover {
    transform: translateX(8px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

/* 优势类型样式 */
.implication-item.strength {
    background: linear-gradient(135deg, 
        rgba(39, 174, 96, 0.08) 0%, 
        rgba(39, 174, 96, 0.03) 100%);
    border-color: rgba(39, 174, 96, 0.2);
}

.implication-item.strength::before {
    background: linear-gradient(180deg, 
        var(--secondary-color) 0%, 
        #27ae60 100%);
}

.implication-item.strength:hover {
    background: linear-gradient(135deg, 
        rgba(39, 174, 96, 0.15) 0%, 
        rgba(39, 174, 96, 0.05) 100%);
    border-color: rgba(39, 174, 96, 0.4);
}

.implication-item.strength .implication-icon {
    color: #27ae60;
    background: linear-gradient(135deg, 
        rgba(39, 174, 96, 0.1) 0%, 
        rgba(39, 174, 96, 0.05) 100%);
    border: 2px solid rgba(39, 174, 96, 0.2);
}

/* 劣势类型样式 */
.implication-item.weakness {
    background: linear-gradient(135deg, 
        rgba(231, 76, 60, 0.08) 0%, 
        rgba(231, 76, 60, 0.03) 100%);
    border-color: rgba(231, 76, 60, 0.2);
}

.implication-item.weakness::before {
    background: linear-gradient(180deg, 
        #e74c3c 0%, 
        #c0392b 100%);
}

.implication-item.weakness:hover {
    background: linear-gradient(135deg, 
        rgba(231, 76, 60, 0.15) 0%, 
        rgba(231, 76, 60, 0.05) 100%);
    border-color: rgba(231, 76, 60, 0.4);
}

.implication-item.weakness .implication-icon {
    color: #e74c3c;
    background: linear-gradient(135deg, 
        rgba(231, 76, 60, 0.1) 0%, 
        rgba(231, 76, 60, 0.05) 100%);
    border: 2px solid rgba(231, 76, 60, 0.2);
}

/* 启示图标 */
.implication-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    flex-shrink: 0;
    margin-top: var(--spacing-xs);
    transition: all var(--transition-base);
    position: relative;
}

.implication-icon::before {
    content: '';
    position: absolute;
    inset: 2px;
    border-radius: 50%;
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.4) 0%, 
        rgba(255, 255, 255, 0.1) 100%);
    opacity: 0;
    transition: opacity var(--transition-base);
}

.implication-item:hover .implication-icon {
    transform: scale(1.1);
}

.implication-item:hover .implication-icon::before {
    opacity: 1;
}

/* 启示文本内容 */
.implication-text {
    flex: 1;
}

.implication-text h4 {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-sm) 0;
    letter-spacing: -0.01em;
}

.implication-text p {
    font-size: 1rem;
    color: var(--text-secondary);
    line-height: 1.6;
    margin: 0;
}

/* 响应式设计 */
@media (max-width: 992px) {
    .strategic-implications {
        padding: var(--spacing-lg);
    }
    
    .implications-header h3 {
        font-size: 1.5rem;
    }
    
    .implication-item {
        padding: var(--spacing-lg);
        gap: var(--spacing-md);
    }
    
    .implication-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
    
    .implication-text h4 {
        font-size: 1.125rem;
    }
}

@media (max-width: 768px) {
    .strategic-implications {
        margin: var(--spacing-lg) 0;
        padding: var(--spacing-md);
    }
    
    .implications-header {
        margin-bottom: var(--spacing-lg);
    }
    
    .implications-header h3 {
        font-size: 1.25rem;
    }
    
    .implications-content {
        gap: var(--spacing-md);
    }
    
    .implication-item {
        flex-direction: column;
        text-align: center;
        padding: var(--spacing-md);
        gap: var(--spacing-sm);
    }
    
    .implication-item:hover {
        transform: translateY(-4px);
    }
    
    .implication-icon {
        width: 45px;
        height: 45px;
        font-size: 1.125rem;
        margin: 0 auto var(--spacing-sm) auto;
    }
    
    .implication-text h4 {
        font-size: 1rem;
    }
    
    .implication-text p {
        font-size: 0.9rem;
    }
}

/* 动画效果 */
@keyframes implications-fade-in {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.strategic-implications {
    animation: implications-fade-in 0.8s ease-out;
}

.implication-item {
    animation: implications-fade-in 0.6s ease-out;
    animation-fill-mode: both;
}

.implication-item:nth-child(1) { 
    animation-delay: 0.1s; 
}

.implication-item:nth-child(2) { 
    animation-delay: 0.2s; 
}

/* ===================================
   战略机会分析样式系统
   ================================== */

/* 战略机会分析容器 */
.strategic-opportunity {
    background: var(--background-primary);
    border-radius: 16px;
    padding: var(--spacing-xl);
    border: 2px solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
    margin: var(--spacing-xl) 0;
    position: relative;
    overflow: hidden;
}

.strategic-opportunity::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, 
        var(--primary-color) 0%, 
        var(--secondary-color) 50%, 
        var(--accent-color) 100%);
}

/* 机会分析头部 */
.opportunity-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
    position: relative;
}

.opportunity-header h3 {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
    letter-spacing: -0.02em;
    position: relative;
    display: inline-block;
}

.opportunity-header h3::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 2px;
}

/* 机会分析内容区域 */
.opportunity-content {
    display: flex;
    justify-content: center;
    align-items: stretch;
    gap: var(--spacing-lg);
}

/* 机会项目基础样式 */
.opportunity-item {
    flex: 1;
    max-width: 400px;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: var(--spacing-xl);
    border-radius: 12px;
    border: 2px solid transparent;
    transition: all var(--transition-smooth);
    position: relative;
    overflow: hidden;
    text-align: center;
}

.opportunity-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    transition: all var(--transition-base);
}

.opportunity-item:hover {
    transform: translateY(-8px);
    box-shadow: 0 16px 40px rgba(0, 0, 0, 0.15);
}

/* 蓝海市场类型样式 */
.opportunity-item.blue-ocean {
    background: linear-gradient(135deg, 
        rgba(52, 152, 219, 0.08) 0%, 
        rgba(52, 152, 219, 0.03) 100%);
    border-color: rgba(52, 152, 219, 0.2);
}

.opportunity-item.blue-ocean::before {
    background: linear-gradient(90deg, 
        var(--primary-color) 0%, 
        #3498db 100%);
}

.opportunity-item.blue-ocean:hover {
    background: linear-gradient(135deg, 
        rgba(52, 152, 219, 0.15) 0%, 
        rgba(52, 152, 219, 0.05) 100%);
    border-color: rgba(52, 152, 219, 0.4);
}

.opportunity-item.blue-ocean .opportunity-icon {
    color: #3498db;
    background: linear-gradient(135deg, 
        rgba(52, 152, 219, 0.1) 0%, 
        rgba(52, 152, 219, 0.05) 100%);
    border: 2px solid rgba(52, 152, 219, 0.2);
}

/* 跳板机会类型样式 */
.opportunity-item.springboard {
    background: linear-gradient(135deg, 
        rgba(155, 89, 182, 0.08) 0%, 
        rgba(155, 89, 182, 0.03) 100%);
    border-color: rgba(155, 89, 182, 0.2);
}

.opportunity-item.springboard::before {
    background: linear-gradient(90deg, 
        #9b59b6 0%, 
        var(--accent-color) 100%);
}

.opportunity-item.springboard:hover {
    background: linear-gradient(135deg, 
        rgba(155, 89, 182, 0.15) 0%, 
        rgba(155, 89, 182, 0.05) 100%);
    border-color: rgba(155, 89, 182, 0.4);
}

.opportunity-item.springboard .opportunity-icon {
    color: #9b59b6;
    background: linear-gradient(135deg, 
        rgba(155, 89, 182, 0.1) 0%, 
        rgba(155, 89, 182, 0.05) 100%);
    border: 2px solid rgba(155, 89, 182, 0.2);
}

/* 机会图标 */
.opportunity-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    flex-shrink: 0;
    margin-bottom: var(--spacing-lg);
    transition: all var(--transition-base);
    position: relative;
}

.opportunity-icon::before {
    content: '';
    position: absolute;
    inset: 3px;
    border-radius: 50%;
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.4) 0%, 
        rgba(255, 255, 255, 0.1) 100%);
    opacity: 0;
    transition: opacity var(--transition-base);
}

.opportunity-item:hover .opportunity-icon {
    transform: scale(1.1);
}

.opportunity-item:hover .opportunity-icon::before {
    opacity: 1;
}

/* 机会信息内容 */
.opportunity-info h4 {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-sm) 0;
    letter-spacing: -0.01em;
}

.opportunity-info p {
    font-size: 1rem;
    color: var(--text-secondary);
    line-height: 1.6;
    margin: 0;
    max-width: 280px;
}

/* 响应式设计 */
@media (max-width: 992px) {
    .strategic-opportunity {
        padding: var(--spacing-lg);
    }
    
    .opportunity-header h3 {
        font-size: 1.5rem;
    }
    
    .opportunity-item {
        padding: var(--spacing-lg);
    }
    
    .opportunity-icon {
        width: 70px;
        height: 70px;
        font-size: 1.75rem;
    }
    
    .opportunity-info h4 {
        font-size: 1.125rem;
    }
}

@media (max-width: 768px) {
    .strategic-opportunity {
        margin: var(--spacing-lg) 0;
        padding: var(--spacing-md);
    }
    
    .opportunity-header {
        margin-bottom: var(--spacing-lg);
    }
    
    .opportunity-header h3 {
        font-size: 1.25rem;
    }
    
    .opportunity-content {
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .opportunity-item {
        max-width: none;
        padding: var(--spacing-md);
    }
    
    .opportunity-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
        margin-bottom: var(--spacing-md);
    }
    
    .opportunity-info h4 {
        font-size: 1rem;
    }
    
    .opportunity-info p {
        font-size: 0.9rem;
        max-width: none;
    }
}

/* 动画效果 */
@keyframes opportunity-fade-in {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes opportunity-icon-pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.strategic-opportunity {
    animation: opportunity-fade-in 0.8s ease-out;
}

.opportunity-item {
    animation: opportunity-fade-in 0.6s ease-out;
    animation-fill-mode: both;
}

.opportunity-item:nth-child(1) { 
    animation-delay: 0.1s; 
}

.opportunity-item:nth-child(2) { 
    animation-delay: 0.2s; 
}

.opportunity-icon {
    animation: opportunity-icon-pulse 3s ease-in-out infinite;
}

.opportunity-item:hover .opportunity-icon {
    animation-play-state: paused;
    transform: scale(1.1);
}

/* ===================================
   SWOT分析样式系统
   ================================== */

/* SWOT网格容器 */
.swot-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    gap: var(--spacing-lg);
    margin: var(--spacing-xl) 0;
    min-height: 600px;
}

/* SWOT象限基础样式 */
.swot-quadrant {
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.95) 0%, 
        rgba(248, 250, 252, 0.95) 100%);
    border-radius: 16px;
    padding: var(--spacing-xl);
    position: relative;
    border: 2px solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
    transition: all var(--transition-smooth);
    overflow: hidden;
}

.swot-quadrant::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    transition: all var(--transition-base);
}

.swot-quadrant:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
}

/* SWOT标题样式 */
.swot-quadrant h3 {
    font-size: 1.25rem;
    font-weight: 700;
    margin: 0 0 var(--spacing-lg) 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    position: relative;
}

.swot-quadrant h3 i {
    font-size: 1.5rem;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    flex-shrink: 0;
}

/* 优势象限样式 */
.swot-quadrant.strengths {
    background: linear-gradient(135deg, 
        rgba(39, 174, 96, 0.08) 0%, 
        rgba(39, 174, 96, 0.03) 100%);
    border-color: rgba(39, 174, 96, 0.2);
}

.swot-quadrant.strengths::before {
    background: linear-gradient(90deg, #27ae60, #2ecc71);
}

.swot-quadrant.strengths h3 {
    color: #27ae60;
}

.swot-quadrant.strengths h3 i {
    background: rgba(39, 174, 96, 0.15);
    color: #27ae60;
}

/* 劣势象限样式 */
.swot-quadrant.weaknesses {
    background: linear-gradient(135deg, 
        rgba(231, 76, 60, 0.08) 0%, 
        rgba(231, 76, 60, 0.03) 100%);
    border-color: rgba(231, 76, 60, 0.2);
}

.swot-quadrant.weaknesses::before {
    background: linear-gradient(90deg, #e74c3c, #c0392b);
}

.swot-quadrant.weaknesses h3 {
    color: #e74c3c;
}

.swot-quadrant.weaknesses h3 i {
    background: rgba(231, 76, 60, 0.15);
    color: #e74c3c;
}

/* 机会象限样式 */
.swot-quadrant.opportunities {
    background: linear-gradient(135deg, 
        rgba(52, 152, 219, 0.08) 0%, 
        rgba(52, 152, 219, 0.03) 100%);
    border-color: rgba(52, 152, 219, 0.2);
}

.swot-quadrant.opportunities::before {
    background: linear-gradient(90deg, #3498db, #5dade2);
}

.swot-quadrant.opportunities h3 {
    color: #3498db;
}

.swot-quadrant.opportunities h3 i {
    background: rgba(52, 152, 219, 0.15);
    color: #3498db;
}

/* 威胁象限样式 */
.swot-quadrant.threats {
    background: linear-gradient(135deg, 
        rgba(241, 196, 15, 0.08) 0%, 
        rgba(241, 196, 15, 0.03) 100%);
    border-color: rgba(241, 196, 15, 0.2);
}

.swot-quadrant.threats::before {
    background: linear-gradient(90deg, #f39c12, #f1c40f);
}

.swot-quadrant.threats h3 {
    color: #f39c12;
}

.swot-quadrant.threats h3 i {
    background: rgba(241, 196, 15, 0.15);
    color: #f39c12;
}

/* SWOT列表样式 */
.swot-quadrant ul {
    list-style: none;
    padding: 0;
    margin: 0;
    text-align: left;
}

.swot-quadrant li {
    position: relative;
    padding: var(--spacing-sm) 0;
    padding-left: var(--spacing-lg);
    font-size: 0.95rem;
    line-height: 1.6;
    color: var(--text-secondary);
    transition: all var(--transition-base);
}

.swot-quadrant li:before {
    content: '•';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1.5rem;
    font-weight: bold;
    opacity: 0.6;
}

.swot-quadrant.strengths li:before {
    color: #27ae60;
}

.swot-quadrant.weaknesses li:before {
    color: #e74c3c;
}

.swot-quadrant.opportunities li:before {
    color: #3498db;
}

.swot-quadrant.threats li:before {
    color: #f39c12;
}

.swot-quadrant li:hover {
    color: var(--text-primary);
    transform: translateX(4px);
}

.swot-quadrant li strong {
    color: var(--text-primary);
    font-weight: 600;
}

/* SWOT战略分析部分 */
.swot-strategies {
    background: var(--background-primary);
    border-radius: 16px;
    padding: var(--spacing-xl);
    margin-top: var(--spacing-xl);
    border: 2px solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
}

.swot-strategies h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    text-align: center;
    margin: 0 0 var(--spacing-xl) 0;
    position: relative;
}

.swot-strategies h3::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 2px;
}

/* 战略网格 */
.strategy-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
}

.strategy-item {
    padding: var(--spacing-lg);
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.6) 0%, 
        rgba(248, 250, 252, 0.6) 100%);
    border-radius: 12px;
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all var(--transition-base);
}

.strategy-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.8) 0%, 
        rgba(248, 250, 252, 0.8) 100%);
}

.strategy-item h4 {
    font-size: 1.125rem;
    font-weight: 700;
    margin: 0 0 var(--spacing-sm) 0;
    color: var(--primary-color);
}

.strategy-item p {
    font-size: 0.95rem;
    line-height: 1.6;
    color: var(--text-secondary);
    margin: 0;
}

/* SWOT结论 */
.swot-conclusion {
    text-align: center;
    padding: var(--spacing-xl);
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.6) 0%, 
        rgba(248, 250, 252, 0.6) 100%);
    border-radius: 12px;
    margin-top: var(--spacing-xl);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.swot-conclusion p {
    font-size: 1rem;
    line-height: 1.8;
    color: var(--text-secondary);
    max-width: 800px;
    margin: 0 auto;
}

/* 响应式设计 */
@media (max-width: 992px) {
    .swot-grid {
        grid-template-columns: 1fr;
        grid-template-rows: auto;
        min-height: auto;
    }
    
    .swot-quadrant {
        padding: var(--spacing-lg);
    }
    
    .strategy-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .swot-grid {
        gap: var(--spacing-md);
    }
    
    .swot-quadrant {
        padding: var(--spacing-md);
    }
    
    .swot-quadrant h3 {
        font-size: 1.125rem;
    }
    
    .swot-quadrant h3 i {
        width: 35px;
        height: 35px;
        font-size: 1.25rem;
    }
    
    .swot-quadrant li {
        font-size: 0.9rem;
        padding-left: var(--spacing-md);
    }
    
    .swot-strategies {
        padding: var(--spacing-md);
    }
    
    .swot-strategies h3 {
        font-size: 1.25rem;
    }
    
    .strategy-item {
        padding: var(--spacing-md);
    }
    
    .strategy-item h4 {
        font-size: 1rem;
    }
    
    .strategy-item p {
        font-size: 0.875rem;
    }
}

/* 动画效果 */
@keyframes swot-fade-in {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.swot-grid {
    animation: swot-fade-in 0.8s ease-out;
}

.swot-quadrant {
    animation: swot-fade-in 0.6s ease-out;
    animation-fill-mode: both;
}

.swot-quadrant:nth-child(1) { animation-delay: 0.1s; }
.swot-quadrant:nth-child(2) { animation-delay: 0.2s; }
.swot-quadrant:nth-child(3) { animation-delay: 0.3s; }
.swot-quadrant:nth-child(4) { animation-delay: 0.4s; }

.swot-strategies {
    animation: swot-fade-in 0.7s ease-out;
    animation-delay: 0.5s;
    animation-fill-mode: both;
}

/* ===================================
   品牌策略对比样式系统
   ================================== */

/* 品牌策略对比容器 */
.brand-strategy-comparison {
    background: var(--background-primary);
    border-radius: 16px;
    padding: var(--spacing-xl);
    margin: var(--spacing-xl) 0;
    border: 2px solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
    position: relative;
    overflow: hidden;
}

.brand-strategy-comparison::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, 
        var(--primary-color) 0%, 
        var(--secondary-color) 50%, 
        var(--accent-color) 100%);
}

/* 策略对比表格 */
.strategy-comparison-table {
    margin: var(--spacing-xl) 0;
}

.comparison-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.comparison-header h3 {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
    position: relative;
    display: inline-block;
}

.comparison-header h3::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 2px;
}

/* 对比展现形式 */
.comparison-columns {
    display: flex;
    align-items: stretch;
    gap: var(--spacing-md);
    margin: var(--spacing-xl) 0;
    position: relative;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
    border-radius: 20px;
    padding: var(--spacing-lg);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.8);
}

/* 中央分割线和VS标识 */
.comparison-columns::before {
    content: 'VS';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #ff6b47, #ff8a42);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    font-weight: 800;
    box-shadow: 0 8px 20px rgba(255, 107, 71, 0.4);
    z-index: 10;
    border: 4px solid white;
}

.comparison-columns::after {
    content: '';
    position: absolute;
    top: 10%;
    left: 50%;
    transform: translateX(-50%);
    width: 2px;
    height: 80%;
    background: linear-gradient(180deg, 
        rgba(39, 174, 96, 0.3) 0%, 
        rgba(255, 107, 71, 0.6) 50%, 
        rgba(52, 152, 219, 0.3) 100%);
    z-index: 1;
}

.strategy-column {
    flex: 1;
    background: transparent;
    border-radius: 16px;
    padding: var(--spacing-xl);
    position: relative;
    transition: all var(--transition-smooth);
    overflow: hidden;
}

.strategy-column:hover {
    transform: scale(1.02);
}

/* 新兴品牌样式 */
.strategy-column.emerging-brands {
    background: linear-gradient(135deg, 
        rgba(39, 174, 96, 0.1) 0%, 
        rgba(46, 204, 113, 0.05) 100%);
    border: 2px solid rgba(39, 174, 96, 0.3);
    margin-right: var(--spacing-lg);
}

.strategy-column.emerging-brands::before {
    content: '新兴品牌';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    height: 50px;
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    font-weight: 700;
    border-radius: 14px 14px 0 0;
    letter-spacing: 1px;
}

.strategy-column.emerging-brands h4 {
    color: transparent;
    margin-top: var(--spacing-xl);
}

/* 传统巨头样式 */
.strategy-column.traditional-giants {
    background: linear-gradient(135deg, 
        rgba(52, 152, 219, 0.1) 0%, 
        rgba(93, 173, 226, 0.05) 100%);
    border: 2px solid rgba(52, 152, 219, 0.3);
    margin-left: var(--spacing-lg);
}

.strategy-column.traditional-giants::before {
    content: '传统巨头';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    height: 50px;
    background: linear-gradient(135deg, #3498db, #5dade2);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    font-weight: 700;
    border-radius: 14px 14px 0 0;
    letter-spacing: 1px;
}

.strategy-column.traditional-giants h4 {
    color: transparent;
    margin-top: var(--spacing-xl);
}

/* 策略列标题 */
.strategy-column h4 {
    font-size: 1.5rem;
    font-weight: 700;
    text-align: center;
    margin: 0 0 var(--spacing-lg) 0;
    position: relative;
    padding-bottom: var(--spacing-sm);
}

.strategy-column h4::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 2px;
    background: currentColor;
    border-radius: 1px;
}

/* 对比策略部分 */
.strategy-section {
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    padding: var(--spacing-md) var(--spacing-lg);
    border: 1px solid rgba(255, 255, 255, 0.6);
    transition: all var(--transition-base);
    margin-bottom: var(--spacing-md);
    backdrop-filter: blur(8px);
}

.strategy-section:hover {
    background: rgba(255, 255, 255, 0.95);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.strategy-section:last-child {
    margin-bottom: 0;
}

/* 策略标题样式 */
.strategy-section h5 {
    font-size: 1rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-sm) 0;
    text-align: center;
    position: relative;
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: 20px;
    background: linear-gradient(135deg, rgba(0, 115, 230, 0.1), rgba(0, 115, 230, 0.05));
    border: 1px solid rgba(0, 115, 230, 0.2);
}

/* 不同类型的策略标题颜色 */
.emerging-brands .strategy-section:nth-child(1) h5 {
    background: linear-gradient(135deg, rgba(39, 174, 96, 0.15), rgba(39, 174, 96, 0.05));
    border-color: rgba(39, 174, 96, 0.3);
    color: #27ae60;
}

.emerging-brands .strategy-section:nth-child(2) h5 {
    background: linear-gradient(135deg, rgba(46, 204, 113, 0.15), rgba(46, 204, 113, 0.05));
    border-color: rgba(46, 204, 113, 0.3);
    color: #2ecc71;
}

.emerging-brands .strategy-section:nth-child(3) h5 {
    background: linear-gradient(135deg, rgba(231, 76, 60, 0.15), rgba(231, 76, 60, 0.05));
    border-color: rgba(231, 76, 60, 0.3);
    color: #e74c3c;
}

.traditional-giants .strategy-section:nth-child(1) h5 {
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.15), rgba(52, 152, 219, 0.05));
    border-color: rgba(52, 152, 219, 0.3);
    color: #3498db;
}

.traditional-giants .strategy-section:nth-child(2) h5 {
    background: linear-gradient(135deg, rgba(93, 173, 226, 0.15), rgba(93, 173, 226, 0.05));
    border-color: rgba(93, 173, 226, 0.3);
    color: #5dade2;
}

.traditional-giants .strategy-section:nth-child(3) h5 {
    background: linear-gradient(135deg, rgba(155, 89, 182, 0.15), rgba(155, 89, 182, 0.05));
    border-color: rgba(155, 89, 182, 0.3);
    color: #9b59b6;
}

.strategy-section ul {
    list-style: none;
    padding: 0;
    margin: 0;
    text-align: left;
}

.strategy-section li {
    position: relative;
    padding: var(--spacing-xs) 0;
    padding-left: var(--spacing-lg);
    font-size: 0.9rem;
    line-height: 1.5;
    color: var(--text-secondary);
    transition: all var(--transition-base);
    border-radius: 6px;
    margin-bottom: var(--spacing-xs);
}

.strategy-section li:before {
    content: '•';
    position: absolute;
    left: var(--spacing-xs);
    top: 50%;
    transform: translateY(-50%);
    color: var(--primary-color);
    font-size: 1.2rem;
    font-weight: bold;
}

/* 新兴品牌的list项目样式 */
.emerging-brands .strategy-section li:before {
    color: #27ae60;
}

.emerging-brands .strategy-section li:hover {
    background: rgba(39, 174, 96, 0.1);
    padding-left: calc(var(--spacing-md) + 4px);
    color: var(--text-primary);
}

/* 传统巨头的list项目样式 */
.traditional-giants .strategy-section li:before {
    color: #3498db;
}

.traditional-giants .strategy-section li:hover {
    background: rgba(52, 152, 219, 0.1);
    padding-left: calc(var(--spacing-md) + 4px);
    color: var(--text-primary);
}

/* 案例研究部分 */
.case-studies {
    margin: var(--spacing-xl) 0;
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.6) 0%, 
        rgba(248, 250, 252, 0.6) 100%);
    border-radius: 16px;
    padding: var(--spacing-xl);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.case-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.case-header h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
    position: relative;
    display: inline-block;
}

.case-header h3::after {
    content: '';
    position: absolute;
    bottom: -6px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 1px;
}

/* 案例网格 */
.case-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
}

.case-section {
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.8) 0%, 
        rgba(248, 250, 252, 0.8) 100%);
    border-radius: 12px;
    padding: var(--spacing-lg);
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all var(--transition-base);
}

.case-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.case-section h4 {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color);
    text-align: center;
    margin: 0 0 var(--spacing-lg) 0;
    position: relative;
}

.case-section h4::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 2px;
    background: currentColor;
    border-radius: 1px;
}

/* 品牌案例 */
.brand-case {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.brand-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: var(--spacing-md);
    background: rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    border: 2px solid rgba(0, 0, 0, 0.05);
    transition: all var(--transition-base);
    color: var(--text-primary);
}

.brand-item:hover {
    background: rgba(255, 255, 255, 1);
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    color: var(--text-primary);
}

.brand-name {
    font-size: 1rem;
    font-weight: 700;
    margin-bottom: var(--spacing-xs);
}

.brand-detail {
    font-size: 0.875rem;
    color: var(--text-secondary);
    line-height: 1.5;
}

/* Bejoan定位部分 */
.bejoan-positioning {
    margin-top: var(--spacing-xl);
    padding: var(--spacing-xl);
    background: linear-gradient(135deg, 
        rgba(0, 115, 230, 0.08) 0%, 
        rgba(0, 115, 230, 0.03) 100%);
    border-radius: 12px;
    border: 2px solid rgba(0, 115, 230, 0.2);
    position: relative;
    overflow: hidden;
}

.bejoan-positioning::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.bejoan-positioning h4 {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color);
    text-align: center;
    margin: 0 0 var(--spacing-lg) 0;
}

.bejoan-positioning p {
    font-size: 1rem;
    line-height: 1.7;
    color: var(--text-secondary);
    text-align: center;
    margin: 0;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* 策略结论 */
.strategy-conclusion {
    text-align: center;
    padding: var(--spacing-xl);
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.6) 0%, 
        rgba(248, 250, 252, 0.6) 100%);
    border-radius: 12px;
    margin-top: var(--spacing-xl);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.strategy-conclusion p {
    font-size: 1rem;
    line-height: 1.8;
    color: var(--text-secondary);
    max-width: 700px;
    margin: 0 auto;
}

/* 响应式设计 */
@media (max-width: 992px) {
    .comparison-columns {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
    
    .case-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
    
    .strategy-column {
        padding: var(--spacing-lg);
    }
    
    .case-section {
        padding: var(--spacing-md);
    }
}

@media (max-width: 768px) {
    .brand-strategy-comparison {
        padding: var(--spacing-md);
    }
    
    .comparison-header h3 {
        font-size: 1.5rem;
    }
    
    .strategy-column {
        padding: var(--spacing-md);
    }
    
    .strategy-column h4 {
        font-size: 1.25rem;
    }
    
    .strategy-section {
        padding: var(--spacing-md);
    }
    
    .case-studies {
        padding: var(--spacing-md);
    }
    
    .case-header h3 {
        font-size: 1.25rem;
    }
    
    .bejoan-positioning {
        padding: var(--spacing-md);
    }
    
    .bejoan-positioning h4 {
        font-size: 1.125rem;
    }
    
    .bejoan-positioning p {
        font-size: 0.9rem;
    }
}

/* 动画效果 */
@keyframes strategy-fade-in {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.brand-strategy-comparison {
    animation: strategy-fade-in 0.8s ease-out;
}

.strategy-column {
    animation: strategy-fade-in 0.6s ease-out;
    animation-fill-mode: both;
}

.strategy-column.emerging-brands {
    animation-delay: 0.1s;
}

.strategy-column.traditional-giants {
    animation-delay: 0.2s;
}

.case-studies {
    animation: strategy-fade-in 0.7s ease-out;
    animation-delay: 0.3s;
    animation-fill-mode: both;
}

.bejoan-positioning {
    animation: strategy-fade-in 0.7s ease-out;
    animation-delay: 0.4s;
    animation-fill-mode: both;
}

/* ===================================
   功能缺失机会分析样式系统
   ================================== */

/* 功能缺失分析容器 */
.function-gap-analysis {
    background: var(--background-primary);
    border-radius: 16px;
    padding: var(--spacing-xl);
    margin: var(--spacing-xl) 0;
    border: 2px solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
    position: relative;
    overflow: hidden;
}

.function-gap-analysis::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, 
        var(--primary-color) 0%, 
        var(--secondary-color) 50%, 
        var(--accent-color) 100%);
}

/* 功能缺失头部 */
.gap-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.gap-header h3 {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
    position: relative;
    display: inline-block;
}

.gap-header h3::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 2px;
}

/* 功能缺失内容 */
.gap-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.gap-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.95) 0%, 
        rgba(248, 250, 252, 0.95) 100%);
    border-radius: 12px;
    border: 2px solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
    transition: all var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

.gap-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--primary-color);
    transition: all var(--transition-base);
}

.gap-item:hover {
    transform: translateX(8px);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
}

.gap-item:hover::before {
    width: 6px;
}

/* 功能缺失图标 */
.gap-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 1.5rem;
    flex-shrink: 0;
    box-shadow: 0 8px 24px rgba(var(--primary-color-rgb), 0.3);
    position: relative;
    overflow: hidden;
    transition: all var(--transition-base);
}

.gap-icon::before {
    content: '';
    position: absolute;
    inset: 3px;
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.3) 0%, 
        rgba(255, 255, 255, 0.1) 100%);
    border-radius: 50%;
}

.gap-icon i {
    position: relative;
    z-index: 1;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.gap-item:hover .gap-icon {
    transform: scale(1.1);
    box-shadow: 0 12px 30px rgba(var(--primary-color-rgb), 0.4);
}

/* 功能缺失信息 */
.gap-info {
    flex: 1;
}

.gap-info h4 {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-sm) 0;
    letter-spacing: -0.01em;
}

.gap-info p {
    font-size: 1rem;
    color: var(--text-secondary);
    line-height: 1.6;
    margin: 0;
}

/* 竞争定位矩阵 */
.competitive-positioning {
    background: var(--background-primary);
    border-radius: 16px;
    padding: var(--spacing-xl);
    margin: var(--spacing-xl) 0;
    border: 2px solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
    position: relative;
    overflow: hidden;
}

.competitive-positioning::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, 
        var(--primary-color) 0%, 
        var(--secondary-color) 50%, 
        var(--accent-color) 100%);
}

/* 定位头部 */
.positioning-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.positioning-header h3 {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-sm) 0;
    position: relative;
    display: inline-block;
}

.positioning-header h3::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 2px;
}

.positioning-subtitle {
    font-size: 1rem;
    color: var(--text-secondary);
    font-weight: 500;
    margin-top: var(--spacing-sm);
}

/* 定位矩阵 */
.positioning-matrix {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    gap: var(--spacing-lg);
    min-height: 400px;
    margin: var(--spacing-xl) 0;
}

/* 矩阵象限 */
.matrix-quadrant {
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.95) 0%, 
        rgba(248, 250, 252, 0.95) 100%);
    border-radius: 16px;
    padding: var(--spacing-xl);
    position: relative;
    border: 2px solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
    transition: all var(--transition-smooth);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: center;
}

.matrix-quadrant::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    transition: all var(--transition-base);
}

.matrix-quadrant:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
}

/* 高价格/高功能象限 */
.matrix-quadrant.high-price-high-function::before {
    background: linear-gradient(90deg, #3498db, #5dade2);
}

.matrix-quadrant.high-price-high-function {
    border-color: rgba(52, 152, 219, 0.3);
}

/* 低价格/高功能象限 */
.matrix-quadrant.low-price-high-function::before {
    background: linear-gradient(90deg, #27ae60, #2ecc71);
}

.matrix-quadrant.low-price-high-function {
    border-color: rgba(39, 174, 96, 0.3);
}

/* 高价格/基础功能象限 */
.matrix-quadrant.high-price-low-function::before {
    background: linear-gradient(90deg, #f39c12, #f1c40f);
}

.matrix-quadrant.high-price-low-function {
    border-color: rgba(243, 156, 18, 0.3);
}

/* 低价格/基础功能象限 */
.matrix-quadrant.low-price-low-function::before {
    background: linear-gradient(90deg, #e74c3c, #c0392b);
}

.matrix-quadrant.low-price-low-function {
    border-color: rgba(231, 76, 60, 0.3);
}

/* 象限标签 */
.quadrant-label {
    font-size: 1rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
    position: relative;
    padding-bottom: var(--spacing-sm);
}

.quadrant-label::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 2px;
    background: var(--primary-color);
    border-radius: 1px;
}

/* 象限内容 */
.quadrant-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: var(--spacing-md);
}

.brand-position {
    font-size: 1rem;
    color: var(--text-secondary);
    line-height: 1.6;
    padding: var(--spacing-md);
    background: rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all var(--transition-base);
}

.brand-position:hover {
    background: rgba(255, 255, 255, 1);
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.opportunity-position {
    font-size: 1rem;
    color: var(--primary-color);
    font-weight: 600;
    line-height: 1.6;
    padding: var(--spacing-md);
    background: linear-gradient(135deg, 
        rgba(var(--primary-color-rgb), 0.1) 0%, 
        rgba(var(--secondary-color-rgb), 0.1) 100%);
    border-radius: 8px;
    border: 2px solid rgba(var(--primary-color-rgb), 0.3);
    transition: all var(--transition-base);
}

.opportunity-position:hover {
    background: linear-gradient(135deg, 
        rgba(var(--primary-color-rgb), 0.2) 0%, 
        rgba(var(--secondary-color-rgb), 0.2) 100%);
    transform: scale(1.05);
    box-shadow: 0 8px 20px rgba(var(--primary-color-rgb), 0.3);
}

/* 响应式设计 */
@media (max-width: 992px) {
    .function-gap-analysis {
        padding: var(--spacing-lg);
    }
    
    .competitive-positioning {
        padding: var(--spacing-lg);
    }
    
    .positioning-matrix {
        grid-template-columns: 1fr;
        grid-template-rows: auto;
        min-height: auto;
    }
    
    .matrix-quadrant {
        padding: var(--spacing-lg);
    }
    
    .gap-item {
        padding: var(--spacing-md);
    }
    
    .gap-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
}

@media (max-width: 768px) {
    .function-gap-analysis {
        padding: var(--spacing-md);
    }
    
    .competitive-positioning {
        padding: var(--spacing-md);
    }
    
    .gap-header h3 {
        font-size: 1.5rem;
    }
    
    .positioning-header h3 {
        font-size: 1.5rem;
    }
    
    .gap-item {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-sm);
        padding: var(--spacing-sm);
    }
    
    .gap-item:hover {
        transform: translateY(-4px);
    }
    
    .gap-icon {
        width: 45px;
        height: 45px;
        font-size: 1.125rem;
        margin: 0 auto;
    }
    
    .gap-info h4 {
        font-size: 1.125rem;
        text-align: center;
    }
    
    .gap-info p {
        font-size: 0.9rem;
        text-align: center;
    }
    
    .positioning-matrix {
        gap: var(--spacing-md);
    }
    
    .matrix-quadrant {
        padding: var(--spacing-md);
        min-height: 150px;
    }
    
    .quadrant-label {
        font-size: 0.9rem;
        margin-bottom: var(--spacing-md);
    }
    
    .brand-position,
    .opportunity-position {
        font-size: 0.875rem;
        padding: var(--spacing-sm);
    }
}

/* 动画效果 */
@keyframes gap-fade-in {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.function-gap-analysis {
    animation: gap-fade-in 0.8s ease-out;
}

.gap-item {
    animation: gap-fade-in 0.6s ease-out;
    animation-fill-mode: both;
}

.gap-item:nth-child(1) { animation-delay: 0.1s; }
.gap-item:nth-child(2) { animation-delay: 0.2s; }
.gap-item:nth-child(3) { animation-delay: 0.3s; }

.competitive-positioning {
    animation: gap-fade-in 0.8s ease-out;
    animation-delay: 0.4s;
    animation-fill-mode: both;
}

.matrix-quadrant {
    animation: gap-fade-in 0.6s ease-out;
    animation-fill-mode: both;
}

.matrix-quadrant:nth-child(1) { animation-delay: 0.5s; }
.matrix-quadrant:nth-child(2) { animation-delay: 0.6s; }
.matrix-quadrant:nth-child(3) { animation-delay: 0.7s; }
.matrix-quadrant:nth-child(4) { animation-delay: 0.8s; }

/* ===================================
   平台比较数据样式系统
   ================================== */

/* 比较卡片内容容器 */
.card-content {
    padding: var(--spacing-lg);
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.95) 0%, 
        rgba(248, 250, 252, 0.95) 100%);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.8);
}

/* 比较项目容器 */
.comparison-item {
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.6);
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all var(--transition-base);
}

.comparison-item:last-child {
    margin-bottom: 0;
}

.comparison-item:hover {
    background: rgba(255, 255, 255, 0.8);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

/* 指标标签 */
.metric-label {
    display: block;
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
}

.test-item {
    background: var(--background-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-light);
    transition: all var(--transition-base);
}

/* 指标比较容器 */
.metric-comparison {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
    align-items: center;
}

/* 指标数值样式 */
.metric-value {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: 6px;
    font-weight: 600;
    font-size: 0.95rem;
    position: relative;
    transition: all var(--transition-base);
    text-align: center;
}

/* HD平台样式 */
.metric-value.hd {
    background: linear-gradient(135deg, 
        rgba(255, 107, 53, 0.15) 0%, 
        rgba(255, 107, 53, 0.08) 100%);
    color: #d35400;
    border: 1px solid rgba(255, 107, 53, 0.3);
}

.metric-value.hd::before {
    content: '🏠';
    position: absolute;
    left: 8px;
    font-size: 1rem;
}

.metric-value.hd {
    padding-left: 32px;
}

/* Lowe's平台样式 */
.metric-value.lowes {
    background: linear-gradient(135deg, 
        rgba(52, 152, 219, 0.15) 0%, 
        rgba(52, 152, 219, 0.08) 100%);
    color: #2980b9;
    border: 1px solid rgba(52, 152, 219, 0.3);
}

.metric-value.lowes::before {
    content: '🔧';
    position: absolute;
    left: 8px;
    font-size: 1rem;
}

.metric-value.lowes {
    padding-left: 32px;
}

/* 悬停效果 */
.metric-value:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.metric-value.hd:hover {
    background: linear-gradient(135deg, 
        rgba(255, 107, 53, 0.25) 0%, 
        rgba(255, 107, 53, 0.12) 100%);
    border-color: rgba(255, 107, 53, 0.5);
}

.metric-value.lowes:hover {
    background: linear-gradient(135deg, 
        rgba(52, 152, 219, 0.25) 0%, 
        rgba(52, 152, 219, 0.12) 100%);
    border-color: rgba(52, 152, 219, 0.5);
}

/* 数值高亮效果 */
.metric-value strong,
.metric-value b {
    font-weight: 700;
    font-size: 1.1em;
}

/* 特殊标识（优势/劣势指示器） */
.metric-value[data-advantage="true"]::after {
    content: '↗';
    position: absolute;
    right: 8px;
    color: #27ae60;
    font-weight: bold;
    font-size: 1.2rem;
}

.metric-value[data-disadvantage="true"]::after {
    content: '↘';
    position: absolute;
    right: 8px;
    color: #e74c3c;
    font-weight: bold;
    font-size: 1.2rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .card-content {
        padding: var(--spacing-md);
    }
    
    .comparison-item {
        padding: var(--spacing-sm);
        margin-bottom: var(--spacing-md);
    }
    
    .metric-comparison {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }
    
    .metric-value {
        font-size: 0.85rem;
        padding: var(--spacing-xs) var(--spacing-sm);
    }
    
    .metric-value.hd,
    .metric-value.lowes {
        padding-left: 28px;
    }
    
    .metric-value.hd::before,
    .metric-value.lowes::before {
        left: 6px;
        font-size: 0.9rem;
    }
    
    .metric-label {
        font-size: 0.8rem;
        margin-bottom: var(--spacing-xs);
    }
}

@media (max-width: 480px) {
    .metric-value {
        font-size: 0.8rem;
        padding: var(--spacing-xs);
    }
    
    .metric-value.hd,
    .metric-value.lowes {
        padding-left: 24px;
    }
    
    .metric-value.hd::before,
    .metric-value.lowes::before {
        left: 4px;
        font-size: 0.8rem;
    }
}

/* 动画效果 */
@keyframes comparison-fade-in {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.comparison-item {
    animation: comparison-fade-in 0.5s ease-out;
    animation-fill-mode: both;
}

.comparison-item:nth-child(1) { animation-delay: 0.1s; }
.comparison-item:nth-child(2) { animation-delay: 0.2s; }
.comparison-item:nth-child(3) { animation-delay: 0.3s; }

/* 特殊数值高亮 */
.metric-value:contains("248") {
    position: relative;
}

.metric-value:contains("105") {
    position: relative;
}

/* 数据对比增强 */
.comparison-item[data-metric="products"] .metric-value.hd {
    border-left: 4px solid #27ae60;
}

.comparison-item[data-metric="products"] .metric-value.lowes {
    border-left: 4px solid #f39c12;
}

.comparison-item[data-metric="price"] .metric-value.hd {
    border-left: 4px solid #e74c3c;
}

.comparison-item[data-metric="price"] .metric-value.lowes {
    border-left: 4px solid #27ae60;
}

.comparison-item[data-metric="concentration"] .metric-value.hd {
    border-left: 4px solid #f39c12;
}

.comparison-item[data-metric="concentration"] .metric-value.lowes {
    border-left: 4px solid #3498db;
}

/* ===================================
   整合营销计划样式系统
   ================================== */

/* 整合营销计划容器 */
.integrated-marketing-plan {
    background: var(--background-primary);
    border-radius: 16px;
    padding: var(--spacing-xl);
    border: 2px solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
    margin: var(--spacing-xl) 0;
    position: relative;
    overflow: hidden;
}

.integrated-marketing-plan::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, 
        var(--primary-color) 0%, 
        var(--secondary-color) 50%, 
        var(--accent-color) 100%);
}

/* 计划头部 */
.plan-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
    position: relative;
}

.plan-header h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-sm) 0;
    letter-spacing: -0.02em;
    position: relative;
    display: inline-block;
}

.plan-header h3::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 2px;
}

.plan-subtitle {
    font-size: 1rem;
    color: var(--text-secondary);
    font-weight: 500;
    margin-top: var(--spacing-sm);
}

/* 营销阶段容器 */
.marketing-phases {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

/* 阶段卡片 */
.phase-card {
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.95) 0%, 
        rgba(248, 250, 252, 0.95) 100%);
    border-radius: 16px;
    padding: var(--spacing-xl);
    position: relative;
    transition: all var(--transition-smooth);
    overflow: hidden;
    border: 2px solid transparent;
}

.phase-card::before {
    content: '';
    position: absolute;
    inset: 0;
    padding: 2px;
    background: linear-gradient(135deg, 
        var(--primary-color), 
        var(--secondary-color), 
        var(--accent-color));
    border-radius: inherit;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: xor;
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    opacity: 0;
    transition: opacity var(--transition-base);
}

.phase-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 16px 40px rgba(0, 0, 0, 0.15);
}

.phase-card:hover::before {
    opacity: 1;
}

/* 启动期样式 */
.startup-phase {
    background: linear-gradient(135deg, 
        rgba(39, 174, 96, 0.08) 0%, 
        rgba(39, 174, 96, 0.03) 100%);
}

.startup-phase .phase-icon {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: #ffffff;
}

/* 增长期样式 */
.growth-phase {
    background: linear-gradient(135deg, 
        rgba(52, 152, 219, 0.08) 0%, 
        rgba(52, 152, 219, 0.03) 100%);
}

.growth-phase .phase-icon {
    background: linear-gradient(135deg, #3498db, #5dade2);
    color: #ffffff;
}

/* 阶段指示器 */
.phase-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.phase-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    position: relative;
    overflow: hidden;
    transition: all var(--transition-base);
}

.phase-icon::before {
    content: '';
    position: absolute;
    inset: 3px;
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.3) 0%, 
        rgba(255, 255, 255, 0.1) 100%);
    border-radius: 50%;
}

.phase-icon i {
    position: relative;
    z-index: 1;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.phase-timeline {
    background: linear-gradient(135deg, 
        rgba(var(--primary-color-rgb), 0.1) 0%, 
        rgba(var(--secondary-color-rgb), 0.1) 100%);
    color: var(--primary-color);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
    border: 1px solid rgba(var(--primary-color-rgb), 0.2);
}

/* 阶段详情 */
.phase-details h4 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-sm) 0;
    letter-spacing: -0.01em;
}

.phase-description {
    font-size: 1rem;
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
}

/* 阶段指标 */
.phase-metrics {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.metric-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background: rgba(255, 255, 255, 0.7);
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all var(--transition-base);
}

.metric-item:hover {
    background: rgba(255, 255, 255, 0.9);
    transform: translateX(4px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.metric-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 600;
}

.metric-value {
    font-size: 1rem;
    color: var(--text-primary);
    font-weight: 700;
    padding: var(--spacing-xs) var(--spacing-sm);
    background: linear-gradient(135deg, 
        rgba(var(--primary-color-rgb), 0.1) 0%, 
        rgba(var(--secondary-color-rgb), 0.1) 100%);
    border-radius: 16px;
    border: 1px solid rgba(var(--primary-color-rgb), 0.2);
}

/* 阶段预算 */
.phase-budget {
    text-align: center;
    padding: var(--spacing-lg);
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.8) 0%, 
        rgba(248, 250, 252, 0.8) 100%);
    border-radius: 12px;
    border: 2px solid rgba(var(--primary-color-rgb), 0.1);
    position: relative;
    overflow: hidden;
}

.phase-budget::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.budget-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.budget-amount {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    text-shadow: 0 2px 8px rgba(var(--primary-color-rgb), 0.3);
}

/* 计划总结 */
.plan-summary {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.6) 0%, 
        rgba(248, 250, 252, 0.6) 100%);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.8);
}

.summary-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--background-secondary);
    border-radius: 8px;
    transition: all var(--transition-base);
}

.summary-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.summary-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 1.25rem;
    flex-shrink: 0;
    box-shadow: 0 6px 16px rgba(var(--primary-color-rgb), 0.3);
}

.summary-content {
    flex: 1;
}

.summary-title {
    font-size: 1rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.summary-text {
    font-size: 0.9rem;
    color: var(--text-secondary);
    line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 992px) {
    .integrated-marketing-plan {
        padding: var(--spacing-lg);
    }
    
    .plan-header h3 {
        font-size: 1.75rem;
    }
    
    .marketing-phases {
        gap: var(--spacing-lg);
    }
    
    .phase-card {
        padding: var(--spacing-lg);
    }
    
    .phase-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
    
    .phase-details h4 {
        font-size: 1.25rem;
    }
    
    .budget-amount {
        font-size: 1.25rem;
    }
}

@media (max-width: 768px) {
    .integrated-marketing-plan {
        margin: var(--spacing-lg) 0;
        padding: var(--spacing-md);
    }
    
    .plan-header {
        margin-bottom: var(--spacing-lg);
    }
    
    .plan-header h3 {
        font-size: 1.5rem;
    }
    
    .plan-subtitle {
        font-size: 0.9rem;
    }
    
    .marketing-phases {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
        margin-bottom: var(--spacing-lg);
    }
    
    .phase-card {
        padding: var(--spacing-md);
    }
    
    .phase-indicator {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-sm);
    }
    
    .phase-icon {
        width: 45px;
        height: 45px;
        font-size: 1.125rem;
    }
    
    .phase-timeline {
        font-size: 0.8rem;
        padding: var(--spacing-xs) var(--spacing-sm);
    }
    
    .phase-details h4 {
        font-size: 1.125rem;
        text-align: center;
    }
    
    .phase-description {
        font-size: 0.9rem;
        text-align: center;
    }
    
    .metric-item {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-xs);
        padding: var(--spacing-sm);
    }
    
    .metric-item:hover {
        transform: translateY(-2px);
    }
    
    .metric-label {
        font-size: 0.8rem;
    }
    
    .metric-value {
        font-size: 0.9rem;
    }
    
    .phase-budget {
        padding: var(--spacing-md);
    }
    
    .budget-amount {
        font-size: 1.125rem;
    }
    
    .plan-summary {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
        padding: var(--spacing-md);
    }
    
    .summary-item {
        padding: var(--spacing-sm);
        gap: var(--spacing-sm);
    }
    
    .summary-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
    
    .summary-title {
        font-size: 0.9rem;
    }
    
    .summary-text {
        font-size: 0.8rem;
    }
}

/* 动画效果 */
@keyframes marketing-plan-fade-in {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes phase-slide-in {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes metric-count-up {
    from {
        transform: scale(0.8);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

.integrated-marketing-plan {
    animation: marketing-plan-fade-in 0.8s ease-out;
}

.phase-card {
    animation: phase-slide-in 0.6s ease-out;
    animation-fill-mode: both;
}

.startup-phase {
    animation-delay: 0.1s;
}

.growth-phase {
    animation-delay: 0.2s;
}

.metric-item {
    animation: marketing-plan-fade-in 0.5s ease-out;
    animation-fill-mode: both;
}

.metric-item:nth-child(1) { animation-delay: 0.3s; }
.metric-item:nth-child(2) { animation-delay: 0.4s; }
.metric-item:nth-child(3) { animation-delay: 0.5s; }

.metric-value {
    animation: metric-count-up 0.6s ease-out;
    animation-fill-mode: both;
    animation-delay: 0.6s;
}

.plan-summary {
    animation: marketing-plan-fade-in 0.7s ease-out;
    animation-delay: 0.8s;
    animation-fill-mode: both;
}

/* 悬停增强效果 */
.phase-card:hover .phase-icon {
    transform: scale(1.1);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.2);
}

.phase-card:hover .metric-value {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.3);
}

.phase-card:hover .budget-amount {
    transform: scale(1.1);
    text-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.4);
}

/* 特殊效果 */
@keyframes budget-pulse {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(var(--primary-color-rgb), 0.4);
    }
    50% {
        box-shadow: 0 0 0 10px rgba(var(--primary-color-rgb), 0);
    }
}

.phase-budget:hover {
    animation: budget-pulse 2s infinite;
}

/* 进度指示器（可选增强） */
.phase-card::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    transition: width 0.6s ease-out;
}

.phase-card:hover::after {
    width: 100%;
}

/* ===================================
   Slide-3 主要品牌表现对比样式
   ================================== */

/* 品牌表现对比区域 */
.brand-performance-section {
    margin-top: var(--spacing-xl);
    padding: var(--spacing-xl);
    background: linear-gradient(135deg, 
        rgba(248, 250, 252, 0.95) 0%, 
        rgba(255, 255, 255, 0.95) 100%);
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.brand-performance-section h3 {
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    text-align: center;
}

.brand-performance-section .section-subtitle {
    font-size: 0.95rem;
    color: var(--text-secondary);
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

/* 品牌对比网格 */
.brand-comparison-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

/* 品牌卡片 */
.brand-card {
    background: white;
    border-radius: 12px;
    padding: var(--spacing-lg);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.08);
}

.brand-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

.brand-card h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
    text-align: center;
}

/* 品牌指标 */
.brand-metrics {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.brand-metrics .metric {
    padding: var(--spacing-sm) var(--spacing-md);
    background: rgba(0, 0, 0, 0.03);
    border-radius: 8px;
    font-size: 0.9rem;
    color: var(--text-secondary);
    text-align: center;
}

.brand-metrics .metric.highlight {
    background: linear-gradient(135deg, 
        rgba(0, 115, 230, 0.1) 0%, 
        rgba(0, 166, 81, 0.1) 100%);
    color: var(--primary-color);
    font-weight: 500;
}

/* Bejoan参考信息 */
.bejoan-reference {
    margin-top: var(--spacing-xl);
    padding: var(--spacing-lg);
    background: linear-gradient(135deg, 
        rgba(0, 166, 81, 0.05) 0%, 
        rgba(0, 115, 230, 0.05) 100%);
    border-radius: 12px;
    border: 1px solid rgba(0, 166, 81, 0.2);
}

.bejoan-reference h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

.bejoan-reference p {
    font-size: 0.95rem;
    color: var(--text-primary);
    line-height: 1.6;
}

/* 响应式布局 */
@media (max-width: 1024px) {
    .brand-comparison-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .brand-performance-section {
        padding: var(--spacing-md);
    }
    
    .brand-comparison-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .brand-card {
        padding: var(--spacing-md);
    }
}

/* 修复价格区间分布样式 */
.price-range-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
}

.price-range-item .range-label {
    flex: 0 0 140px;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.price-range-item .range-bar {
    flex: 1;
    height: 24px;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 12px;
    overflow: hidden;
    position: relative;
}

.price-range-item .range-fill {
    height: 100%;
    border-radius: 12px;
    transition: width 1s ease-out;
}

.price-range-item .range-value {
    flex: 0 0 50px;
    text-align: right;
    font-weight: 600;
    color: var(--text-primary);
}

/* 对比展现移动端响应式样式 */
@media (max-width: 768px) {
    .comparison-columns {
        flex-direction: column;
        gap: var(--spacing-xl);
        padding: var(--spacing-md);
    }

    .comparison-columns::before {
        top: calc(50% - var(--spacing-xl)/2);
        width: 50px;
        height: 50px;
        font-size: 1rem;
    }

    .comparison-columns::after {
        display: none;
    }

    .strategy-column.emerging-brands,
    .strategy-column.traditional-giants {
        margin: 0;
    }

    .strategy-column {
        padding: var(--spacing-lg);
    }

    .strategy-column::before {
        font-size: 1rem;
        height: 40px;
    }

    .strategy-section {
        padding: var(--spacing-sm) var(--spacing-md);
        margin-bottom: var(--spacing-sm);
    }

    .strategy-section h5 {
        font-size: 0.9rem;
        padding: var(--spacing-xs);
    }

    .strategy-section li {
        font-size: 0.85rem;
        padding: var(--spacing-xs) 0;
        padding-left: var(--spacing-sm);
    }

    .strategy-section li:before {
        font-size: 1rem;
        left: 2px;
    }
}

