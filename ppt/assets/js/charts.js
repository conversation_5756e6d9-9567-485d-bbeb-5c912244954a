// 图表工具库
class ChartManager {
    constructor() {
        this.charts = new Map();
        this.colors = {
            primary: '#0073e6',
            secondary: '#00a651',
            accent: '#ff6b35',
            warning: '#ffc107',
            info: '#17a2b8',
            light: '#f8f9fa',
            dark: '#343a40'
        };
        
        this.gradients = {
            primary: ['#0073e6', '#005bb5'],
            secondary: ['#00a651', '#008a44'],
            accent: ['#ff6b35', '#e55a2b'],
            multi: ['#0073e6', '#00a651', '#ff6b35', '#ffc107', '#17a2b8']
        };
    }

    // 创建响应式Canvas
    createCanvas(containerId, aspectRatio = 2) {
        const container = document.getElementById(containerId);
        if (!container) return null;

        // 先清理容器中现有的canvas元素
        const existingCanvas = container.querySelector('.chart-canvas');
        if (existingCanvas) {
            existingCanvas.remove();
        }

        const canvas = document.createElement('canvas');
        canvas.className = 'chart-canvas';
        
        // 响应式尺寸
        const resizeCanvas = () => {
            const containerWidth = container.offsetWidth;
            canvas.width = containerWidth;
            canvas.height = containerWidth / aspectRatio;
        };

        window.addEventListener('resize', resizeCanvas);
        resizeCanvas();
        
        container.appendChild(canvas);
        return canvas;
    }

    // 创建市场规模饼图
    createMarketSizePieChart(containerId, data) {
        // 先销毁现有的图表实例
        this.destroyChart(containerId);
        
        const canvas = this.createCanvas(containerId, 1.5);
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        const chart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: data.labels,
                datasets: [{
                    data: data.values,
                    backgroundColor: this.gradients.multi,
                    borderWidth: 3,
                    borderColor: '#ffffff',
                    hoverBorderWidth: 5,
                    hoverOffset: 10
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            usePointStyle: true,
                            padding: 20,
                            font: {
                                size: 14,
                                family: 'Inter'
                            }
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: (context) => {
                                const label = context.label || '';
                                const value = context.parsed;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return `${label}: $${value.toFixed(1)}B (${percentage}%)`;
                            }
                        }
                    }
                },
                animation: {
                    animateRotate: true,
                    duration: 2000
                }
            }
        });

        this.charts.set(containerId, chart);
        return chart;
    }

    // 创建GMV对比柱状图
    createGMVBarChart(containerId, data) {
        // 先销毁现有的图表实例
        this.destroyChart(containerId);
        
        const canvas = this.createCanvas(containerId, 2);
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        const chart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: data.labels,
                datasets: [{
                    label: 'GMV (Million $)',
                    data: data.values,
                    backgroundColor: this.gradients.multi.map(color => color + '80'),
                    borderColor: this.gradients.multi,
                    borderWidth: 2,
                    borderRadius: 8,
                    borderSkipped: false,
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: (context) => {
                                return `GMV: $${context.parsed.y.toFixed(1)}M`;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: (value) => `$${value}M`
                        },
                        grid: {
                            color: '#e9ecef'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                },
                animation: {
                    duration: 2000,
                    easing: 'easeOutQuart'
                }
            }
        });

        this.charts.set(containerId, chart);
        return chart;
    }

    // 创建价格分布堆叠柱状图
    createPriceDistributionChart(containerId, data) {
        // 先销毁现有的图表实例
        this.destroyChart(containerId);
        
        const canvas = this.createCanvas(containerId, 2.5);
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        const chart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: data.channels,
                datasets: [
                    {
                        label: '经济型 (<$800)',
                        data: data.economy,
                        backgroundColor: this.colors.info + '80',
                        borderColor: this.colors.info,
                        borderWidth: 1
                    },
                    {
                        label: '中端 ($801-$1500)',
                        data: data.mid,
                        backgroundColor: this.colors.primary + '80',
                        borderColor: this.colors.primary,
                        borderWidth: 1
                    },
                    {
                        label: '高端 (>$1501)',
                        data: data.premium,
                        backgroundColor: this.colors.accent + '80',
                        borderColor: this.colors.accent,
                        borderWidth: 1
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        stacked: true,
                        grid: {
                            display: false
                        }
                    },
                    y: {
                        stacked: true,
                        beginAtZero: true,
                        ticks: {
                            callback: (value) => `${value}%`
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        callbacks: {
                            label: (context) => {
                                return `${context.dataset.label}: ${context.parsed.y.toFixed(1)}%`;
                            }
                        }
                    }
                },
                animation: {
                    duration: 2000,
                    easing: 'easeOutQuart'
                }
            }
        });

        this.charts.set(containerId, chart);
        return chart;
    }

    // 创建功能对比雷达图
    createFeatureRadarChart(containerId, data) {
        // 先销毁现有的图表实例
        this.destroyChart(containerId);
        
        const canvas = this.createCanvas(containerId, 1);
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        const chart = new Chart(ctx, {
            type: 'radar',
            data: {
                labels: data.features,
                datasets: [
                    {
                        label: 'Bejoan',
                        data: data.bejoan,
                        backgroundColor: this.colors.primary + '20',
                        borderColor: this.colors.primary,
                        borderWidth: 3,
                        pointBackgroundColor: this.colors.primary,
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 6
                    },
                    {
                        label: '竞品平均',
                        data: data.competitors,
                        backgroundColor: this.colors.secondary + '20',
                        borderColor: this.colors.secondary,
                        borderWidth: 3,
                        pointBackgroundColor: this.colors.secondary,
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 6
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    r: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            stepSize: 20,
                            callback: (value) => `${value}%`
                        },
                        grid: {
                            color: '#e9ecef'
                        },
                        angleLines: {
                            color: '#e9ecef'
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    }
                },
                animation: {
                    duration: 2000,
                    easing: 'easeOutQuart'
                }
            }
        });

        this.charts.set(containerId, chart);
        return chart;
    }

    // 创建投资回报对比图
    createROIComparisonChart(containerId, data) {
        // 先销毁现有的图表实例
        this.destroyChart(containerId);
        
        const canvas = this.createCanvas(containerId, 2);
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        const chart = new Chart(ctx, {
            type: 'scatter',
            data: {
                datasets: data.strategies.map((strategy, index) => ({
                    label: strategy.name,
                    data: [{
                        x: strategy.risk,
                        y: strategy.roi
                    }],
                    backgroundColor: this.gradients.multi[index] + '80',
                    borderColor: this.gradients.multi[index],
                    borderWidth: 3,
                    pointRadius: strategy.recommended ? 15 : 10,
                    pointHoverRadius: strategy.recommended ? 18 : 13
                }))
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: '风险等级 (1-10)',
                            font: {
                                size: 14,
                                weight: 'bold'
                            }
                        },
                        min: 0,
                        max: 10
                    },
                    y: {
                        title: {
                            display: true,
                            text: 'ROI (%)',
                            font: {
                                size: 14,
                                weight: 'bold'
                            }
                        },
                        ticks: {
                            callback: (value) => `${value}%`
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: (context) => {
                                const strategy = data.strategies[context.datasetIndex];
                                return [
                                    `${strategy.name}`,
                                    `风险: ${context.parsed.x}/10`,
                                    `ROI: ${context.parsed.y}%`,
                                    `投资: $${strategy.investment}M`
                                ];
                            }
                        }
                    }
                },
                animation: {
                    duration: 2000,
                    easing: 'easeOutQuart'
                }
            }
        });

        this.charts.set(containerId, chart);
        return chart;
    }

    // 销毁图表
    destroyChart(containerId) {
        const chart = this.charts.get(containerId);
        if (chart) {
            chart.destroy();
            this.charts.delete(containerId);
        }
    }

    // 销毁所有图表
    destroyAllCharts() {
        this.charts.forEach(chart => chart.destroy());
        this.charts.clear();
    }

    // 更新图表数据
    updateChart(containerId, newData) {
        const chart = this.charts.get(containerId);
        if (chart) {
            chart.data = newData;
            chart.update('active');
        }
    }

    // 创建产品类型分布饼图
    createProductTypePieChart(containerId, data) {
        // 先销毁现有的图表实例
        this.destroyChart(containerId);
        
        const canvas = this.createCanvas(containerId, 1.5);
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        const chart = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: data.labels,
                datasets: [{
                    data: data.values,
                    backgroundColor: [this.colors.primary, this.colors.secondary],
                    borderWidth: 2,
                    borderColor: '#ffffff',
                }]
            },
            plugins: [ChartDataLabels],
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            usePointStyle: true,
                            padding: 20,
                            font: {
                                size: 14,
                                family: 'Inter'
                            }
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: (context) => {
                                const label = context.label || '';
                                const value = context.parsed;
                                return ` ${label}: ${value}%`;
                            }
                        }
                    },
                    datalabels: {
                        display: true,
                        color: 'white',
                        font: {
                            size: 16,
                            weight: 'bold'
                        },
                        formatter: (value, context) => {
                            return value + '%';
                        },
                        anchor: 'center',
                        align: 'center'
                    }
                }
            }
        });
        this.charts.set(containerId, chart);
        return chart;
    }

    // 创建价格区间分布饼图
    createPriceRangePieChart(containerId, data) {
        // 先销毁现有的图表实例
        this.destroyChart(containerId);
        
        const canvas = this.createCanvas(containerId, 1.5);
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        const chart = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: data.labels,
                datasets: [{
                    data: data.values,
                    backgroundColor: [this.colors.info, this.colors.primary, this.colors.warning],
                    borderWidth: 2,
                    borderColor: '#ffffff',
                }]
            },
            plugins: [ChartDataLabels],
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            usePointStyle: true,
                            padding: 20,
                            font: {
                                size: 14,
                                family: 'Inter'
                            }
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: (context) => {
                                const label = context.label || '';
                                const value = context.parsed;
                                return ` ${label}: ${value}%`;
                            }
                        }
                    },
                    datalabels: {
                        display: true,
                        color: 'white',
                        font: {
                            size: 16,
                            weight: 'bold'
                        },
                        formatter: (value, context) => {
                            return value + '%';
                        },
                        anchor: 'center',
                        align: 'center'
                    }
                }
            }
        });
        this.charts.set(containerId, chart);
        return chart;
    }
}

// 全局图表管理器实例
window.chartManager = new ChartManager();
