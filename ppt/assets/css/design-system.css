/* ===================================
   McKinsey风格设计系统增强版
   ================================== */

:root {
    /* === 颜色系统 === */
    /* 主色调 - McKinsey蓝 */
    --primary-color: #0073e6;
    --primary-light: #4da3f0;
    --primary-dark: #005bb5;
    --primary-alpha-10: rgba(0, 115, 230, 0.1);
    --primary-alpha-20: rgba(0, 115, 230, 0.2);
    
    /* 辅助色调 */
    --secondary-color: #00a651;
    --secondary-light: #33b86f;
    --secondary-dark: #008a44;
    
    /* 强调色调 */
    --accent-color: #ff6b35;
    --accent-light: #ff8a5c;
    --accent-dark: #e55a2b;
    
    /* 中性色调 */
    --text-primary: #1a1a1a;
    --text-secondary: #666666;
    --text-tertiary: #999999;
    --text-inverse: #ffffff;
    
    /* 背景色调 */
    --background-primary: #ffffff;
    --background-secondary: #f8f9fa;
    --background-tertiary: #f1f3f5;
    --background-dark: #2c3e50;
    
    /* 边框色调 */
    --border-color: #e9ecef;
    --border-light: #f1f3f5;
    --border-dark: #dee2e6;
    
    /* 状态色调 */
    --success-color: #28a745;
    --warning-color: #ffc107;
    --error-color: #dc3545;
    --info-color: #17a2b8;
    
    /* === 字体系统 === */
    --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', monospace;
    
    /* 字体大小 - 基于1.25比例 */
    --font-size-xs: 0.75rem;    /* 12px */
    --font-size-sm: 0.875rem;   /* 14px */
    --font-size-base: 1rem;     /* 16px */
    --font-size-lg: 1.125rem;   /* 18px */
    --font-size-xl: 1.25rem;    /* 20px */
    --font-size-2xl: 1.5rem;    /* 24px */
    --font-size-3xl: 1.875rem;  /* 30px */
    --font-size-4xl: 2.25rem;   /* 36px */
    --font-size-5xl: 3rem;      /* 48px */
    
    /* 字体权重 */
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;
    
    /* 行高 */
    --line-height-tight: 1.25;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.75;
    
    /* === 间距系统 - 8px网格 === */
    --spacing-xs: 0.25rem;   /* 4px */
    --spacing-sm: 0.5rem;    /* 8px */
    --spacing-md: 1rem;      /* 16px */
    --spacing-lg: 1.5rem;    /* 24px */
    --spacing-xl: 2rem;      /* 32px */
    --spacing-2xl: 3rem;     /* 48px */
    --spacing-3xl: 4rem;     /* 64px */
    --spacing-4xl: 6rem;     /* 96px */
    
    /* === 阴影系统 === */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    
    /* 内阴影 */
    --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
    
    /* === 圆角系统 === */
    --radius-none: 0;
    --radius-sm: 0.25rem;    /* 4px */
    --radius-md: 0.5rem;     /* 8px */
    --radius-lg: 0.75rem;    /* 12px */
    --radius-xl: 1rem;       /* 16px */
    --radius-2xl: 1.5rem;    /* 24px */
    --radius-full: 9999px;
    
    /* === 过渡动画 === */
    --transition-fast: 150ms ease-out;
    --transition-base: 250ms ease-out;
    --transition-slow: 350ms ease-out;
    --transition-slower: 500ms ease-out;
    
    /* 缓动函数 */
    --ease-in: cubic-bezier(0.4, 0, 1, 1);
    --ease-out: cubic-bezier(0, 0, 0.2, 1);
    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
    --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
    
    /* === Z-index层级 === */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    
    /* === 断点系统 === */
    --breakpoint-sm: 640px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 1024px;
    --breakpoint-xl: 1280px;
    --breakpoint-2xl: 1536px;
}

/* === 全局重置和基础样式 === */
* {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-base);
    line-height: var(--line-height-normal);
    color: var(--text-primary);
    background-color: var(--background-primary);
    margin: 0;
    padding: 0;
}

/* === 字体样式类 === */
.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }
.text-3xl { font-size: var(--font-size-3xl); }
.text-4xl { font-size: var(--font-size-4xl); }
.text-5xl { font-size: var(--font-size-5xl); }

.font-light { font-weight: var(--font-weight-light); }
.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }
.font-extrabold { font-weight: var(--font-weight-extrabold); }

.leading-tight { line-height: var(--line-height-tight); }
.leading-normal { line-height: var(--line-height-normal); }
.leading-relaxed { line-height: var(--line-height-relaxed); }

/* === 间距工具类 === */
.m-0 { margin: 0; }
.m-1 { margin: var(--spacing-xs); }
.m-2 { margin: var(--spacing-sm); }
.m-4 { margin: var(--spacing-md); }
.m-6 { margin: var(--spacing-lg); }
.m-8 { margin: var(--spacing-xl); }

.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-4 { padding: var(--spacing-md); }
.p-6 { padding: var(--spacing-lg); }
.p-8 { padding: var(--spacing-xl); }

/* === 阴影工具类 === */
.shadow-xs { box-shadow: var(--shadow-xs); }
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }
.shadow-2xl { box-shadow: var(--shadow-2xl); }
.shadow-inner { box-shadow: var(--shadow-inner); }

/* === 圆角工具类 === */
.rounded-none { border-radius: var(--radius-none); }
.rounded-sm { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-2xl { border-radius: var(--radius-2xl); }
.rounded-full { border-radius: var(--radius-full); }

/* === 过渡工具类 === */
.transition-fast { transition: all var(--transition-fast); }
.transition-base { transition: all var(--transition-base); }
.transition-slow { transition: all var(--transition-slow); }
.transition-slower { transition: all var(--transition-slower); }

/* === 颜色工具类 === */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.text-inverse { color: var(--text-inverse); }

.bg-primary { background-color: var(--background-primary); }
.bg-secondary { background-color: var(--background-secondary); }
.bg-tertiary { background-color: var(--background-tertiary); }

.border-color { border-color: var(--border-color); }
.border-light { border-color: var(--border-light); }
.border-dark { border-color: var(--border-dark); }

/* === 响应式工具类 === */
@media (max-width: 768px) {
    .md\:hidden { display: none; }
    .md\:text-sm { font-size: var(--font-size-sm); }
    .md\:p-4 { padding: var(--spacing-md); }
}

@media (min-width: 769px) {
    .md\:block { display: block; }
    .md\:flex { display: flex; }
    .md\:grid { display: grid; }
}
