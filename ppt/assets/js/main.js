// 主应用程序逻辑
class PresentationApp {
    constructor() {
        this.currentSlide = 1;
        this.totalSlides = 37;
        this.currentChapter = 1;
        this.isLoading = false;

        // 章节配置
        this.chapters = {
            1: { name: 'chapter1_background', slides: [1, 2, 3, 4, 5, 6], totalSlides: 6 },
            2: { name: 'chapter2_battlefield', slides: [7, 8, 9, 10, 11, 12, 13, 14, 15, 16], totalSlides: 10 },
            3: { name: 'chapter3_players', slides: [17, 18, 19, 20, 21, 22, 23], totalSlides: 7 },
            4: { name: 'chapter4_strategy', slides: [24, 25, 26, 27], totalSlides: 4 },
            5: { name: 'chapter5_choices', slides: [28, 29, 30, 31, 32], totalSlides: 5},
            6: { name: 'chapter6_summary', slides: [33, 34, 35, 36, 37], totalSlides: 5 }
        };
        
        this.init();
    }
    
    init() {
        console.log('Initializing PresentationApp...');
        showDebug('正在初始化应用', this.currentSlide);

        this.bindEvents();
        this.updateProgress();
        this.updateControls();
        this.updateChapterNavigation();
        this.hideLoading();
        this.initAnimations();

        // 不需要重复加载，iframe已经有初始src
        console.log('PresentationApp initialized successfully');
        showDebug('应用初始化完成', this.currentSlide);
    }
    
    bindEvents() {
        // 导航按钮
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');

        if (prevBtn) {
            prevBtn.addEventListener('click', () => {
                console.log('Previous button clicked');
                this.previousSlide();
            });
        } else {
            console.error('Previous button not found');
        }

        if (nextBtn) {
            nextBtn.addEventListener('click', () => {
                console.log('Next button clicked');
                this.nextSlide();
            });
        } else {
            console.error('Next button not found');
        }

        // 头部章节导航
        document.querySelectorAll('.chapter-tab').forEach(item => {
            item.addEventListener('click', (e) => {
                const chapter = parseInt(e.currentTarget.dataset.chapter);
                console.log('Chapter tab clicked:', chapter);
                this.goToChapter(chapter);
            });
        });



        // 键盘导航
        document.addEventListener('keydown', (e) => this.handleKeyboard(e));

        // 触摸手势
        this.initTouchGestures();
    }
    
    previousSlide() {
        if (this.currentSlide > 1) {
            this.goToSlide(this.currentSlide - 1);
        }
    }
    
    nextSlide() {
        if (this.currentSlide < this.totalSlides) {
            this.goToSlide(this.currentSlide + 1);
        }
    }
    
    goToSlide(slideNumber) {
        console.log(`goToSlide called with: ${slideNumber}`);
        showDebug(`正在跳转到第${slideNumber}页`, slideNumber);

        if (slideNumber < 1 || slideNumber > this.totalSlides || this.isLoading) {
            console.log(`Invalid slide number or loading: ${slideNumber}`);
            showDebug('跳转失败', slideNumber, '无效的幻灯片编号或正在加载');
            return;
        }

        this.showLoading();
        this.currentSlide = slideNumber;

        // 确定当前章节
        const chapter = this.getChapterBySlide(slideNumber);
        if (chapter !== this.currentChapter) {
            this.currentChapter = chapter;
            this.updateChapterNavigation();
        }

        // 加载对应的章节页面
        const chapterInfo = this.chapters[chapter];
        const slideIndex = chapterInfo.slides.indexOf(slideNumber);
        const localSlideNumber = slideIndex + 1;
        const url = `chapters/${chapterInfo.name}.html#slide-${localSlideNumber}`;

        console.log(`Loading slide ${slideNumber}, chapter ${chapter}, local slide ${localSlideNumber}, URL: ${url}`);
        showDebug(`加载第${chapter}章第${localSlideNumber}页`, slideNumber);

        this.loadSlide(url);
        this.updateProgress();
        this.updateControls();
    }
    
    goToChapter(chapterNumber) {
        const chapterInfo = this.chapters[chapterNumber];
        if (chapterInfo) {
            this.goToSlide(chapterInfo.slides[0]);
        }
    }
    
    getChapterBySlide(slideNumber) {
        for (const [chapter, info] of Object.entries(this.chapters)) {
            if (info.slides.includes(slideNumber)) {
                return parseInt(chapter);
            }
        }
        return 1;
    }
    
    loadSlide(url) {
        const iframe = document.getElementById('slideFrame');
        const currentBaseUrl = iframe.src.split('#')[0];
        const newBaseUrl = url.split('#')[0];

        iframe.style.opacity = '0';

        setTimeout(() => {
            // If the base URL is the same (or the current one ends with the new one, handling relative paths),
            // we are navigating within the same chapter. The 'onload' event will not fire reliably.
            if (currentBaseUrl.endsWith(newBaseUrl)) {
                iframe.src = url; // Just change the hash
                // Manually trigger the "post-load" logic after a short delay
                setTimeout(() => {
                    iframe.style.opacity = '1';
                    this.hideLoading();
                    this.triggerSlideAnimations();
                }, 100);
            } else {
                // If the base URL is different, we are loading a new chapter file.
                // In this case, we must rely on the 'onload' event.
                iframe.onload = () => {
                    iframe.style.opacity = '1';
                    this.hideLoading();
                    this.triggerSlideAnimations();
                    iframe.onload = null; // Clean up the event handler to prevent unexpected firing
                };
                iframe.onerror = () => {
                    console.error('Failed to load slide:', url);
                    this.hideLoading();
                    iframe.onerror = null; // Clean up
                };
                iframe.src = url;
            }
        }, 150); // Delay to allow fade-out animation to be visible
    }
    
    updateProgress() {
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        const currentSlideSpan = document.getElementById('currentSlide');

        if (progressFill && progressText && currentSlideSpan) {
            const percentage = (this.currentSlide / this.totalSlides) * 100;
            progressFill.style.width = `${percentage}%`;
            progressText.textContent = `${this.currentSlide} / ${this.totalSlides}`;
            currentSlideSpan.textContent = this.currentSlide;
        }
    }
    
    updateControls() {
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');

        if (prevBtn && nextBtn) {
            prevBtn.disabled = this.currentSlide === 1;
            nextBtn.disabled = this.currentSlide === this.totalSlides;
        }
    }
    
    updateChapterNavigation() {
        document.querySelectorAll('.chapter-tab').forEach(item => {
            item.classList.remove('active');
        });

        const activeChapter = document.querySelector(`.chapter-tab[data-chapter="${this.currentChapter}"]`);
        if (activeChapter) {
            activeChapter.classList.add('active');
        }
    }
    
    showLoading() {
        this.isLoading = true;
        document.getElementById('loadingOverlay').classList.remove('hidden');
    }
    
    hideLoading() {
        this.isLoading = false;
        setTimeout(() => {
            document.getElementById('loadingOverlay').classList.add('hidden');
        }, 300);
    }
    
    handleKeyboard(e) {
        switch(e.key) {
            case 'ArrowLeft':
            case 'ArrowUp':
                e.preventDefault();
                this.previousSlide();
                break;
            case 'ArrowRight':
            case 'ArrowDown':
            case ' ':
                e.preventDefault();
                this.nextSlide();
                break;
            case 'Home':
                e.preventDefault();
                this.goToSlide(1);
                break;
            case 'End':
                e.preventDefault();
                this.goToSlide(this.totalSlides);
                break;

        }
    }
    
    initTouchGestures() {
        let startX = 0;
        let startY = 0;
        
        document.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
        });
        
        document.addEventListener('touchend', (e) => {
            const endX = e.changedTouches[0].clientX;
            const endY = e.changedTouches[0].clientY;
            const diffX = startX - endX;
            const diffY = startY - endY;
            
            // 水平滑动
            if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
                if (diffX > 0) {
                    this.nextSlide();
                } else {
                    this.previousSlide();
                }
            }
        });
    }
    

    
    initAnimations() {
        // 初始化动画系统
        this.animationController = new AnimationController();
    }
    
    triggerSlideAnimations() {
        // 触发当前幻灯片的动画
        const iframe = document.getElementById('slideFrame');
        if (iframe.contentWindow && iframe.contentWindow.triggerAnimations) {
            iframe.contentWindow.triggerAnimations();
        }
    }
}

// 动画控制器
class AnimationController {
    constructor() {
        this.observers = [];
        this.initIntersectionObserver();
    }
    
    initIntersectionObserver() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.triggerAnimation(entry.target);
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '50px'
        });
        
        this.observers.push(observer);
    }
    
    triggerAnimation(element) {
        if (element.classList.contains('animate-on-scroll')) {
            element.classList.add('animated');
        }
    }
    
    // 数字递增动画
    animateNumber(element, start, end, duration = 1000) {
        const startTime = performance.now();
        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            const current = Math.floor(start + (end - start) * this.easeOutQuart(progress));
            element.textContent = current.toLocaleString();
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };
        
        requestAnimationFrame(animate);
    }
    
    easeOutQuart(t) {
        return 1 - Math.pow(1 - t, 4);
    }
}

// 调试函数
function showDebug(status, slide, error = '无') {
    const debugOverlay = document.getElementById('debugOverlay');
    const debugStatus = document.getElementById('debugStatus');
    const debugSlide = document.getElementById('debugSlide');
    const debugError = document.getElementById('debugError');

    if (debugOverlay && debugStatus && debugSlide && debugError) {
        debugOverlay.classList.add('show');
        debugStatus.textContent = status;
        debugSlide.textContent = slide;
        debugError.textContent = error;
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing app...');
    showDebug('DOM已加载', '1');

    // 检查关键元素是否存在
    const slideFrame = document.getElementById('slideFrame');
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');

    if (!slideFrame) {
        console.error('slideFrame not found');
        showDebug('错误', '1', 'slideFrame未找到');
        return;
    }

    if (!prevBtn || !nextBtn) {
        console.error('Navigation buttons not found');
        showDebug('错误', '1', '导航按钮未找到');
        return;
    }

    // 确保所有元素都存在
    setTimeout(() => {
        try {
            window.presentationApp = new PresentationApp();
            showDebug('应用已初始化', '1');

            // 加载保存的主题
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme === 'dark') {
                document.body.classList.add('dark-theme');
            }
        } catch (error) {
            console.error('Failed to initialize app:', error);
            showDebug('初始化失败', '1', error.message);
        }
    }, 100);
});

// 导出给其他模块使用
window.PresentationApp = PresentationApp;
