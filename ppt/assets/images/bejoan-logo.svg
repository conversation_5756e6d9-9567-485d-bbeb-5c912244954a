<svg width="120" height="40" viewBox="0 0 120 40" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#0073e6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00a651;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆角矩形 -->
  <rect x="2" y="2" width="36" height="36" rx="8" fill="url(#logoGradient)" />
  
  <!-- 字母 B -->
  <text x="20" y="28" font-family="Inter, Arial, sans-serif" font-size="24" font-weight="700" fill="white" text-anchor="middle">B</text>
  
  <!-- 公司名称 -->
  <text x="48" y="18" font-family="Inter, Arial, sans-serif" font-size="16" font-weight="600" fill="#0073e6">Be<PERSON>an</text>
  <text x="48" y="32" font-family="Inter, Arial, sans-serif" font-size="10" font-weight="400" fill="#666">Smart Living</text>
</svg>
