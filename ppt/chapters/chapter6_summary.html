<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第六章：总结与下一步行动</title>
    <link rel="stylesheet" href="../assets/css/common.css">
    <link rel="stylesheet" href="../assets/css/slide.css">
    <link rel="stylesheet" href="../assets/css/chapter6-enhanced.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 幻灯片1：第6章首页 -->
    <div id="slide-1" class="slide">
        <div class="slide-content center-content">
            <!-- 背景图片 -->
            <div class="hero-background">
                <div class="bg-image"></div>
                <div class="bg-overlay"></div>
                <div class="bg-pattern animate-fadeIn animate-delay-100"></div>
                <div class="bg-gradient animate-fadeIn animate-delay-200"></div>
            </div>

            <!-- 主标题区域 -->
            <div class="hero-section animate-slideUp animate-delay-300" style="margin-bottom: 60px;">
                <div class="chapter-badge animate-scaleIn animate-delay-400">第六章</div>
                <h1 class="hero-title animate-slideUp animate-delay-500">
                    总结与下一步
                </h1>
                <p class="hero-subtitle animate-slideUp animate-delay-600">
                    12个月战略落地与风险管理
                </p>
                <div class="hero-description animate-slideUp animate-delay-700">
                    <p>战略核心是"60/30/10"渠道布局+智能马桶与马桶盖的"按摩+除臭"差异化产品组合，立即启动两项高优先级补充调研，确保战略成功落地。</p>
                </div>
            </div>

            <!-- 核心洞察 -->
            <div class="content-block core-insights animate-slideUp animate-delay-800">
                <h3 class="insights-title">核心洞察</h3>
                <div class="insights-grid">
                    <div class="insight-item animate-scaleIn animate-delay-900">
                        <div class="insight-number">01</div>
                        <div class="insight-content">
                            <p>战略核心是"60/30/10"渠道布局+<strong>智能马桶与马桶盖的</strong>"按摩+除臭"的<span class="insight-highlight">差异化产品组合</span></p>
                        </div>
                    </div>
                    <div class="insight-item animate-scaleIn animate-delay-950">
                        <div class="insight-number">02</div>
                        <div class="insight-content">
                            <p><span class="insight-highlight">关键风险</span>包括竞争加剧、供应链中断、品牌认知建立缓慢</p>
                        </div>
                    </div>
                    <div class="insight-item animate-scaleIn animate-delay-1000">
                        <div class="insight-number">03</div>
                        <div class="insight-content">
                            <p>立即启动Menards线下调研和B2B渠道访谈两项<strong>高优先级行动</strong></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 章节概要 -->
            <div class="content-block chapter-overview animate-slideUp animate-delay-1100">
                <h3 class="overview-title">本章节内容概要</h3>
                <div class="chapter-grid">
                    <div class="chapter-item">
                        <div class="chapter-number">6.1</div>
                        <div class="chapter-content">
                            <h4>战略总结</h4>
                            <p>核心战略要素与风险管理框架</p>
                        </div>
                    </div>
                    <div class="chapter-item">
                        <div class="chapter-number">6.2</div>
                        <div class="chapter-content">
                            <h4>下一步行动</h4>
                            <p>两项高优先级补充调研计划</p>
                        </div>
                    </div>
                    <div class="chapter-item">
                        <div class="chapter-number">6.3</div>
                        <div class="chapter-content">
                            <h4>风险管控</h4>
                            <p>识别关键风险，制定相应预案</p>
                        </div>
                    </div>
                    
                </div>
            </div>
        </div>
    </div>

    <!-- 幻灯片2：战略总结 -->
    <div class="slide" id="slide-2" style="display: none;">
        <div class="slide-header">
            <h1 class="slide-title">战略总结与风险管理</h1>
            <p class="slide-subtitle">在结束前，用一页纸的时间回顾最重要的结论，并正视风险。</p>
        </div>
        <div class="slide-content">
            <div class="content-block" data-animate="slideUp">
                <div class="final-conclusions">
                    <div class="conclusion-insight">
                        <p class="insight-text">我们的战略核心是：通过"60/30/10"的渠道布局，以"按摩+除臭"的差异化产品，抢占北美中端智能马桶市场，并同步拓展智能马桶盖市场。</p>
                    </div>
                    
                    <div class="key-findings" data-animate="cardFloat" data-delay="200">
                        <div class="findings-header">
                            <h3>核心战略要素</h3>
                        </div>
                        <div class="findings-grid">
                            <div class="finding-item market" data-animate="scaleIn" data-delay="300">
                                <div class="finding-icon">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <div class="finding-content">
                                    <h4>渠道配置</h4>
                                    <p>Home Depot 60%, Lowe's 30%, Menards 10%</p>
                                </div>
                            </div>
                            
                            <div class="finding-item channels" data-animate="scaleIn" data-delay="400">
                                <div class="finding-icon">
                                    <i class="fas fa-store"></i>
                                </div>
                                <div class="finding-content">
                                    <h4>产品策略</h4>
                                    <p>智能马桶补齐除臭功能，智能马桶盖强化按摩优势</p>
                                </div>
                            </div>
                            
                            <div class="finding-item competition" data-animate="scaleIn" data-delay="500">
                                <div class="finding-icon">
                                    <i class="fas fa-chess"></i>
                                </div>
                                <div class="finding-content">
                                    <h4>价格定位</h4>
                                    <p>$649-999差异化定价</p>
                                </div>
                            </div>
                            
                            <div class="finding-item product" data-animate="scaleIn" data-delay="600">
                                <div class="finding-icon">
                                    <i class="fas fa-cogs"></i>
                                </div>
                                <div class="finding-content">
                                    <h4>品牌定位</h4>
                                    <p>高价值智能体验普及者</p>
                                </div>
                            </div>
                            
                            <div class="finding-item positioning" data-animate="scaleIn" data-delay="700">
                                <div class="finding-icon">
                                    <i class="fas fa-bullseye"></i>
                                </div>
                                <div class="finding-content">
                                    <h4>关键风险</h4>
                                    <p>竞争加剧、供应链中断、品牌认知建立缓慢</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="success-formula" data-animate="slideUp" data-delay="800">
                        <div class="formula-header">
                            <h3>Bejoan成功公式</h3>
                        </div>
                        <div class="formula-equation">
                            <div class="formula-element">
                                <span class="element-label">产品差异化</span>
                                <span class="element-value">按摩清洗优势</span>
                            </div>
                            <div class="formula-operator">+</div>
                            <div class="formula-element">
                                <span class="element-label">价格竞争力</span>
                                <span class="element-value">$699-$899甜蜜点</span>
                            </div>
                            <div class="formula-operator">+</div>
                            <div class="formula-element">
                                <span class="element-label">渠道策略</span>
                                <span class="element-value">60:30:10配比</span>
                            </div>
                            <div class="formula-operator">=</div>
                            <div class="formula-result">
                                <span class="result-label">市场成功</span>
                                <span class="result-value">1.5%份额目标</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="content-block" data-animate="fadeIn" data-delay="1000">
                <div class="data-source">
                    <p><strong>数据来源：</strong>综合分析基于：①一级数据源：三大渠道318款产品深度采集(Home Depot 248款、Lowe's 105款、Menards 26款，不含Bejoan产品数据)+1.16万+真实用户评论文本挖掘+Bejoan 8款产品功能分析(数据来自亚马逊美国平台)；②二级数据源：Grand View Research、Arizton等7份权威市场研究报告+各公司年报数据；③三级数据源：补充调研计划覆盖数据盲点</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 幻灯片28：下一步行动 -->
    <div class="slide" id="slide-3" style="display: none; padding: 40px; min-height: 100vh; box-sizing: border-box;">
        <div class="slide-header" style="text-align: center; margin-bottom: 40px;">
            <h1 class="slide-title" style="font-size: 28px; color: #1e293b; margin-bottom: 15px; font-weight: 600;">下一步行动：我们建议立即启动两项高优先级补充调研</h1>
            <p class="slide-subtitle" style="font-size: 18px; color: #64748b;">好的战略会以清晰的、可立即执行的下一步行动结尾。</p>
        </div>
        <div class="slide-content" style="max-width: 1200px; margin: 0 auto;">
            <div class="content-block" data-animate="slideUp">
                <div class="action-plan">
                    <div class="plan-insight" style="background: #f8fafc; border-radius: 15px; padding: 25px; margin-bottom: 30px; border: 2px solid #e2e8f0;">
                        <p class="insight-text" style="margin: 0; font-size: 16px; color: #1e293b; line-height: 1.6; text-align: center;">为确保"B方案"的成功落地并弥补当前的数据盲点，我们建议立即启动两项调研，这将为Bejoan在智能马桶和马桶盖两条产品线在新渠道的拓展提供更精准的指引。</p>
                    </div>
                    
                    <div class="research-projects" data-animate="cardFloat" data-delay="200">
                        <div class="projects-grid" style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 30px; margin-bottom: 40px;">
                            <div class="project-item high-priority" data-animate="slideLeft" data-delay="300" style="background: #fef2f2; border-radius: 15px; padding: 25px; border: 2px solid #fecaca; position: relative;">
                                <div class="project-badge" style="position: absolute; top: -10px; right: 20px; background: #ef4444; color: white; padding: 5px 15px; border-radius: 20px; font-size: 12px; font-weight: 600;">
                                    <span class="priority-label">最高优先级</span>
                                </div>
                                <div class="project-content">
                                    <h3 style="margin: 0 0 20px; font-size: 20px; color: #1e293b; font-weight: 600;">Menards线下渠道专项调研</h3>
                                    <div class="project-details" style="space-y: 15px;">
                                        <div class="detail-item" style="display: flex; align-items: flex-start; gap: 12px; margin-bottom: 12px; padding: 12px; background: #fff; border-radius: 8px;">
                                            <i class="fas fa-bullseye" style="color: #ef4444; margin-top: 2px; width: 16px;"></i>
                                            <span style="font-size: 14px; color: #374151; line-height: 1.4;text-align: left;"><strong>目标：</strong>评估其真实的线下市场潜力，制定专门的进入策略，尤其关注智能马桶和马桶盖的市场机会</span>
                                        </div>
                                        <div class="detail-item" style="display: flex; align-items: flex-start; gap: 12px; margin-bottom: 12px; padding: 12px; background: #fff; border-radius: 8px;">
                                            <i class="fas fa-tools" style="color: #ef4444; margin-top: 2px; width: 16px;"></i>
                                            <span style="font-size: 14px; color: #374151; line-height: 1.4;"><strong>方法：</strong>实地门店调研+经销商访谈+消费者调研</span>
                                        </div>
                                        <div class="detail-item" style="display: flex; align-items: flex-start; gap: 12px; margin-bottom: 12px; padding: 12px; background: #fff; border-radius: 8px;">
                                            <i class="fas fa-dollar-sign" style="color: #ef4444; margin-top: 2px; width: 16px;"></i>
                                            <span style="font-size: 14px; color: #374151; line-height: 1.4;"><strong>预算：</strong>$50,000</span>
                                        </div>
                                        <div class="detail-item" style="display: flex; align-items: flex-start; gap: 12px; margin-bottom: 12px; padding: 12px; background: #fff; border-radius: 8px;">
                                            <i class="fas fa-clock" style="color: #ef4444; margin-top: 2px; width: 16px;"></i>
                                            <span style="font-size: 14px; color: #374151; line-height: 1.4;"><strong>周期：</strong>6周</span>
                                        </div>
                                        <div class="detail-item" style="display: flex; align-items: flex-start; gap: 12px; margin-bottom: 12px; padding: 12px; background: #fff; border-radius: 8px;">
                                            <i class="fas fa-file-alt" style="color: #ef4444; margin-top: 2px; width: 16px;"></i>
                                            <span style="font-size: 14px; color: #374151; line-height: 1.4;"><strong>产出：</strong>Menards渠道进入策略报告</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="project-item medium-priority" data-animate="slideLeft" data-delay="400" style="background: #f0f9ff; border-radius: 15px; padding: 25px; border: 2px solid #bfdbfe; position: relative;">
                                <div class="project-badge" style="position: absolute; top: -10px; right: 20px; background: #3b82f6; color: white; padding: 5px 15px; border-radius: 20px; font-size: 12px; font-weight: 600;">
                                    <span class="priority-label">高优先级</span>
                                </div>
                                <div class="project-content">
                                    <h3 style="margin: 0 0 20px; font-size: 20px; color: #1e293b; font-weight: 600;">B2B渠道（建筑商、设计师）访谈</h3>
                                    <div class="project-details" style="space-y: 15px;">
                                        <div class="detail-item" style="display: flex; align-items: flex-start; gap: 12px; margin-bottom: 12px; padding: 12px; background: #fff; border-radius: 8px;">
                                            <i class="fas fa-bullseye" style="color: #3b82f6; margin-top: 2px; width: 16px;"></i>
                                            <span style="font-size: 14px; color: #374151; line-height: 1.4;text-align: left;"><strong>目标：</strong>探索商业采购市场的机会，为未来的增长开辟新路径，了解智能马桶和马桶盖在B2B市场的需求和痛点</span>
                                        </div>
                                        <div class="detail-item" style="display: flex; align-items: flex-start; gap: 12px; margin-bottom: 12px; padding: 12px; background: #fff; border-radius: 8px;">
                                            <i class="fas fa-tools" style="color: #3b82f6; margin-top: 2px; width: 16px;"></i>
                                            <span style="font-size: 14px; color: #374151; line-height: 1.4;"><strong>方法：</strong>深度访谈+问卷调研+市场测试</span>
                                        </div>
                                        <div class="detail-item" style="display: flex; align-items: flex-start; gap: 12px; margin-bottom: 12px; padding: 12px; background: #fff; border-radius: 8px;">
                                            <i class="fas fa-dollar-sign" style="color: #3b82f6; margin-top: 2px; width: 16px;"></i>
                                            <span style="font-size: 14px; color: #374151; line-height: 1.4;"><strong>预算：</strong>$30,000</span>
                                        </div>
                                        <div class="detail-item" style="display: flex; align-items: flex-start; gap: 12px; margin-bottom: 12px; padding: 12px; background: #fff; border-radius: 8px;">
                                            <i class="fas fa-clock" style="color: #3b82f6; margin-top: 2px; width: 16px;"></i>
                                            <span style="font-size: 14px; color: #374151; line-height: 1.4;"><strong>周期：</strong>4周</span>
                                        </div>
                                        <div class="detail-item" style="display: flex; align-items: flex-start; gap: 12px; margin-bottom: 12px; padding: 12px; background: #fff; border-radius: 8px;">
                                            <i class="fas fa-file-alt" style="color: #3b82f6; margin-top: 2px; width: 16px;"></i>
                                            <span style="font-size: 14px; color: #374151; line-height: 1.4;"><strong>产出：</strong>B2B市场机会评估报告</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                        </div>
                    </div>
                    
                    <div class="research-value" data-animate="slideUp" data-delay="600">
                        <div class="value-header">
                            <h3>调研价值</h3>
                        </div>
                        <div class="value-points">
                            <div class="value-point">
                                <div class="point-icon">
                                    <i class="fas fa-map-marked-alt"></i>
                                </div>
                                <div class="point-content">
                                    <h4>填补Menards线下数据空白</h4>
                                    <p>避免战略盲点</p>
                                </div>
                            </div>
                            <div class="value-point">
                                <div class="point-icon">
                                    <i class="fas fa-expand-arrows-alt"></i>
                                </div>
                                <div class="point-content">
                                    <h4>发现B2B市场新机会</h4>
                                    <p>扩大总可达市场(TAM)</p>
                                </div>
                            </div>
                            <div class="value-point">
                                <div class="point-icon">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <div class="point-content">
                                    <h4>为战略执行提供更精准的数据支撑</h4>
                                    <p>这两项行动将为我们的战略执行提供更精准的导航，并可能发现新的增长引擎</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="content-block" data-animate="fadeIn" data-delay="800">
                <div class="data-source">
                    <p><strong>数据来源：</strong>基于当前数据盲点分析及调研需求评估；预算基于类似调研项目的市场价格；Bejoan产品数据来自亚马逊美国平台。</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 幻灯片29：风险管控 -->
    <div class="slide" id="slide-4" style="display: none; padding: 40px; min-height: 100vh; box-sizing: border-box;">
        <div class="slide-header" style="text-align: center; margin-bottom: 40px;">
            <h1 class="slide-title" style="font-size: 28px; color: #1e293b; margin-bottom: 15px; font-weight: 600;">风险管控：识别五大关键风险，制定相应预案</h1>
            <p class="slide-subtitle" style="font-size: 18px; color: #64748b;">优秀的战略不仅要追求成功，更要为可能的挫折做好准备。</p>
        </div>
        <div class="slide-content" style="max-width: 1200px; margin: 0 auto;">
            <div class="content-block" data-animate="slideUp">
                <div class="risk-management">
                    <div class="risk-insight" style="background: #f8fafc; border-radius: 15px; padding: 25px; margin-bottom: 30px; border: 2px solid #e2e8f0;">
                        <p class="insight-text" style="margin: 0; font-size: 16px; color: #1e293b; line-height: 1.6; text-align: center;">我们识别了五大关键风险，并为每个风险制定了具体的预案。这确保了即使面临挫折，Bejoan也能快速调整并继续前进。</p>
                    </div>
                    
                    <div class="risk-matrix" data-animate="cardFloat" data-delay="200">
                        <div class="matrix-header" style="text-align: center; margin-bottom: 30px;">
                            <h3 style="margin: 0; font-size: 22px; color: #1e293b; font-weight: 600;">风险评估矩阵</h3>
                        </div>
                        <div class="risk-items" style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px;">
                            <div class="risk-item high" data-animate="slideLeft" data-delay="300" style="background: #fef2f2; border-radius: 12px; padding: 20px; border: 2px solid #fecaca;align-items: center;">
                                <div class="risk-level" style="display: flex; align-items: center; margin-bottom: 15px;">
                                    <span class="level-indicator high" style="width: 12px; height: 12px; background: #ef4444; border-radius: 50%;"></span>
                                    <span class="level-text" style="font-size: 14px; color: #ef4444; font-weight: 600;">高风险</span>
                                </div>
                                <div class="risk-content">
                                    <h4 style="margin: 0 0 10px; font-size: 18px; color: #1e293b; font-weight: 600;">渠道准入门槛</h4>
                                    <p class="risk-description" style="margin: 0 0 12px; font-size: 14px; color: #64748b; line-height: 1.4;">Home Depot/Lowe's可能设置更高的准入标准</p>
                                    <div class="risk-mitigation" style="background: #fff; padding: 10px; border-radius: 6px; font-size: 13px; color: #374151; line-height: 1.4;">
                                        <strong>预案：</strong>提前建立经销商关系，考虑第三方平台作为跳板
                                    </div>
                                </div>
                            </div>
                            
                            <div class="risk-item high" data-animate="slideLeft" data-delay="400" style="background: #fef2f2; border-radius: 12px; padding: 20px; border: 2px solid #fecaca;align-items: center;">
                                <div class="risk-level" style="display: flex; align-items: center; margin-bottom: 15px;">
                                    <span class="level-indicator high" style="width: 12px; height: 12px; background: #ef4444; border-radius: 50%;"></span>
                                    <span class="level-text" style="font-size: 14px; color: #ef4444; font-weight: 600;">高风险</span>
                                </div>
                                <div class="risk-content">
                                    <h4 style="margin: 0 0 10px; font-size: 18px; color: #1e293b; font-weight: 600;">产品功能缺陷</h4>
                                    <p class="risk-description" style="margin: 0 0 12px; font-size: 14px; color: #64748b; line-height: 1.4;">智能马桶和马桶盖产品线除臭功能补齐不及时或效果不佳</p>
                                    <div class="risk-mitigation" style="background: #fff; padding: 10px; border-radius: 6px; font-size: 13px; color: #374151; line-height: 1.4;">
                                        <strong>预案：</strong>分别为两条产品线建立技术供应商备选方案，确保功能及时上线
                                    </div>
                                </div>
                            </div>
                            
                            <div class="risk-item medium" data-animate="slideLeft" data-delay="500" style="background: #fef9e7; border-radius: 12px; padding: 20px; border: 2px solid #fde68a;align-items: center;">
                                <div class="risk-level" style="display: flex; align-items: center; margin-bottom: 15px;">
                                    <span class="level-indicator medium" style="width: 12px; height: 12px; background: #f59e0b; border-radius: 50%;"></span>
                                    <span class="level-text" style="font-size: 14px; color: #f59e0b; font-weight: 600;">中风险</span>
                                </div>
                                <div class="risk-content">
                                    <h4 style="margin: 0 0 10px; font-size: 18px; color: #1e293b; font-weight: 600;">竞争对手反击</h4>
                                    <p class="risk-description" style="margin: 0 0 12px; font-size: 14px; color: #64748b; line-height: 1.4;">HOROW、ANZZI等品牌可能降价或升级产品</p>
                                    <div class="risk-mitigation" style="background: #fff; padding: 10px; border-radius: 6px; font-size: 13px; color: #374151; line-height: 1.4;">
                                        <strong>预案：</strong>强化双产品线按摩清洗差异化，避免正面价格战
                                    </div>
                                </div>
                            </div>
                            
                            <div class="risk-item medium" data-animate="slideLeft" data-delay="600" style="background: #fef9e7; border-radius: 12px; padding: 20px; border: 2px solid #fde68a;align-items: center;">
                                <div class="risk-level" style="display: flex; align-items: center; margin-bottom: 15px;">
                                    <span class="level-indicator medium" style="width: 12px; height: 12px; background: #f59e0b; border-radius: 50%;"></span>
                                    <span class="level-text" style="font-size: 14px; color: #f59e0b; font-weight: 600;">中风险</span>
                                </div>
                                <div class="risk-content">
                                    <h4 style="margin: 0 0 10px; font-size: 18px; color: #1e293b; font-weight: 600;">市场接受度不足</h4>
                                    <p class="risk-description" style="margin: 0 0 12px; font-size: 14px; color: #64748b; line-height: 1.4;">北美消费者对按摩功能的接受度低于预期</p>
                                    <div class="risk-mitigation" style="background: #fff; padding: 10px; border-radius: 6px; font-size: 13px; color: #374151; line-height: 1.4;">
                                        <strong>预案：</strong>调整营销重点至健康和舒适价值，增加体验营销
                                    </div>
                                </div>
                            </div>
                            
                            <div class="risk-item low" data-animate="slideLeft" data-delay="700" style="background: #f0fdf4; border-radius: 12px; padding: 20px; border: 2px solid #bbf7d0;align-items: center;">
                                <div class="risk-level" style="display: flex; align-items: center; margin-bottom: 15px;">
                                    <span class="level-indicator low" style="width: 12px; height: 12px; background: #10b981; border-radius: 50%;"></span>
                                    <span class="level-text" style="font-size: 14px; color: #10b981; font-weight: 600;">低风险</span>
                                </div>
                                <div class="risk-content">
                                    <h4 style="margin: 0 0 10px; font-size: 18px; color: #1e293b; font-weight: 600;">供应链中断</h4>
                                    <p class="risk-description" style="margin: 0 0 12px; font-size: 14px; color: #64748b; line-height: 1.4;">生产或物流环节出现问题</p>
                                    <div class="risk-mitigation" style="background: #fff; padding: 10px; border-radius: 6px; font-size: 13px; color: #374151; line-height: 1.4;">
                                        <strong>预案：</strong>建立多元化供应商网络，保持安全库存
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="contingency-plan" data-animate="slideUp" data-delay="800">
                        <div class="plan-header">
                            <h3>应急预案总览</h3>
                        </div>
                        <div class="plan-content">
                            <p>如果前90天的关键指标未达预期，我们建议立即启动"战略A"（市场试点），降低投入规模，专注于学习和优化。</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="content-block" data-animate="fadeIn" data-delay="1000">
                <div class="data-source">
                    <p><strong>数据来源：</strong>基于类似品牌进入北美市场的失败案例分析；风险评估基于行业专家访谈</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 幻灯片30：结语 -->
    <div class="slide" id="slide-5" style="display: none; padding: 40px; min-height: 100vh; box-sizing: border-box; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
        <div class="slide-content center-content" style="display: flex; flex-direction: column; justify-content: center; align-items: center; min-height: 100vh; text-align: center;">
            <div class="content-block" data-animate="fadeIn">
                <div class="closing-statement">
                    <h1 class="closing-title" data-animate="slideUp" data-delay="200" style="font-size: 48px; color: white; margin-bottom: 30px; font-weight: 700;">北美之路，始于足下</h1>
                    <p class="closing-subtitle" data-animate="slideUp" data-delay="400" style="font-size: 24px; color: rgba(255,255,255,0.9); margin-bottom: 50px; line-height: 1.4;">数据为我们指明了方向，策略为我们铺平了道路，现在需要的是行动的勇气。</p>
                    
                    <div class="closing-quote" data-animate="scaleIn" data-delay="600" style="background: rgba(255,255,255,0.1); border-radius: 20px; padding: 40px; margin-bottom: 50px; backdrop-filter: blur(10px);">
                        <blockquote style="margin: 0; font-size: 28px; color: white; font-style: italic; line-height: 1.5;">
                            "在正确的时间，用正确的策略，进入正确的市场。<br>
                            Bejoan的北美征程，正当其时。"
                        </blockquote>
                    </div>
                    
                    <div class="closing-cta" data-animate="bounceIn" data-delay="800">
                        <p class="cta-text" style="font-size: 20px; color: white; margin-bottom: 30px; font-weight: 500;">让我们一起，将数据洞察转化为市场成功。</p>
                        <div class="cta-contact" style="background: rgba(255,255,255,0.1); border-radius: 15px; padding: 25px; backdrop-filter: blur(10px);">
                            <p style="font-size: 18px; color: white; margin: 0 0 10px; font-weight: 500;">感谢您的时间与信任</p>
                            <p class="contact-info" style="font-size: 16px; color: rgba(255,255,255,0.8); margin: 0;">如有任何问题，请随时联系我们的团队</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 幻灯片导航逻辑
        let currentSlide = 1;
        const totalSlides = 5;
        
        function showSlide(slideNumber) {
            document.querySelectorAll('.slide').forEach(slide => {
                slide.style.display = 'none';
            });
            
            const targetSlide = document.getElementById(`slide-${slideNumber}`);
            if (targetSlide) {
                targetSlide.style.display = 'block';
                triggerAnimations(targetSlide);
            }
            
            currentSlide = slideNumber;
        }
        
        function triggerAnimations(slide) {
            const animatedElements = slide.querySelectorAll('[data-animate]');
            animatedElements.forEach((element, index) => {
                const delay = parseInt(element.dataset.delay) || 0;
                setTimeout(() => {
                    element.classList.add('animated');
                }, delay);
            });
        }
        
        // 监听URL hash变化
        function handleHashChange() {
            const hash = window.location.hash;
            const match = hash.match(/#slide-(\d+)/);
            if (match) {
                const slideNumber = parseInt(match[1]);
                if (slideNumber >= 1 && slideNumber <= totalSlides) {
                    showSlide(slideNumber);
                }
            }
        }
        
        // 初始化
        window.addEventListener('load', () => {
            handleHashChange();
            if (!window.location.hash) {
                showSlide(1);
            }
        });
        
        window.addEventListener('hashchange', handleHashChange);
        
        // 导出函数供父页面调用
        window.triggerAnimations = () => {
            const currentSlideElement = document.getElementById(`slide-${currentSlide}`);
            if (currentSlideElement) {
                triggerAnimations(currentSlideElement);
            }
        };
    </script>
</body>
</html>
