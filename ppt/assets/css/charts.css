/* 图表和可视化样式 */

/* 通用图表容器 */
.chart-container {
    position: relative;
    background: var(--background-primary);
    border-radius: 16px;
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
    margin: var(--spacing-lg) 0;
    transition: all var(--transition-base);
}

.chart-container:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-medium);
}

.chart-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-md);
    border-bottom: 2px solid var(--border-color);
}

.chart-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-sm) 0;
}

.chart-subtitle {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin: 0;
}

/* Canvas图表样式 */
.chart-canvas {
    max-height: 400px;
    width: 100%;
    margin: var(--spacing-lg) 0;
}

/* 交互式图表控制 */
.chart-controls {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    margin: var(--spacing-lg) 0;
    flex-wrap: wrap;
}

.chart-control-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--background-secondary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: all var(--transition-base);
}

.chart-control-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.chart-control-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* 图表图例 */
.chart-legend {
    display: flex;
    justify-content: center;
    gap: var(--spacing-lg);
    margin: var(--spacing-lg) 0;
    flex-wrap: wrap;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 4px;
    flex-shrink: 0;
}

/* 数据标签 */
.chart-data-labels {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--spacing-md);
    margin: var(--spacing-lg) 0;
}

.data-label {
    text-align: center;
    padding: var(--spacing-md);
    background: var(--background-secondary);
    border-radius: 8px;
    transition: all var(--transition-base);
}

.data-label:hover {
    transform: scale(1.05);
    background: var(--light-blue);
}

.data-label-value {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.data-label-text {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
}

/* 进度条图表 */
.progress-chart {
    margin: var(--spacing-lg) 0;
}

.progress-item {
    margin-bottom: var(--spacing-lg);
}

.progress-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.progress-name {
    font-size: var(--font-size-base);
    font-weight: 500;
    color: var(--text-primary);
}

.progress-value {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--primary-color);
}

.progress-bar {
    height: 12px;
    background: var(--background-secondary);
    border-radius: 6px;
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    border-radius: 6px;
    transition: width 1s ease-in-out;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* 饼图样式 */
.pie-chart-container {
    display: flex;
    align-items: center;
    gap: var(--spacing-xl);
}

.pie-chart {
    flex-shrink: 0;
}

.pie-legend {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.pie-legend-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
    background: var(--background-secondary);
    border-radius: 8px;
    transition: all var(--transition-base);
}

.pie-legend-item:hover {
    background: var(--light-blue);
    transform: translateX(4px);
}

.pie-legend-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.pie-legend-value {
    font-weight: 600;
    color: var(--primary-color);
}

/* 热力图样式 */
.heatmap-container {
    overflow-x: auto;
    margin: var(--spacing-lg) 0;
}

.heatmap-grid {
    display: grid;
    gap: 2px;
    min-width: 600px;
}

.heatmap-cell {
    padding: var(--spacing-sm);
    text-align: center;
    border-radius: 4px;
    font-size: var(--font-size-xs);
    font-weight: 500;
    transition: all var(--transition-base);
    cursor: pointer;
}

.heatmap-cell:hover {
    transform: scale(1.1);
    z-index: 10;
    box-shadow: var(--shadow-medium);
}

/* 数据密度颜色 */
.heatmap-cell.density-0 { background: #f8f9fa; color: #6c757d; }
.heatmap-cell.density-1 { background: #e3f2fd; color: #1976d2; }
.heatmap-cell.density-2 { background: #bbdefb; color: #1565c0; }
.heatmap-cell.density-3 { background: #90caf9; color: #0d47a1; }
.heatmap-cell.density-4 { background: #64b5f6; color: #0d47a1; }
.heatmap-cell.density-5 { background: #42a5f5; color: white; }

/* 仪表盘样式 */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
    margin: var(--spacing-lg) 0;
}

.dashboard-metric {
    background: var(--background-primary);
    border-radius: 12px;
    padding: var(--spacing-lg);
    text-align: center;
    border: 1px solid var(--border-color);
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.dashboard-metric::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.dashboard-metric:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-medium);
}

.metric-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto var(--spacing-md);
    background: var(--light-blue);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    color: var(--primary-color);
}

.metric-value {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.metric-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
}

.metric-change {
    font-size: var(--font-size-xs);
    font-weight: 500;
    padding: 2px 8px;
    border-radius: 12px;
}

.metric-change.positive {
    background: rgba(0, 166, 81, 0.1);
    color: var(--secondary-color);
}

.metric-change.negative {
    background: rgba(255, 107, 53, 0.1);
    color: var(--accent-color);
}

/* 价格段分析样式 */
.price-segment-analysis {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
    margin: var(--spacing-xl) 0;
}

.segment-card {
    background: var(--background-primary);
    border-radius: 12px;
    padding: var(--spacing-lg);
    border: 2px solid var(--border-color);
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.segment-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    transition: all var(--transition-base);
}

.segment-card.economic::before {
    background: linear-gradient(90deg, #17a2b8, #138496);
}

.segment-card.mid-range::before {
    background: linear-gradient(90deg, #0073e6, #005bb5);
}

.segment-card.premium::before {
    background: linear-gradient(90deg, #ff6b35, #e55a2b);
}

.segment-card.highlight {
    border-color: var(--primary-color);
    box-shadow: 0 8px 25px rgba(0, 115, 230, 0.15);
    transform: translateY(-2px);
}

.segment-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-medium);
}

.segment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.segment-header h4 {
    margin: 0;
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
}

.segment-trend {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: var(--font-size-xs);
    font-weight: 500;
    background: var(--light-blue);
    color: var(--primary-color);
}

.segment-metrics {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.segment-metrics .metric {
    text-align: center;
    padding: var(--spacing-sm);
    background: var(--background-secondary);
    border-radius: 8px;
}

.segment-metrics .metric-label {
    display: block;
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
}

.segment-metrics .metric-value {
    display: block;
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--text-primary);
}

.segment-strategy {
    text-align: center;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: 20px;
    font-size: var(--font-size-sm);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.segment-strategy.avoid {
    background: rgba(255, 107, 53, 0.1);
    color: var(--accent-color);
    border: 1px solid rgba(255, 107, 53, 0.3);
}

.segment-strategy.target {
    background: rgba(0, 166, 81, 0.1);
    color: var(--secondary-color);
    border: 1px solid rgba(0, 166, 81, 0.3);
    animation: pulse 2s infinite;
}

.segment-strategy.future {
    background: rgba(0, 115, 230, 0.1);
    color: var(--primary-color);
    border: 1px solid rgba(0, 115, 230, 0.3);
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* 热力图增强样式 */
.heatmap-enhanced {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 4px;
    margin: var(--spacing-lg) 0;
    padding: var(--spacing-md);
    background: var(--background-secondary);
    border-radius: 12px;
}

.heatmap-cell-enhanced {
    aspect-ratio: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-radius: 8px;
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: white;
    text-align: center;
    transition: all var(--transition-base);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.heatmap-cell-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s;
}

.heatmap-cell-enhanced:hover::before {
    transform: translateX(100%);
}

.heatmap-cell-enhanced:hover {
    transform: scale(1.05);
    z-index: 10;
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

.heatmap-cell-enhanced .cell-value {
    font-size: var(--font-size-lg);
    font-weight: 700;
    margin-bottom: 2px;
}

.heatmap-cell-enhanced .cell-label {
    font-size: var(--font-size-xs);
    opacity: 0.9;
}

/* 树状图样式 */
.treemap-container {
    position: relative;
    width: 100%;
    height: 300px;
    background: var(--background-secondary);
    border-radius: 12px;
    overflow: hidden;
}

.treemap-cell {
    position: absolute;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: white;
    font-weight: 600;
    text-align: center;
    border: 2px solid white;
    transition: all var(--transition-base);
    cursor: pointer;
}

.treemap-cell:hover {
    transform: scale(1.02);
    z-index: 10;
    box-shadow: 0 8px 25px rgba(0,0,0,0.3);
}

.treemap-cell .cell-title {
    font-size: var(--font-size-base);
    font-weight: 700;
    margin-bottom: 4px;
}

.treemap-cell .cell-value {
    font-size: var(--font-size-sm);
    opacity: 0.9;
}

/* 响应式图表 */
@media (max-width: 768px) {
    .chart-container {
        padding: var(--spacing-md);
    }

    .pie-chart-container {
        flex-direction: column;
    }

    .chart-controls {
        gap: var(--spacing-sm);
    }

    .chart-control-btn {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: var(--font-size-xs);
    }

    .dashboard-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);
    }

    .heatmap-container {
        font-size: var(--font-size-xs);
    }

    .price-segment-analysis {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .segment-metrics {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }
}

/* Lowe's分析页面样式 */
.lowes-analysis {
    max-width: 1600px;
    margin: 0 auto;
}

.lowes-positioning {
    margin: var(--spacing-xl) 0;
}

.positioning-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin: var(--spacing-lg) 0;
}

.positioning-item {
    background: var(--background-primary);
    border-radius: 12px;
    padding: var(--spacing-lg);
    border: 2px solid var(--border-color);
    transition: all var(--transition-base);
    text-align: center;
}

.positioning-item:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-medium);
}

.positioning-item.health {
    border-color: #00a651;
}

.positioning-item.comfort {
    border-color: #0073e6;
}

.positioning-item.smart {
    border-color: #ff6b35;
}

.positioning-item.service {
    border-color: #ffc107;
}

.positioning-item .item-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto var(--spacing-md);
    background: var(--light-blue);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    color: var(--primary-color);
}

.positioning-item h4 {
    margin: 0 0 var(--spacing-sm) 0;
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
}

.positioning-item p {
    margin: 0 0 var(--spacing-md) 0;
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    line-height: 1.5;
}

.item-metric {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--primary-color);
    background: var(--light-blue);
    padding: 4px 12px;
    border-radius: 20px;
    display: inline-block;
}

/* 客户画像样式 */
.customer-profile {
    margin: var(--spacing-xl) 0;
}

.profile-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    margin: var(--spacing-lg) 0;
}

.profile-segment {
    background: var(--background-secondary);
    border-radius: 12px;
    padding: var(--spacing-lg);
    border-left: 4px solid var(--primary-color);
}

.profile-segment.primary {
    border-left-color: var(--primary-color);
}

.profile-segment.secondary {
    border-left-color: var(--secondary-color);
}

.profile-segment h4 {
    margin: 0 0 var(--spacing-md) 0;
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
}

.segment-details {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.detail-item {
    background: var(--background-primary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 6px;
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

/* 品牌竞争格局样式 */
.brand-landscape {
    max-width: 1400px;
    margin: 0 auto;
}

.brand-analysis-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-lg);
    margin: var(--spacing-xl) 0;
}

.channel-brands {
    background: var(--background-primary);
    border-radius: 16px;
    padding: var(--spacing-xl);
    border: 2px solid var(--border-color);
    transition: all var(--transition-base);
}

.channel-brands:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.channel-brands.home-depot {
    border-top: 4px solid #0073e6;
}

.channel-brands.lowes {
    border-top: 4px solid #00a651;
}

.channel-brands.menards {
    border-top: 4px solid #ff6b35;
}

.channel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 2px solid var(--border-color);
}

.channel-header h4 {
    margin: 0;
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
}

.channel-count {
    background: var(--light-blue);
    color: var(--primary-color);
    padding: 4px 12px;
    border-radius: 20px;
    font-size: var(--font-size-sm);
    font-weight: 600;
}

.brand-tiers {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.tier {
    background: var(--background-secondary);
    border-radius: 12px;
    padding: var(--spacing-md);
    border-left: 4px solid;
}

.tier.premium {
    border-left-color: #ff6b35;
}

.tier.mid-range {
    border-left-color: #0073e6;
}

.tier.economy {
    border-left-color: #17a2b8;
}

.tier.smart-seats {
    border-left-color: #00a651;
}

.tier.smart-toilets {
    border-left-color: #ffc107;
}

.tier h5 {
    margin: 0 0 var(--spacing-sm) 0;
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--text-primary);
}

.brand-list {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-sm);
}

.brand-item {
    background: var(--background-primary);
    padding: 4px 8px;
    border-radius: 6px;
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.brand-item.leader {
    background: var(--primary-color);
    color: white;
    font-weight: 600;
}

.brand-item.opportunity {
    background: var(--secondary-color);
    color: white;
    font-weight: 600;
    animation: pulse 2s infinite;
}

.tier-metrics {
    display: flex;
    gap: var(--spacing-md);
}

.tier-metrics .metric {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    background: var(--background-primary);
    padding: 2px 8px;
    border-radius: 4px;
}

/* 进入策略时间线样式 */
.entry-strategy {
    max-width: 1400px;
    margin: 0 auto;
}

.strategy-timeline {
    margin: var(--spacing-xl) 0;
}

.timeline-phases {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
    margin: var(--spacing-xl) 0;
}

.phase {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-lg);
    background: var(--background-primary);
    border-radius: 16px;
    padding: var(--spacing-xl);
    border: 2px solid var(--border-color);
    transition: all var(--transition-base);
    position: relative;
}

.phase:hover {
    transform: translateX(8px);
    box-shadow: var(--shadow-medium);
}

.phase.phase-1 {
    border-left: 6px solid #00a651;
}

.phase.phase-2 {
    border-left: 6px solid #0073e6;
}

.phase.phase-3 {
    border-left: 6px solid #ff6b35;
}

.phase-number {
    width: 60px;
    height: 60px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    font-weight: 700;
    flex-shrink: 0;
}

.phase-content {
    flex: 1;
}

.phase-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
}

.phase-header h4 {
    margin: 0;
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
}

.phase-duration {
    background: var(--light-blue);
    color: var(--primary-color);
    padding: 4px 12px;
    border-radius: 20px;
    font-size: var(--font-size-sm);
    font-weight: 600;
}

.phase-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
}

.phase-rationale,
.phase-actions {
    background: var(--background-secondary);
    border-radius: 8px;
    padding: var(--spacing-md);
}

.phase-rationale h5,
.phase-actions h5 {
    margin: 0 0 var(--spacing-sm) 0;
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--text-primary);
}

.phase-rationale ul,
.phase-actions ul {
    margin: 0;
    padding-left: var(--spacing-md);
    list-style-type: disc;
}

.phase-rationale li,
.phase-actions li {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    line-height: 1.5;
    margin-bottom: var(--spacing-xs);
}

.phase-metrics {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    justify-content: center;
}

.phase-metrics .metric {
    background: var(--background-primary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 6px;
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--primary-color);
    text-align: center;
    border: 1px solid var(--border-color);
}

.strategy-summary {
    background: var(--background-secondary);
    border-radius: 16px;
    padding: var(--spacing-xl);
    margin: var(--spacing-xl) 0;
    border: 2px solid var(--primary-color);
}

.summary-header h3 {
    margin: 0 0 var(--spacing-lg) 0;
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
    text-align: center;
}

.summary-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-lg);
}

.summary-metric {
    text-align: center;
    background: var(--background-primary);
    border-radius: 12px;
    padding: var(--spacing-lg);
    border: 1px solid var(--border-color);
}

.summary-metric .metric-value {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.summary-metric .metric-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

/* 渠道策略矩阵样式 */
.channel-strategy-matrix {
    max-width: 1600px;
    margin: 0 auto;
}

.strategy-matrix-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
    gap: var(--spacing-xl);
    margin: var(--spacing-xl) 0;
}

.matrix-channel {
    background: var(--background-primary);
    border-radius: 16px;
    padding: var(--spacing-xl);
    border: 3px solid var(--border-color);
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.matrix-channel::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    transition: all var(--transition-base);
}

.matrix-channel.home-depot::before {
    background: linear-gradient(90deg, #0073e6, #005bb5);
}

.matrix-channel.lowes::before {
    background: linear-gradient(90deg, #00a651, #008a44);
}

.matrix-channel.menards::before {
    background: linear-gradient(90deg, #ff6b35, #e55a2b);
}

.matrix-channel:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0,0,0,0.15);
}

.matrix-channel:hover::before {
    height: 8px;
}

.channel-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 2px solid var(--border-color);
}

.channel-icon {
    width: 60px;
    height: 60px;
    background: var(--light-blue);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    color: var(--primary-color);
    flex-shrink: 0;
}

.channel-info h3 {
    margin: 0 0 var(--spacing-xs) 0;
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--text-primary);
}

.channel-tag {
    background: var(--primary-color);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.strategy-dimensions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.dimension {
    background: var(--background-secondary);
    border-radius: 12px;
    padding: var(--spacing-lg);
    border-left: 4px solid;
}

.dimension.product {
    border-left-color: #0073e6;
}

.dimension.pricing {
    border-left-color: #00a651;
}

.dimension.marketing {
    border-left-color: #ff6b35;
}

.dimension h4 {
    margin: 0 0 var(--spacing-md) 0;
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.dimension h4::before {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: currentColor;
}

.dimension-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.strategy-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm);
    background: var(--background-primary);
    border-radius: 8px;
    border: 1px solid var(--border-color);
    transition: all var(--transition-base);
}

.strategy-item:hover {
    transform: translateX(4px);
    border-color: var(--primary-color);
}

.strategy-item.primary {
    border-color: var(--primary-color);
    background: var(--light-blue);
}

.strategy-item .item-label {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-secondary);
    min-width: 80px;
}

.strategy-item .item-value {
    font-size: var(--font-size-sm);
    color: var(--text-primary);
    text-align: right;
    flex: 1;
}

.channel-metrics {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    background: var(--background-secondary);
    border-radius: 12px;
    border: 2px solid var(--border-color);
}

.channel-metrics .metric {
    text-align: center;
    padding: var(--spacing-sm);
    background: var(--background-primary);
    border-radius: 8px;
}

.channel-metrics .metric-label {
    display: block;
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.channel-metrics .metric-value {
    display: block;
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--primary-color);
}

.strategy-comparison-chart {
    margin: var(--spacing-xl) 0;
    background: var(--background-secondary);
    border-radius: 16px;
    padding: var(--spacing-xl);
    border: 2px solid var(--border-color);
}

.comparison-header {
    text-align: center;
    margin-bottom: var(--spacing-lg);
}

.comparison-header h3 {
    margin: 0;
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
}

/* 风险控制机制样式 */
.risk-mitigation {
    background: var(--background-secondary);
    border-radius: 16px;
    padding: var(--spacing-xl);
    margin: var(--spacing-xl) 0;
    border: 2px solid var(--primary-color);
}

.mitigation-header {
    text-align: center;
    margin-bottom: var(--spacing-lg);
}

.mitigation-header h3 {
    margin: 0;
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
}

.mitigation-items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.mitigation-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    background: var(--background-primary);
    border-radius: 12px;
    padding: var(--spacing-lg);
    border: 1px solid var(--border-color);
    transition: all var(--transition-base);
}

.mitigation-item:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-medium);
    border-color: var(--primary-color);
}

.mitigation-item .item-icon {
    width: 50px;
    height: 50px;
    background: var(--light-blue);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    color: var(--primary-color);
    flex-shrink: 0;
}

.mitigation-item .item-content h4 {
    margin: 0 0 var(--spacing-sm) 0;
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--text-primary);
}

.mitigation-item .item-content p {
    margin: 0;
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    line-height: 1.5;
}

/* 营销策略卡片样式 */
.channel-strategies {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-lg);
    margin: var(--spacing-xl) 0;
}

.strategy-card {
    background: var(--background-primary);
    border-radius: 16px;
    padding: var(--spacing-xl);
    border: 3px solid var(--border-color);
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.strategy-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    transition: all var(--transition-base);
}

.strategy-card.home-depot::before {
    background: linear-gradient(90deg, #0073e6, #005bb5);
}

.strategy-card.lowes::before {
    background: linear-gradient(90deg, #00a651, #008a44);
}

.strategy-card.menards::before {
    background: linear-gradient(90deg, #ff6b35, #e55a2b);
}

.strategy-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0,0,0,0.15);
}

.strategy-card:hover::before {
    height: 8px;
}

.strategy-header {
    text-align: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 2px solid var(--border-color);
}

.strategy-header h3 {
    margin: 0 0 var(--spacing-sm) 0;
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--text-primary);
}

.strategy-subtitle {
    margin: 0;
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-style: italic;
}

.strategy-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.strategy-element {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm);
    background: var(--background-secondary);
    border-radius: 8px;
    border: 1px solid var(--border-color);
    transition: all var(--transition-base);
}

.strategy-element:hover {
    transform: translateX(4px);
    border-color: var(--primary-color);
}

.strategy-element .element-label {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-secondary);
    min-width: 80px;
}

.strategy-element .element-value {
    font-size: var(--font-size-sm);
    color: var(--text-primary);
    text-align: right;
    flex: 1;
}

/* Menards洞察卡片样式 */
.menards-insights {
    margin: var(--spacing-xl) 0;
}

.insight-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-lg);
}

.insight-card {
    background: var(--background-primary);
    border-radius: 12px;
    padding: var(--spacing-lg);
    border: 2px solid var(--border-color);
    transition: all var(--transition-base);
    text-align: center;
}

.insight-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-medium);
}

.insight-card.advantage {
    border-color: var(--secondary-color);
}

.insight-card.opportunity {
    border-color: var(--primary-color);
}

.insight-card.strategy {
    border-color: var(--accent-color);
}

.insight-card .card-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto var(--spacing-md);
    background: var(--light-blue);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    color: var(--primary-color);
}

.insight-card h4 {
    margin: 0 0 var(--spacing-sm) 0;
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--text-primary);
}

.insight-card p {
    margin: 0 0 var(--spacing-md) 0;
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    line-height: 1.5;
}

.insight-card .card-metric {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--primary-color);
    background: var(--light-blue);
    padding: 4px 12px;
    border-radius: 20px;
    display: inline-block;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .brand-analysis-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .channel-strategies {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .insight-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .timeline-phases {
        gap: var(--spacing-lg);
    }

    .phase {
        flex-direction: column;
        text-align: center;
    }

    .phase-details {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .positioning-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .profile-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .strategy-matrix-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .channel-metrics {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }

    .mitigation-items {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .strategy-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
    }

    .strategy-item .item-value {
        text-align: left;
    }
}

/* Bejoan数据免责声明样式 */
.disclaimer-note {
    margin-top: var(--spacing-sm);
    padding: var(--spacing-xs) var(--spacing-sm);
    background: rgba(255, 193, 7, 0.1);
    border: 1px solid rgba(255, 193, 7, 0.3);
    border-radius: 6px;
    font-size: var(--font-size-xs);
    color: #856404;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.disclaimer-note i {
    color: #ffc107;
    font-size: var(--font-size-sm);
}

.data-source p:last-child {
    margin-top: var(--spacing-sm);
    padding: var(--spacing-sm);
    background: rgba(255, 193, 7, 0.1);
    border-left: 4px solid #ffc107;
    border-radius: 4px;
    font-size: var(--font-size-sm);
    color: #856404;
    font-weight: 600;
}

.metric-change.neutral {
    background: rgba(108, 117, 125, 0.1);
    color: #6c757d;
}

/* Bejoan对比分析部分样式 */
.bejoan-comparison-section {
    margin: var(--spacing-xl) 0;
    padding: var(--spacing-xl);
    background: var(--background-secondary);
    border-radius: 16px;
    border: 2px solid var(--border-color);
}

.comparison-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--text-primary);
}

.comparison-title i {
    color: var(--primary-color);
    font-size: var(--font-size-xl);
}

.comparison-subtitle {
    font-size: var(--font-size-sm);
    font-weight: 400;
    color: var(--text-secondary);
    margin-left: auto;
    background: rgba(255, 193, 7, 0.1);
    padding: 4px 12px;
    border-radius: 20px;
    border: 1px solid rgba(255, 193, 7, 0.3);
}

.comparison-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.comparison-card {
    background: var(--background-primary);
    border-radius: 12px;
    padding: var(--spacing-lg);
    border: 2px solid var(--border-color);
    transition: all var(--transition-base);
}

.comparison-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-medium);
}

.comparison-card .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--border-color);
}

.comparison-card h4 {
    margin: 0;
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--text-primary);
}

.data-source-tag, .analysis-tag {
    font-size: var(--font-size-xs);
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.data-source-tag {
    background: rgba(0, 123, 255, 0.1);
    color: #0056b3;
    border: 1px solid rgba(0, 123, 255, 0.3);
}

.analysis-tag {
    background: rgba(40, 167, 69, 0.1);
    color: #155724;
    border: 1px solid rgba(40, 167, 69, 0.3);
}

.comparison-metrics {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.metric-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xs) 0;
}

.metric-row .metric-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 500;
}

.metric-row .metric-value {
    font-size: var(--font-size-sm);
    color: var(--text-primary);
    font-weight: 600;
}

.opportunity-assessment {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.opportunity-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    background: var(--background-secondary);
    border-radius: 8px;
    border: 1px solid var(--border-color);
    transition: all var(--transition-base);
}

.opportunity-item:hover {
    transform: translateX(4px);
    border-color: var(--primary-color);
}

.opportunity-item.best {
    background: rgba(40, 167, 69, 0.1);
    border-color: #28a745;
}

.opportunity-item i {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    font-size: var(--font-size-base);
}

.opportunity-item.best i {
    color: #28a745;
}

.opportunity-content {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.platform-name {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-primary);
}

.opportunity-desc {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
}

.comparison-disclaimer {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    background: rgba(255, 193, 7, 0.1);
    border: 1px solid rgba(255, 193, 7, 0.3);
    border-radius: 8px;
    font-size: var(--font-size-sm);
    color: #856404;
    line-height: 1.5;
}

.comparison-disclaimer i {
    color: #ffc107;
    font-size: var(--font-size-base);
    margin-top: 2px;
    flex-shrink: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .comparison-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .comparison-title {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
    }

    .comparison-subtitle {
        margin-left: 0;
    }
}
