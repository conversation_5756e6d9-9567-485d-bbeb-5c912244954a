/* ===================================
   第1章增强样式 - 战略背景
   ================================== */

/* === 第1页：封面页增强样式 === */

/* 背景装饰系统 - 第1章特定图片 */
.bg-image {
    background-image: url('https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2158&q=80');
    background-size: 100% 100%;
}

/* 标题增强样式 */
.enhanced-title {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    line-height: var(--line-height-tight);
    margin-bottom: var(--spacing-lg);
    text-align: center;
}

.title-primary {
    display: block;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.title-emphasis {
    display: block;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: var(--font-weight-extrabold);
}

.enhanced-subtitle {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-medium);
    color: var(--text-secondary);
    text-align: center;
    margin-bottom: var(--spacing-2xl);
}

/* 副标题容器增强 */
.enhanced-container {
    text-align: center;
    padding: var(--spacing-xl);
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-light);
    box-shadow: var(--shadow-md);
    margin-bottom: var(--spacing-2xl);
}

.typewriter-text {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    overflow: hidden;
    border-right: 2px solid var(--primary-color);
    white-space: nowrap;
    animation: typewriter 3s steps(20, end) 1.5s forwards, blink-caret 0.75s step-end infinite 1.5s;
    width: 0;
}

.enhanced-meta {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
    font-size: var(--font-size-sm);
    color: var(--text-tertiary);
}

.meta-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.meta-item i {
    color: var(--primary-color);
}

/* 关键指标卡片增强 */
.enhanced-insights {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-xl);
    max-width: 900px;
    margin: 0 auto;
}

.insight-card {
    position: relative;
    background: var(--background-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    border: 2px solid var(--border-light);
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: all var(--transition-base);
    overflow: hidden;
}

.insight-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    transition: all var(--transition-base);
}

.insight-card:nth-child(1)::before {
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
}

.insight-card:nth-child(2)::before {
    background: linear-gradient(90deg, var(--secondary-color), var(--secondary-light));
}

.insight-card:nth-child(3)::before {
    background: linear-gradient(90deg, var(--accent-color), var(--accent-light));
}

.insight-card:hover::before {
    height: 6px;
}

.insight-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto var(--spacing-md);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    color: white;
    transition: all var(--transition-base);
}

.primary-icon {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
}

.secondary-icon {
    background: linear-gradient(135deg, var(--secondary-color), var(--secondary-light));
}

.accent-icon {
    background: linear-gradient(135deg, var(--accent-color), var(--accent-light));
}

.insight-card:hover .insight-icon {
    transform: scale(1.1);
    box-shadow: var(--shadow-lg);
}

.insight-content {
    position: relative;
    z-index: 2;
}

.insight-value {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.insight-label {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-secondary);
}

.card-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, rgba(0, 115, 230, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%) scale(0);
    transition: transform var(--transition-slow);
    pointer-events: none;
}

.insight-card:hover .card-glow {
    transform: translate(-50%, -50%) scale(2);
}



.decoration-element.circle {
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: 50%;
    top: 10%;
    right: 10%;
}

.decoration-element.triangle {
    width: 0;
    height: 0;
    border-left: 40px solid transparent;
    border-right: 40px solid transparent;
    border-bottom: 70px solid var(--secondary-color);
    bottom: 20%;
    left: 5%;
}

.decoration-element.square {
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, var(--accent-color), var(--accent-light));
    border-radius: var(--radius-lg);
    top: 60%;
    right: 5%;
    transform: rotate(45deg);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .enhanced-title {
        font-size: var(--font-size-3xl);
    }
    
    .enhanced-subtitle {
        font-size: var(--font-size-lg);
    }
    
    .enhanced-insights {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
    
    .enhanced-container {
        padding: var(--spacing-lg);
        margin: 0 var(--spacing-md) var(--spacing-xl);
    }
    
    .typewriter-text {
        font-size: var(--font-size-base);
        white-space: normal;
        border-right: none;
        animation: none;
        width: auto;
    }
    
    .decoration-element {
        display: none;
    }
}

/* 打字机动画关键帧 */
@keyframes typewriter {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

@keyframes blink-caret {
    from, to {
        border-color: transparent;
    }
    50% {
        border-color: var(--primary-color);
    }
}

/* === 第2页：北美智能马桶市场规模分析增强样式 === */

/* 市场概览洞察 */
.market-overview-insight {
    background: linear-gradient(135deg, var(--primary-alpha-10), var(--secondary-alpha-10));
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    border: 2px solid var(--border-light);
    position: relative;
    overflow: hidden;
    margin-bottom: var(--spacing-2xl);
}

.market-overview-insight::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--accent-color));
}

/* 市场仪表板 */
.market-dashboard {
    margin: var(--spacing-2xl) 0;
}

/* 渠道分布网格 */
.channel-distribution {
    background: var(--background-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    border: 2px solid var(--border-light);
    box-shadow: var(--shadow-md);
    margin: var(--spacing-2xl) 0;
}

.distribution-title {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.distribution-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-xl);
}

.channel-card {
    background: var(--background-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    border: 2px solid var(--border-light);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.channel-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    transition: all var(--transition-base);
}

.channel-card.home-depot::before {
    background: linear-gradient(90deg, #f96302, #ff8533);
}

.channel-card.lowes::before {
    background: linear-gradient(90deg, #004990, #0066cc);
}

.channel-card.menards::before {
    background: linear-gradient(90deg, #ffcc00, #ffd633);
}

.channel-card:hover {
    transform: translateY(-6px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-alpha-20);
}

.channel-card:hover::before {
    height: 6px;
}

.channel-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--border-light);
}

.channel-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--font-size-xl);
    transition: all var(--transition-base);
}

.channel-card.home-depot .channel-icon {
    background: linear-gradient(135deg, #f96302, #ff8533);
}

.channel-card.lowes .channel-icon {
    background: linear-gradient(135deg, #004990, #0066cc);
}

.channel-card.menards .channel-icon {
    background: linear-gradient(135deg, #ffcc00, #ffd633);
}

.channel-card:hover .channel-icon {
    transform: scale(1.1);
    box-shadow: var(--shadow-md);
}

.channel-info h4 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin: 0 0 var(--spacing-xs) 0;
}

.channel-status {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    padding: 2px 8px;
    border-radius: var(--radius-full);
}

.channel-card.home-depot .channel-status {
    background: rgba(249, 99, 2, 0.1);
    color: #f96302;
    border: 1px solid rgba(249, 99, 2, 0.3);
}

.channel-card.lowes .channel-status {
    background: rgba(0, 73, 144, 0.1);
    color: #004990;
    border: 1px solid rgba(0, 73, 144, 0.3);
}

.channel-card.menards .channel-status {
    background: rgba(255, 204, 0, 0.1);
    color: #ffcc00;
    border: 1px solid rgba(255, 204, 0, 0.3);
}

.channel-metrics {
    margin-bottom: var(--spacing-lg);
}

.metric-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xs) 0;
    border-bottom: 1px solid var(--border-light);
}

.metric-row:last-child {
    border-bottom: none;
}

.metric-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
}

.metric-value {
    font-size: var(--font-size-sm);
    color: var(--text-primary);
    font-weight: var(--font-weight-semibold);
}

.channel-insight {
    background: var(--background-secondary);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    border: 1px solid var(--border-light);
}

.channel-insight p {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    line-height: var(--line-height-relaxed);
    margin: 0;
    font-style: italic;
}

/* 市场机会评估 */
.market-opportunity {
    background: var(--background-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    border: 2px solid var(--border-light);
    box-shadow: var(--shadow-md);
    margin: var(--spacing-2xl) 0;
}

.opportunity-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.opportunity-header h3 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.opportunity-header p {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    margin: 0;
}

.opportunity-summary {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.opportunity-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    border: 2px solid var(--border-light);
    transition: all var(--transition-base);
}

.opportunity-item:hover {
    transform: translateX(8px);
    box-shadow: var(--shadow-sm);
}

.opportunity-item.priority-high {
    border-color: var(--accent-color);
    background: linear-gradient(135deg, rgba(255, 107, 53, 0.05), rgba(255, 107, 53, 0.1));
}

.opportunity-item.priority-medium {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, rgba(0, 115, 230, 0.05), rgba(0, 115, 230, 0.1));
}

.opportunity-item.priority-normal {
    border-color: var(--secondary-color);
    background: linear-gradient(135deg, rgba(0, 166, 81, 0.05), rgba(0, 166, 81, 0.1));
}

.opportunity-icon {
    font-size: var(--font-size-2xl);
    flex-shrink: 0;
    margin-top: var(--spacing-xs);
}

.opportunity-content h4 {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin: 0 0 var(--spacing-sm) 0;
}

.opportunity-content p {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    line-height: var(--line-height-relaxed);
    margin: 0;
}

/* 响应式设计 - 第2页 */
@media (max-width: 768px) {
    .distribution-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .channel-card {
        padding: var(--spacing-md);
    }

    .channel-header {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-sm);
    }

    .channel-icon {
        width: 40px;
        height: 40px;
        font-size: var(--font-size-lg);
    }

    .opportunity-item {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-sm);
    }

    .opportunity-item:hover {
        transform: translateY(-4px);
    }

    .market-overview-insight {
        padding: var(--spacing-lg);
    }

    .market-opportunity {
        padding: var(--spacing-lg);
    }

}

@media (max-width: 480px) {
}

/* === 第2页：核心商业问题增强样式 === */

/* 模块卡片增强 */
.business-modules {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-xl);
    margin: var(--spacing-2xl) 0;
}

.module-card {
    background: var(--background-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    border: 2px solid var(--border-light);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.module-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    transition: all var(--transition-base);
}

.module-card:nth-child(1)::before {
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
}

.module-card:nth-child(2)::before {
    background: linear-gradient(90deg, var(--secondary-color), var(--secondary-light));
}

.module-card:nth-child(3)::before {
    background: linear-gradient(90deg, var(--accent-color), var(--accent-light));
}

.module-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-alpha-20);
}

.module-card:hover::before {
    height: 6px;
}

.module-icon {
    width: 70px;
    height: 70px;
    margin: 0 auto var(--spacing-lg);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-2xl);
    color: white;
    transition: all var(--transition-base);
    position: relative;
}

.module-card:nth-child(1) .module-icon {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
}

.module-card:nth-child(2) .module-icon {
    background: linear-gradient(135deg, var(--secondary-color), var(--secondary-light));
}

.module-card:nth-child(3) .module-icon {
    background: linear-gradient(135deg, var(--accent-color), var(--accent-light));
}

.module-card:hover .module-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: var(--shadow-lg);
}

.module-content h3 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    text-align: center;
}

.module-subtitle {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    color: var(--text-secondary);
    text-align: center;
    margin-bottom: var(--spacing-lg);
    font-style: italic;
}

.module-questions {
    list-style: none;
    padding: 0;
    margin: 0;
}

.module-questions li {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
    padding-left: var(--spacing-lg);
    position: relative;
    line-height: var(--line-height-relaxed);
}

.module-questions li::before {
    content: '▶';
    position: absolute;
    left: 0;
    top: 0;
    color: var(--primary-color);
    font-size: var(--font-size-xs);
    transition: all var(--transition-base);
}

.module-card:hover .module-questions li::before {
    transform: translateX(4px);
}

/* 仪表板指标增强 */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-lg);
    margin: var(--spacing-xl) 0;
}

.dashboard-metric {
    background: var(--background-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-light);
    box-shadow: var(--shadow-sm);
    text-align: center;
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.dashboard-metric::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    transform: scaleX(0);
    transition: transform var(--transition-base);
}

.dashboard-metric:hover::before {
    transform: scaleX(1);
}

.dashboard-metric:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-md);
}

.dashboard-metric .metric-icon {
    width: 50px;
    height: 50px;
    margin: 0 auto var(--spacing-md);
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--font-size-lg);
    transition: all var(--transition-base);
}

.dashboard-metric:hover .metric-icon {
    transform: scale(1.1);
    box-shadow: var(--shadow-md);
}

.dashboard-metric .metric-value {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.dashboard-metric .metric-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
}

.dashboard-metric .metric-change {
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    padding: 2px 8px;
    border-radius: var(--radius-full);
}

.dashboard-metric .metric-change.positive {
    background: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
}

/* 响应式设计 - 第2页 */
@media (max-width: 768px) {
    .business-modules {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .dashboard-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);
    }
}

@media (max-width: 480px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .module-card {
        padding: var(--spacing-lg);
    }

    .module-icon {
        width: 60px;
        height: 60px;
        font-size: var(--font-size-xl);
    }

    .module-content h3 {
        font-size: var(--font-size-base);
    }

    .module-questions li {
        font-size: var(--font-size-xs);
        padding-left: var(--spacing-md);
    }
}

/* === 第3页：验证框架增强样式 === */

/* 验证漏斗设计 */
.verification-funnel {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-xl);
    margin: 0 auto;
    position: relative;
}

.verification-funnel::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    width: 100%;
    height: 3px;
    background: linear-gradient(180deg, var(--primary-color), var(--secondary-color), var(--accent-color));
    transform: translateY(-50%);
    border-radius: var(--radius-full);
}

.funnel-level {
    display: flex;
    align-items: center;
    flex-direction: column;
    gap: var(--spacing-xl);
    position: relative;
    background: var(--background-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    border: 2px solid var(--border-light);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-base);
}

.funnel-level:hover {
    transform: translateX(8px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-alpha-20);
}

.funnel-level:nth-child(1) {
    border-left: 4px solid var(--primary-color);
}

.funnel-level:nth-child(2) {
    border-left: 4px solid var(--secondary-color);
}

.funnel-level:nth-child(3) {
    border-left: 4px solid var(--accent-color);
}

.funnel-level:nth-child(4) {
    border-left: 4px solid var(--info-color);
}

.level-number {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: white;
    flex-shrink: 0;
    position: relative;
    z-index: 2;
    transition: all var(--transition-base);
}

.funnel-level:nth-child(1) .level-number {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
}

.funnel-level:nth-child(2) .level-number {
    background: linear-gradient(135deg, var(--secondary-color), var(--secondary-light));
}

.funnel-level:nth-child(3) .level-number {
    background: linear-gradient(135deg, var(--accent-color), var(--accent-light));
}

.funnel-level:nth-child(4) .level-number {
    background: linear-gradient(135deg, var(--info-color), #3498db);
}

.funnel-level:hover .level-number {
    transform: scale(1.1);
    box-shadow: var(--shadow-lg);
}

.level-content {
    flex: 1;
}

.level-content h3 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.data-items {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.data-item {
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    background: var(--background-secondary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-light);
    transition: all var(--transition-base);
    text-align: left;
}

.data-item:hover {
    transform: translateX(4px);
    border-color: var(--primary-color);
    background: var(--primary-alpha-10);
}

.data-item-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}


.data-item i {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    font-size: var(--font-size-base);
    flex-shrink: 0;
    margin-top: 2px;
}

.data-item span {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    line-height: var(--line-height-relaxed);
}

.data-item strong {
    color: var(--text-primary);
    font-weight: var(--font-weight-semibold);
}

/* 验证步骤 */
.verification-steps {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
}

.step {
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--background-secondary);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-light);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    text-align: center;
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.step::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    transform: scaleX(0);
    transition: transform var(--transition-base);
}

.step:hover::before {
    transform: scaleX(1);
}

.step:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
    border-color: var(--primary-color);
}

/* 响应式设计 - 第3页 */
@media (max-width: 768px) {
    .verification-funnel::before {
        display: none;
    }

    .funnel-level {
        flex-direction: column;
        text-align: center;
        padding: var(--spacing-lg);
    }

    .funnel-level:hover {
        transform: translateY(-4px);
    }

    .level-number {
        width: 50px;
        height: 50px;
        font-size: var(--font-size-lg);
        margin: 0 auto var(--spacing-md);
    }

    .verification-steps {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }

    .data-item {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-sm);
    }

    .data-item:hover {
        transform: translateY(-2px);
    }
}

/* === 第4页：核心数据资产增强样式 === */

/* 数据资产网格 */
.data-assets-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-lg);
    margin: var(--spacing-2xl) 0;
}

.asset-card {
    background: var(--background-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    border: 2px solid var(--border-light);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
    text-align: center;
}

.asset-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    transform: scaleX(0);
    transition: transform var(--transition-base);
}

.asset-card:hover::before {
    transform: scaleX(1);
}

.asset-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-alpha-20);
}

.asset-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--spacing-lg);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-2xl);
    color: white;
    transition: all var(--transition-base);
    position: relative;
}

.asset-card:nth-child(odd) .asset-icon {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
}

.asset-card:nth-child(even) .asset-icon {
    background: linear-gradient(135deg, var(--secondary-color), var(--secondary-light));
}

.asset-card:hover .asset-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: var(--shadow-lg);
}

.asset-content h3 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.asset-format {
    display: inline-block;
    padding: 4px 12px;
    background: var(--primary-alpha-10);
    color: var(--primary-color);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    margin-bottom: var(--spacing-sm);
    border: 1px solid var(--primary-alpha-20);
}

.asset-description {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    line-height: var(--line-height-relaxed);
    margin: 0;
}

/* 资产价值声明 */
.asset-value {
    text-align: center;
    padding: var(--spacing-xl);
    background: linear-gradient(135deg, var(--primary-alpha-10), var(--secondary-alpha-10));
    border-radius: var(--radius-xl);
    border: 2px solid var(--border-light);
    margin: var(--spacing-2xl) 0;
    position: relative;
    overflow: hidden;
}

.asset-value::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--accent-color));
}

.value-statement {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    line-height: var(--line-height-relaxed);
    margin: 0;
    font-style: italic;
}

/* 响应式设计 - 第4页 */
@media (max-width: 768px) {
    .data-assets-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .asset-card {
        padding: var(--spacing-lg);
    }

    .asset-icon {
        width: 60px;
        height: 60px;
        font-size: var(--font-size-xl);
    }

    .asset-content h3 {
        font-size: var(--font-size-base);
    }

    .asset-value {
        padding: var(--spacing-lg);
        margin: var(--spacing-xl) 0;
    }

    .value-statement {
        font-size: var(--font-size-base);
    }
}

/* === 第5页：核心建议增强样式 === */

/* 策略仪表板 */
.strategy-dashboard {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-xl);
    margin: var(--spacing-2xl) 0;
}

.dashboard-item {
    background: var(--background-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    border: 2px solid var(--border-light);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.dashboard-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    transition: all var(--transition-base);
}

.dashboard-item:nth-child(1)::before {
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
}

.dashboard-item:nth-child(2)::before {
    background: linear-gradient(90deg, var(--secondary-color), var(--secondary-light));
}

.dashboard-item:nth-child(3)::before {
    background: linear-gradient(90deg, var(--accent-color), var(--accent-light));
}

.dashboard-item:nth-child(4)::before {
    background: linear-gradient(90deg, var(--info-color), #3498db);
}

.dashboard-item:hover {
    transform: translateY(-6px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-alpha-20);
}

.dashboard-item:hover::before {
    height: 6px;
}

.dashboard-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto var(--spacing-lg);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    color: white;
    transition: all var(--transition-base);
}

.dashboard-item:nth-child(1) .dashboard-icon {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
}

.dashboard-item:nth-child(2) .dashboard-icon {
    background: linear-gradient(135deg, var(--secondary-color), var(--secondary-light));
}

.dashboard-item:nth-child(3) .dashboard-icon {
    background: linear-gradient(135deg, var(--accent-color), var(--accent-light));
}

.dashboard-item:nth-child(4) .dashboard-icon {
    background: linear-gradient(135deg, var(--info-color), #3498db);
}

.dashboard-item:hover .dashboard-icon {
    transform: scale(1.1);
    box-shadow: var(--shadow-lg);
}

.dashboard-content h3 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    text-align: center;
}

/* 渠道分配 */
.channel-allocation {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.channel-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.channel-name {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    min-width: 80px;
}

.channel-bar {
    flex: 1;
    height: 8px;
    background: var(--background-tertiary);
    border-radius: var(--radius-full);
    overflow: hidden;
}

.channel-fill {
    height: 100%;
    border-radius: var(--radius-full);
    transition: width 2s ease-out;
    animation: progressBar 2s ease-out forwards;
}

.channel-percent {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    min-width: 40px;
    text-align: right;
}

/* 策略要点 */
.strategy-points {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.strategy-point {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    transition: all var(--transition-base);
}

.strategy-point.urgent {
    background: rgba(255, 107, 53, 0.1);
    color: var(--accent-color);
    border: 1px solid rgba(255, 107, 53, 0.2);
}

.strategy-point.advantage {
    background: rgba(0, 166, 81, 0.1);
    color: var(--secondary-color);
    border: 1px solid rgba(0, 166, 81, 0.2);
}

.strategy-point:hover {
    transform: translateX(4px);
}

.strategy-point i {
    font-size: var(--font-size-sm);
}

/* 机会文本 */
.opportunity-text {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
    text-align: center;
}

.price-range {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--primary-color);
    text-align: center;
    padding: var(--spacing-sm);
    background: var(--primary-alpha-10);
    border-radius: var(--radius-md);
    border: 1px solid var(--primary-alpha-20);
}

/* ROI指标 */
.roi-metrics {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-md);
}

.metric {
    text-align: center;
    padding: var(--spacing-sm);
    background: var(--background-secondary);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-light);
    display: block;
}

.metric-label {
    display: block;
    font-size: var(--font-size-xs);
    color: var(--text-tertiary);
    margin-bottom: var(--spacing-xs);
}

.metric-value {
    display: block;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
}

.highlight-pulse {
    animation: pulse 2s infinite;
    color: var(--primary-color) !important;
}

/* 策略目标 */
.strategy-goal {
    text-align: center;
    padding: var(--spacing-xl);
    background: linear-gradient(135deg, var(--primary-alpha-10), var(--secondary-alpha-10));
    border-radius: var(--radius-xl);
    border: 2px solid var(--border-light);
    margin: var(--spacing-2xl) 0;
    position: relative;
    overflow: hidden;
}

.strategy-goal::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--accent-color));
}

.goal-statement {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    line-height: var(--line-height-relaxed);
    margin: 0;
    font-style: italic;
}

/* 响应式设计 - 第5页 */
@media (max-width: 768px) {
    .strategy-dashboard {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-lg);
    }
}

@media (max-width: 480px) {
    .strategy-dashboard {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .dashboard-item {
        padding: var(--spacing-lg);
    }

    .dashboard-icon {
        width: 50px;
        height: 50px;
        font-size: var(--font-size-lg);
    }

    .dashboard-content h3 {
        font-size: var(--font-size-base);
    }

    .roi-metrics {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }

    .channel-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }

    .channel-name {
        min-width: auto;
    }

    .channel-bar {
        width: 100%;
    }

    .strategy-goal {
        padding: var(--spacing-lg);
        margin: var(--spacing-xl) 0;
    }

    .goal-statement {
        font-size: var(--font-size-base);
    }
}
