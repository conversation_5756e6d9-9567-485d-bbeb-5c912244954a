<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第二章：渠道战场深度剖析</title>
    <link rel="stylesheet" href="../assets/css/design-system.css">
    <link rel="stylesheet" href="../assets/css/animations.css">
    <link rel="stylesheet" href="../assets/css/common.css">
    <link rel="stylesheet" href="../assets/css/slide.css">
    <link rel="stylesheet" href="../assets/css/charts.css">
    <link rel="stylesheet" href="../assets/css/chapter2-enhanced.css">

    <!-- 图表库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@sgratzl/chartjs-chart-boxplot@4.2.8/build/index.umd.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/d3@7.8.5/dist/d3.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .gatekeepers-table {
            table-layout: fixed;
            width: 100%;
        }

        /* 新增样式：雷达图容器 */
        .radar-chart-container {
            width: 100%;
            height: 450px;
            margin: 30px 0;
            display: flex;
            justify-content: center;
            align-items: center;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 20px;
        }

        /* 新增样式：价格分布箱线图 */
        .boxplot-container {
            width: 100%;
            height: 350px;
            margin: 20px 0;
        }

        /* 新增样式：竞争定位散点图 */
        .scatter-chart-container {
            width: 100%;
            height: 400px;
            margin: 20px 0;
        }

        /* 新增样式：营销预算图表 */
        .marketing-chart-container {
            width: 100%;
            height: 300px;
            margin: 20px 0;
        }

        /* 新增样式：渠道对比分析 */
        .channel-comparison-analysis {
            width: 100%;
            margin: 20px 0;
        }

        .channel-comparison-matrix {
            margin: 30px 0;
        }

        .matrix-header {
            text-align: center;
            margin-bottom: 20px;
        }

        .matrix-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .channel-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            border-left: 4px solid;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .channel-card.home-depot {
            border-left-color: #0073e6;
        }

        .channel-card.lowes {
            border-left-color: #00a651;
        }

        .channel-card.menards {
            border-left-color: #ff6b35;
        }

        .channel-card h4 {
            margin: 0 0 15px 0;
            color: #333;
            font-weight: 600;
        }

        .channel-details {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .detail-item {
            font-size: 14px;
            line-height: 1.4;
            color: #555;
        }

        .channel-advantages-challenges {
            margin: 30px 0;
        }

        .advantages-header {
            text-align: center;
            margin-bottom: 20px;
        }

        .advantages-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .advantage-card {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .advantage-card h4 {
            margin: 0 0 15px 0;
            color: #333;
            font-weight: 600;
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 10px;
        }

        .pros, .cons {
            margin: 15px 0;
        }

        .pros h5, .cons h5 {
            margin: 0 0 8px 0;
            font-size: 14px;
            font-weight: 600;
        }

        .pros h5 {
            color: #00a651;
        }

        .cons h5 {
            color: #ff6b35;
        }

        .pros p, .cons p {
            font-size: 13px;
            line-height: 1.5;
            color: #666;
            margin: 0;
        }

        .radar-chart-container canvas {
            max-width: 100%;
            height: auto !important;
        }

        .chart-explanation {
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #0073e6;
        }

        .chart-explanation p {
            margin: 0;
            font-size: 14px;
            line-height: 1.5;
            color: #555;
        }

        /* 守门员产品卡片样式 */
        .gatekeeper-cards-grid {
            margin: 30px 0;
        }
        
        .gatekeeper-category {
            margin-bottom: 30px;
        }
        
        .gatekeeper-category h4 {
            margin-bottom: 20px;
            color: #333;
            font-size: 18px;
            font-weight: 600;
        }
        
        .product-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }
        
        .product-card {
            background: #fff;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .product-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .product-card.economy .card-header {
            background: linear-gradient(135deg, #4CAF50, #45a049);
        }
        
        .product-card.mid-range .card-header {
            background: linear-gradient(135deg, #2196F3, #1976D2);
        }
        
        .product-card.premium .card-header {
            background: linear-gradient(135deg, #9C27B0, #7B1FA2);
        }
        
        .product-card.empty .card-header {
            background: linear-gradient(135deg, #757575, #616161);
        }
        
        .card-header {
            padding: 15px;
            color: white;
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .price-range {
            font-size: 12px;
            opacity: 0.9;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .product-name {
            font-size: 16px;
            font-weight: 600;
        }
        
        .card-body {
            padding: 20px;
        }
        
        .metric {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            font-size: 14px;
            color: #666;
        }
        
        .metric .value {
            font-weight: 600;
            color: #333;
        }
        
        .metric .rating {
            color: #FFA500;
            font-weight: 600;
        }
        
        .description {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #eee;
            font-size: 13px;
            color: #555;
            font-style: italic;
        }
        
        .opportunity {
            text-align: center;
            font-size: 16px;
            color: #4CAF50;
            font-weight: 600;
            padding: 20px;
        }

        @media (max-width: 768px) {
            .matrix-grid,
            .advantages-grid {
                grid-template-columns: 1fr;
            }
            
            .radar-chart-container {
                height: 350px;
                padding: 15px;
            }
            
            .product-cards {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- 幻灯片1：第2章首页 -->
    <div id="slide-1" class="slide">
        <div class="slide-content center-content">
            <!-- 背景图片 -->
            <div class="hero-background">
                <div class="bg-image"></div>
                <div class="bg-overlay"></div>
                <div class="bg-pattern animate-fadeIn animate-delay-100"></div>
                <div class="bg-gradient animate-fadeIn animate-delay-200"></div>
            </div>

            <!-- 主标题区域 -->
            <div class="hero-section animate-slideUp animate-delay-300" style="margin-bottom: 40px;">
                <div class="chapter-badge animate-scaleIn animate-delay-400">第二章</div>
                <h1 class="hero-title animate-slideUp animate-delay-500">
                    渠道战场剖析
                </h1>
                <p class="hero-subtitle animate-slideUp animate-delay-600">
                    深度解析三大渠道的市场容量、竞争格局与价格守门员
                </p>
                <div class="hero-description animate-slideUp animate-delay-700">
                    <p>基于379款产品的全面分析，揭示Home Depot、Lowe's、Menards三大渠道的真实战场态势，识别关键竞争对手，制定精准进入策略。</p>
                </div>
            </div>


            <!-- 核心洞察 -->
            <div class="content-block core-insights animate-slideUp animate-delay-1250">
                <h3 class="insights-title">核心洞察</h3>
                <div class="insights-grid">
                    <div class="insight-item animate-scaleIn animate-delay-1300">
                        <div class="insight-number">01</div>
                        <div class="insight-content">
                            <p>Home Depot是<span class="insight-highlight">最大存量市场</span>($11.0M GMV)，<strong>智能马桶和马桶盖的价格带与守门员各有不同</strong></p>
                        </div>
                    </div>
                    <div class="insight-item animate-scaleIn animate-delay-1350">
                        <div class="insight-number">02</div>
                        <div class="insight-content">
                            <p>Lowe's展现<span class="insight-highlight">最强增长潜力</span>，品牌集中度更高，服务溢价明显</p>
                        </div>
                    </div>
                    <div class="insight-item animate-scaleIn animate-delay-1400">
                        <div class="insight-number">03</div>
                        <div class="insight-content">
                            <p>Menards线上表现严重低估其$130亿线下实力，构成<span class="insight-highlight">最大认知盲点</span></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 章节概要 -->
            <div class="content-block chapter-overview animate-slideUp animate-delay-1300">
                <h3 class="overview-title">本章节内容概要</h3>
                <div class="chapter-grid">
                    <div class="chapter-item">
                        <div class="chapter-number">2.1</div>
                        <div class="chapter-content">
                            <h4>三大渠道GMV对比</h4>
                            <p>Home Depot、Lowe's、Menards的市场规模与增长分析</p>
                        </div>
                    </div>
                    <div class="chapter-item">
                        <div class="chapter-number">2.2</div>
                        <div class="chapter-content">
                            <h4>渠道价格分析</h4>
                            <p>价格分布、竞争强度与定价策略研究</p>
                        </div>
                    </div>
                    <div class="chapter-item">
                        <div class="chapter-number">2.3</div>
                        <div class="chapter-content">
                            <h4>用户感知对比</h4>
                            <p>用户评分、评论分析与品牌认知研究</p>
                        </div>
                    </div>
                    <div class="chapter-item">
                        <div class="chapter-number">2.4</div>
                        <div class="chapter-content">
                            <h4>品牌生态分析</h4>
                            <p>各渠道品牌分布与竞争格局对比</p>
                        </div>
                    </div>
                    <div class="chapter-item">
                        <div class="chapter-number">2.5</div>
                        <div class="chapter-content">
                            <h4>价格守门员</h4>
                            <p>关键竞争对手识别与针对性策略制定</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 装饰元素 -->
            <div class="hero-decorations">
                <div class="decoration-element circle animate-float animate-delay-1500"></div>
                <div class="decoration-element triangle animate-pulse animate-delay-1700"></div>
                <div class="decoration-element square animate-float animate-delay-1900"></div>
            </div>
        </div>
    </div>

    <!-- 幻灯片6：三大渠道的市场容量与增长潜力验证 -->
    <div class="slide" id="slide-2">
        <div class="slide-header">
            <h1 class="slide-title">三大渠道的市场容量与增长潜力验证，揭示了不同的战场属性</h1>
            <p class="slide-subtitle">我们首先要评估的是三个战场的规模和未来潜力，以决定我们的主攻方向。</p>
        </div>
        <div class="slide-content">
            <!-- 市场洞察 -->
            <div class="content-block">
                <div class="market-overview">
                    <div class="market-insight animate-fadeInUp animate-delay-200">
                        <p class="insight-text">Home Depot是当前最大的存量市场（估算GMV $11.0M），以智能马桶为主；而Lowe's（估算GMV $2.0M）则展现出最强的未来增长潜力，智能马桶也占主导；Menards（估算GMV $0.66M）市场规模相对较小，但智能马桶盖产品占比较高。</p>
                    </div>

                    <!-- 图表容器 -->
                    <div class="chart-container animate-cardFloat animate-delay-400 hover-lift">
                        <div class="chart-header">
                            <h3 class="chart-title">三大渠道智能马桶产品（含马桶盖）GMV估算与增长率对比</h3>
                            <p class="chart-subtitle">基于318款产品数据分析，Home Depot领先但Lowe's增长更快</p>
                        </div>

                        <!-- 仪表板指标 -->
                        <div class="dashboard-grid">
                            <div class="dashboard-metric animate-scaleIn animate-delay-600 hover-lift">
                                <div class="metric-icon">
                                    <i class="fas fa-home"></i>
                                </div>
                                <div class="metric-value number-counter" data-target="11.0">0</div>
                                <div class="metric-label">Home Depot GMV (M)</div>
                                <div class="metric-change positive">+21.5% CAGR</div>
                            </div>

                            <div class="dashboard-metric animate-scaleIn animate-delay-700 hover-lift">
                                <div class="metric-icon">
                                    <i class="fas fa-store"></i>
                                </div>
                                <div class="metric-value number-counter" data-target="2.0">0</div>
                                <div class="metric-label">Lowe's GMV (M)</div>
                                <div class="metric-change positive">+23.1% CAGR</div>
                            </div>

                            <div class="dashboard-metric animate-scaleIn animate-delay-800 hover-lift">
                                <div class="metric-icon">
                                    <i class="fas fa-warehouse"></i>
                                </div>
                                <div class="metric-value number-counter" data-target="0.66">0</div>
                                <div class="metric-label">Menards GMV (M)</div>
                                <div class="metric-change positive">增长潜力大</div>
                            </div>

                            <div class="dashboard-metric animate-scaleIn animate-delay-900 hover-lift">
                                <div class="metric-icon">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <div class="metric-value number-counter" data-target="318">0</div>
                                <div class="metric-label">总产品数量</div>
                                <div class="metric-change positive">全面覆盖</div>
                            </div>
                        </div>

                        <!-- 图表控制按钮 -->
                        <div class="chart-controls animate-slideUp animate-delay-1000">
                            <button class="chart-control-btn active" data-view="gmv">
                                <span>GMV对比</span>
                            </button>
                            <button class="chart-control-btn" data-view="growth">
                                <span>增长率对比</span>
                            </button>
                            <button class="chart-control-btn" data-view="products">
                                <span>产品数量与价格</span>
                            </button>
                        </div>

                        <div id="channelChart" style="height: 350px; margin: 20px 0;"></div>
                        
                        <!-- 产品数量与平均价格对比 -->
                        <div class="product-price-comparison">
                            <h4>产品数量与平均价格对比</h4>
                            <div class="comparison-grid">
                                <div class="comparison-item">
                                    <h5>Home Depot</h5>
                                    <p>248个产品，平均价格$858.47</p>
                                </div>
                                <div class="comparison-item">
                                    <h5>Lowe's</h5>
                                    <p>105个产品，平均价格$711.34</p>
                                </div>
                                <div class="comparison-item">
                                    <h5>Menards</h5>
                                    <p>26个产品，平均价格$661.36</p>
                                </div>
                            </div>
                        </div>

                        <div class="chart-data-labels">
                            <div class="data-label">
                                <div class="data-label-value">80.1%</div>
                                <div class="data-label-text">Home Depot市场份额</div>
                            </div>
                            <div class="data-label">
                                <div class="data-label-value">14.6%</div>
                                <div class="data-label-text">Lowe's市场份额</div>
                            </div>
                            <div class="data-label">
                                <div class="data-label-value">4.8%</div>
                                <div class="data-label-text">Menards市场份额</div>
                            </div>
                            <div class="data-label">
                                <div class="data-label-value">$743</div>
                                <div class="data-label-text">市场平均价格</div>
                            </div>
                        </div>

                        <div class="chart-explanation">
                            <p><strong>GMV与增长率图</strong>展示了渠道的当前价值与未来潜力，表明我们需要平衡"现在"的利润（Home Depot）和"未来"的增长（Lowe's）</p>
                            <p><strong>产品数量与价格对比图</strong>揭示了渠道差异化特征：Home Depot产品最丰富且价格最高，体现其市场领导地位；Lowe's产品数量适中但价格相对较低，显示其性价比定位；Menards产品数量最少且价格最低，反映其精选策略和价格敏感的客群定位</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="content-block" data-animate="fadeIn" data-delay="600">
                <div class="data-source">
                    <p><strong>数据来源：</strong>基于Home Depot(248款产品，其中智能马桶约90%)、Lowe's(105款产品，其中智能马桶约81%)、Menards(26款产品，其中智能马桶盖约62%)的SKU数量、平均价格及用户活跃度模型测算（不含Bejoan产品数据）；结合各公司2024年年报数据</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 幻灯片7：Home Depot价格分布 -->
    <div class="slide" id="slide-3" style="display: none;">
        <div class="slide-header">
            <h1 class="slide-title">Home Depot战场被$600-$1000的中端市场主导，占据近60%的市场价值</h1>
            <p class="slide-subtitle">在最大的战场里，我们找到了最肥沃的区域。</p>
        </div>
        <div class="slide-content">
            <!-- 价格分析洞察 -->
            <div class="content-block">
                <div class="price-analysis">
                    <div class="analysis-insight animate-fadeInUp animate-delay-200">
                        <p class="insight-text">想要赢得Home Depot，关键在于赢得中端价格带。这既是最大的机会，也是竞争最激烈的"红海"。此价格带分析涵盖了Home Depot平台上的所有智能马桶和智能马桶盖产品。</p>
                    </div>


                        <!-- 交互式控制面板 -->
                        <div class="chart-controls">
                            <button class="chart-control-btn active" data-view="stacked">堆叠对比</button>
                            <button class="chart-control-btn" data-view="treemap">树状图</button>
                        </div>

                        <!-- 图表容器 -->
                        <div id="priceDistributionChart" style="height: 400px; margin: 20px 0;"></div>

                        <div class="chart-explanation">
                            <p><strong>战略洞察：</strong>此图直观地展示了Home Depot的"中端凸起"结构，与Lowe's的经济型市场和Menards的经济型市场形成鲜明对比。<strong>Menards的经济型市场主要由智能马桶盖产品构成，这与Home Depot和Lowe's以智能马桶为主的经济型市场有所不同。</strong></p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="content-block" data-animate="fadeIn" data-delay="700">
                <div class="data-source">
                    <p><strong>数据来源：</strong>基于Home Depot 248款产品价格分布分析（不含Bejoan产品数据）；价格段定义：经济型(<$800), 中端($801-$1500), 高端(>$1501)</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 幻灯片8：Menards认知盲点 -->
    <div class="slide" id="slide-4" style="display: none;">
        <div class="slide-header">
            <h1 class="slide-title">Menards的线上表现严重低估了其百亿美金的线下实力，构成了最大的认知盲点</h1>
            <p class="slide-subtitle">如果只看线上，我们会犯下致命的战略错误。</p>
        </div>
        <div class="slide-content center-content">
            <div class="content-block" data-animate="slideUp">
                <div class="iceberg-comparison">
                    <div class="iceberg-insight">
                        <p class="insight-text">Menards线上仅26款产品，几乎可以忽略不计，<strong>且其中智能马桶盖产品占比较高。</strong>但我们的"第四重验证"发现，它是一个年收入超130亿美金的线下巨头。这个巨大的反差意味着它要么是巨大的蓝海，要么是智能马桶品类的"绝缘体"。</p>
                    </div>
                    
                    <div class="icebergs-container">
                        <div class="iceberg small" data-animate="scaleIn" data-delay="200">
                            <div class="iceberg-top">
                                <div class="iceberg-label">Menards线上表现</div>
                                <div class="iceberg-value">26款SKU</div>
                            </div>
                            <div class="iceberg-bottom">
                                <div class="iceberg-hidden">$0.66M线上GMV</div>
                            </div>
                        </div>
                        
                        <div class="vs-separator" data-animate="fadeIn" data-delay="400">
                            <span>VS</span>
                        </div>
                        
                        <div class="iceberg large" data-animate="scaleIn" data-delay="600">
                            <div class="iceberg-top">
                                <div class="iceberg-label">Menards真实实力</div>
                                <div class="iceberg-value">中西部区域霸主</div>
                            </div>
                            <div class="iceberg-bottom">
                                <div class="iceberg-hidden">$130亿+年收入</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="warning-message" data-animate="fadeIn" data-delay="800">
                        <i class="fas fa-exclamation-triangle"></i>
                        <p>这组对比警示我们，针对Menards的任何决策都必须基于线下调研，否则风险极高。</p>
                    </div>
                </div>
            </div>
            <div class="content-block" data-animate="fadeIn" data-delay="1000">
                <div class="data-source">
                    <p><strong>数据来源：</strong>Menards线上数据基于2024年平台产品采集（<strong>不含Bejoan产品数据</strong>）；年收入数据来源Forbes 2024私人企业排名</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 幻灯片9：三大主流渠道特性与客户偏好深度对比 -->
    <div class="slide" id="slide-5" style="display: none;">
        <div class="slide-header">
            <h1 class="slide-title">三大主流渠道特性与客户偏好深度对比</h1>
            <p class="slide-subtitle">基于Home Depot、Lowe's、Menards三大平台318款产品+用户行为深度分析，揭示渠道差异化策略要点。</p>
        </div>
        <div class="slide-content">
            <div class="content-block" data-animate="slideUp">
                <div class="channel-comparison-analysis">
                    <div class="analysis-insight">
                        <p class="insight-text">三大渠道在价格敏感度、服务要求、产品选择等关键维度呈现显著差异。Home Depot适合价格策略，Lowe's适合服务策略，Menards适合极简低价策略。Bejoan需要针对性布局。</p>
                    </div>

                    <div class="chart-container" data-animate="cardFloat" data-delay="200">
                        <div class="chart-header">
                            <h3 class="chart-title">三渠道特性雷达图对比</h3>
                            <p class="chart-subtitle">五个关键维度的差异化分析</p>
                        </div>

                        <!-- 雷达图容器 -->
                        <div class="radar-chart-container">
                            <canvas id="channelRadarChart" style="height: 400px;"></canvas>
                        </div>

                        <!-- 渠道特性对比矩阵 -->
                        <div class="channel-comparison-matrix">
                            <div class="matrix-header">
                                <h3>渠道特性对比矩阵</h3>
                            </div>
                            <div class="matrix-grid">
                                <div class="channel-card home-depot">
                                    <h4><i class="fas fa-home"></i> Home Depot</h4>
                                    <div class="channel-details">
                                        <div class="detail-item"><strong>目标客户:</strong> 专业承包商+DIY爱好者</div>
                                        <div class="detail-item"><strong>价格策略:</strong> 最低价保证，频繁促销</div>
                                        <div class="detail-item"><strong>产品选择:</strong> 248款，<strong>以智能马桶为主</strong></div>
                                        <div class="detail-item"><strong>服务支持:</strong> 基础安装，技术支持有限</div>
                                        <div class="detail-item"><strong>决策影响因子:</strong> 价格40% + 库存30%</div>
                                        <div class="detail-item"><strong>平均客单价:</strong> $858.47 <strong>- 综合智能马桶和马桶盖</strong></div>
                                    </div>
                                </div>
                                
                                <div class="channel-card lowes">
                                    <h4><i class="fas fa-store"></i> Lowe's</h4>
                                    <div class="channel-details">
                                        <div class="detail-item"><strong>目标客户:</strong> 家庭装修者+品质买家</div>
                                        <div class="detail-item"><strong>价格策略:</strong> 合理价格，服务溢价</div>
                                        <div class="detail-item"><strong>产品选择:</strong> 105款，<strong>以智能马桶为主</strong>，精选优质</div>
                                        <div class="detail-item"><strong>服务支持:</strong> 专业安装，全程服务</div>
                                        <div class="detail-item"><strong>决策影响因子:</strong> 服务35% + 品质30%</div>
                                        <div class="detail-item"><strong>平均客单价:</strong> $711.34 <strong>- 综合智能马桶和马桶盖</strong></div>
                                    </div>
                                </div>
                                
                                <div class="channel-card menards">
                                    <h4><i class="fas fa-warehouse"></i> Menards</h4>
                                    <div class="channel-details">
                                        <div class="detail-item"><strong>目标客户:</strong> 中西部农村+价格敏感用户</div>
                                        <div class="detail-item"><strong>价格策略:</strong> 极致低价，批量折扣</div>
                                        <div class="detail-item"><strong>产品选择:</strong> 26款，<strong>以智能马桶盖为主</strong>，基础实用</div>
                                        <div class="detail-item"><strong>服务支持:</strong> 自助服务，成本导向</div>
                                        <div class="detail-item"><strong>决策影响因子:</strong> 价格60% + 实用性25%</div>
                                        <div class="detail-item"><strong>平均客单价:</strong> $661.36 <strong>- 综合智能马桶和马桶盖</strong></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 渠道优势与挑战 -->
                        <div class="channel-advantages-challenges">
                            <div class="advantages-header">
                                <h3>渠道优势与挑战分析</h3>
                            </div>
                            <div class="advantages-grid">
                                <div class="advantage-card home-depot">
                                    <h4>Home Depot</h4>
                                    <div class="pros">
                                        <h5>优势</h5>
                                        <p>全美最大家居建材零售商，专业承包商客户资源丰富，价格竞争力强，线下门店覆盖面广</p>
                                    </div>
                                    <div class="cons">
                                        <h5>挑战</h5>
                                        <p>价格压力大，利润率偏低，服务支持相对基础，竞争激烈，新品推广难</p>
                                    </div>
                                </div>
                                
                                <div class="advantage-card lowes">
                                    <h4>Lowe's</h4>
                                    <div class="pros">
                                        <h5>优势</h5>
                                        <p>家庭装修市场定位清晰，专业安装服务体系完善，客户品质意识强，溢价接受度高，品牌合作关系稳定</p>
                                    </div>
                                    <div class="cons">
                                        <h5>挑战</h5>
                                        <p>产品选择相对有限，进入门槛较高，品牌方投入要求高</p>
                                    </div>
                                </div>
                                
                                <div class="advantage-card menards">
                                    <h4>Menards</h4>
                                    <div class="pros">
                                        <h5>优势</h5>
                                        <p>中西部地区深度覆盖，极致的低价策略，农村市场渠道优势，简化产品线，降低选择成本</p>
                                    </div>
                                    <div class="cons">
                                        <h5>挑战</h5>
                                        <p>产品选择有限(仅26款)，服务支持相对薄弱，地域局限性明显，品牌知名度不足</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="chart-explanation">
                            <p><strong>图表解释:</strong> 雷达图清晰展示了三大渠道的差异化特征：Home Depot在产品丰富度和价格敏感度上领先，Lowe's在服务要求和品质要求上突出，Menards在价格敏感度上极致但其他维度相对薄弱。这为Bejoan制定差异化进入策略提供了清晰指引。</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="content-block" data-animate="fadeIn" data-delay="600">
                <div class="data-source">
                    <p><strong>数据来源：</strong>基于三大平台318款产品的价格、评分、功能特性综合分析（<strong>不含Bejoan产品数据</strong>）；结合各平台官方定位及业务模式研究</p>
                </div>
            </div>
        </div>
    </div>







    <!-- 幻灯片10：各渠道价格策略与竞争态势分析 -->
    <div id="slide-6" class="slide">
        <div class="slide-header">
            <h1 class="slide-title">各渠道价格策略与竞争态势分析</h1>
            <p class="slide-subtitle">不同渠道价格水平差异显著，Bejoan需要制定差异化定价策略。</p>
        </div>
        <div class="slide-content">
            <div class="content-block">
                <div class="price-strategy-analysis">
                    <div class="strategy-insight">
                        <p class="insight-text">通过对318款产品的价格深度分析，我们发现各渠道在智能马桶和马桶盖的定价策略上存在显著差异，这为Bejoan制定差异化定价策略提供了重要依据。</p>
                    </div>

                    <!-- 各渠道价格分布对比 -->
                    <div class="price-distribution-section">
                        <h3>各渠道价格分布对比（按产品类型）</h3>
                        
                        <div class="channel-price-grid">
                            <!-- Home Depot -->
                            <div class="channel-price-card home-depot">
                                <h4><i class="fas fa-home"></i> Home Depot</h4>
                                <div class="product-price-breakdown">
                                    <div class="product-type">
                                        <h5>智能马桶</h5>
                                        <div class="price-info">
                                            <div class="price-range-slide6">价格范围: $333.80-$5646.88</div>
                                            <div class="price-average">平均值: $905.60</div>
                                        </div>
                                    </div>
                                    <div class="product-type">
                                        <h5>智能马桶盖</h5>
                                        <div class="price-info">
                                            <div class="price-range-slide6">价格范围: $168.66-$494.00</div>
                                            <div class="price-average">平均值: $287.98</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Lowe's -->
                            <div class="channel-price-card lowes">
                                <h4><i class="fas fa-store"></i> Lowe's</h4>
                                <div class="product-price-breakdown">
                                    <div class="product-type">
                                        <h5>智能马桶</h5>
                                        <div class="price-info">
                                            <div class="price-range-slide6">价格范围: $189.00-$7999.98</div>
                                            <div class="price-average">平均值: $805.22</div>
                                        </div>
                                    </div>
                                    <div class="product-type">
                                        <h5>智能马桶盖</h5>
                                        <div class="price-info">
                                            <div class="price-range-slide6">价格范围: $109.98-$499.99</div>
                                            <div class="price-average">平均值: $311.12</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Menards -->
                            <div class="channel-price-card menards">
                                <h4><i class="fas fa-warehouse"></i> Menards</h4>
                                <div class="product-price-breakdown">
                                    <div class="product-type">
                                        <h5>智能马桶</h5>
                                        <div class="price-info">
                                            <div class="price-range-slide6">价格范围: $649.00-$1990.00</div>
                                            <div class="price-average">平均值: $1241.60</div>
                                        </div>
                                    </div>
                                    <div class="product-type">
                                        <h5>智能马桶盖</h5>
                                        <div class="price-info">
                                            <div class="price-range-slide6">价格范围: $86.79-$599.99</div>
                                            <div class="price-average">平均值: $298.71</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Bejoan价格策略建议 -->
                    <div class="bejoan-strategy-section">
                        <h3>Bejoan价格策略建议（作为新进入者，基于产品线差异化）</h3>
                        
                        <div class="strategy-recommendations">
                            <div class="strategy-card toilet-seat">
                                <div class="strategy-header">
                                    <h4>智能马桶盖</h4>
                                    <div class="recommended-price">建议价位: $200-400</div>
                                </div>
                                <div class="strategy-content">
                                    <div class="strategy-approach">
                                        <strong>策略:</strong> 巩固性价比优势，强调按摩功能
                                    </div>
                                    <div class="strategy-rationale">
                                        <strong>理由:</strong> Bejoan在该品类有成熟产品和市场经验
                                    </div>
                                </div>
                            </div>

                            <div class="strategy-card smart-toilet">
                                <div class="strategy-header">
                                    <h4>智能马桶</h4>
                                    <div class="recommended-price">建议价位: $749-899</div>
                                </div>
                                <div class="strategy-content">
                                    <div class="strategy-approach">
                                        <strong>策略:</strong> 竞争性定价，突出功能差异化
                                    </div>
                                    <div class="strategy-rationale">
                                        <strong>理由:</strong> 瞄准Home Depot中端市场，避开低价红海
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 添加箱线图数据说明 -->
                    <div class="boxplot-data-section">
                        <h3>各渠道价格分布箱线图</h3>
                        <!-- 实际的箱线图容器 -->
                        <div class="boxplot-container">
                            <canvas id="priceBoxplotChart"></canvas>
                        </div>
                        <!-- 数据已在上方箱线图中展示 -->
                    </div>
                    
                    <!-- 主要竞品价格定位分析 -->
                    <div class="competitor-analysis-section">
                        <h3>主要竞品价格定位分析（不含Bejoan，因其为新进入者）</h3>
                        
                        <div class="competitor-cards">
                            <div class="competitor-card toto">
                                <div class="brand-info">
                                    <h4>TOTO</h4>
                                    <div class="price-range-slide6">$1899-4899</div>
                                </div>
                                <div class="positioning">高端技术领导者</div>
                            </div>

                            <div class="competitor-card kohler">
                                <div class="brand-info">
                                    <h4>KOHLER</h4>
                                    <div class="price-range-slide6">$1299-3999</div>
                                </div>
                                <div class="positioning">品牌溢价策略</div>
                            </div>

                            <div class="competitor-card anzzi">
                                <div class="brand-info">
                                    <h4>ANZZI</h4>
                                    <div class="price-range-slide6">$799-1599</div>
                                </div>
                                <div class="positioning">性价比导向</div>
                            </div>
                        </div>
                    </div>

                    <!-- 图表解释 -->
                    <div class="chart-explanation">
                        <p>箱线图清晰展示了各渠道的价格分布特征：Home Depot智能马桶价格分布最广且集中在中端，Lowe's价格相对较低但分布均匀，Menards智能马桶价格偏高但选择有限。智能马桶盖在三个渠道的价格分布相对接近，为Bejoan提供了统一定价的可能性。</p>
                    </div>
                    
                    <!-- 核心观点 -->
                    <div class="core-insight-section">
                        <h3>核心观点</h3>
                        <div class="insight-content">
                            <p>Bejoan作为新进入者，应采取差异化定价策略。在智能马桶盖市场巩固现有优势，在智能马桶市场则瞄准中端，通过功能差异化逐步建立品牌溢价能力。</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="content-block" data-animate="fadeIn" data-delay="600">
                <div class="data-source">
                    <p><strong>数据来源：</strong>基于三大平台318款产品价格数据统计分析（不含Bejoan产品数据）；Bejoan当前产品定价$189-839(中位价$234)来自亚马逊美国平台；竞品价格基于各平台实时数据采集</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 幻灯片10A：Home Depot价格守门员产品分析 -->
    <div id="slide-7" class="slide">
        <div class="slide-header">
            <h1 class="slide-title">Home Depot价格守门员产品分析</h1>
            <p class="slide-subtitle">识别各价格段的关键竞争对手，制定针对性竞争策略。</p>
        </div>
        <div class="slide-content">
            <div class="content-block">
                <div class="gatekeeper-analysis">
                    <h3>智能马桶价格守门员</h3>
                    <div class="gatekeeper-cards">
                        <div class="gatekeeper-card economy">
                            <h4>经济型(<$800)</h4>
                            <div class="product-info">
                                <h5>HOROW Tankless Smart Toilet T3</h5>
                                <p>价格: $624</p>
                                <p>评论: 1810评论</p>
                                <p>评分: 4.7分</p>
                                <p>特点: 高销量高评分的价格杀手</p>
                            </div>
                        </div>
                        
                        <div class="gatekeeper-card mid-range">
                            <h4>中端型($801-$1500)</h4>
                            <div class="product-info">
                                <h5>ANZZI ENVO Echo</h5>
                                <p>价格: $1287</p>
                                <p>评论: 1294评论</p>
                                <p>评分: 4.4分</p>
                                <p>特点: 技术与价格平衡的标杆</p>
                            </div>
                        </div>
                        
                        <div class="gatekeeper-card premium">
                            <h4>高端型(>$1501)</h4>
                            <div class="product-info">
                                <h5>ANZZI ENVO Resonance Matte Black</h5>
                                <p>价格: $1849</p>
                                <p>评论: 478评论</p>
                                <p>评分: 4.3分</p>
                                <p>特点: 高端市场的性价比选择</p>
                            </div>
                        </div>
                    </div>
                    
                    <h3>智能马桶盖价格守门员</h3>
                    <div class="gatekeeper-cards">
                        <div class="gatekeeper-card economy">
                            <h4>经济型(<$300)</h4>
                            <div class="product-info">
                                <h5>BIO BIDET SlimTwo</h5>
                                <p>价格: $200</p>
                                <p>评论: 800评论</p>
                                <p>评分: 4.5分</p>
                                <p>特点: 市场领导者</p>
                            </div>
                        </div>
                        
                        <div class="gatekeeper-card mid-range">
                            <h4>中端型($300-500)</h4>
                            <div class="product-info">
                                <h5>BIO BIDET Bliss BB-2000</h5>
                                <p>价格: $494</p>
                                <p>评论: 1336评论</p>
                                <p>评分: 4.8分</p>
                                <p>特点: 功能与品质标杆</p>
                            </div>
                        </div>
                        
                        <div class="gatekeeper-card premium">
                            <h4>高端型(>$500)</h4>
                            <div class="product-info">
                                <h5>市场空白</h5>
                                <p>特点: 机会区域</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 散点图定位信息 -->
                    <div class="scatter-plot-section">
                        <h3>竞争定位散点图分析</h3>
                        <div class="scatter-explanation">
                            <p>X轴为价格，Y轴为评论数量，气泡大小代表评分高低</p>
                        </div>
                        <!-- 实际的散点图容器 -->
                        <div class="scatter-chart-container">
                            <canvas id="homeDepotScatterChart"></canvas>
                        </div>
                        <!-- 守门员产品卡片展示 -->
                        <div class="gatekeeper-cards-grid">
                            <div class="gatekeeper-category">
                                <h4>智能马桶价格守门员</h4>
                                <div class="product-cards">
                                    <div class="product-card economy">
                                        <div class="card-header">
                                            <span class="price-range">经济型 &lt;$800</span>
                                            <span class="product-name">HOROW T3</span>
                                        </div>
                                        <div class="card-body">
                                            <div class="metric">价格: <span class="value">$624</span></div>
                                            <div class="metric">评论: <span class="value">1810条</span></div>
                                            <div class="metric">评分: <span class="rating">★4.7</span></div>
                                            <div class="description">高销量高评分的价格杀手</div>
                                        </div>
                                    </div>
                                    <div class="product-card mid-range">
                                        <div class="card-header">
                                            <span class="price-range">中端型 $801-$1500</span>
                                            <span class="product-name">ANZZI ENVO Echo</span>
                                        </div>
                                        <div class="card-body">
                                            <div class="metric">价格: <span class="value">$1287</span></div>
                                            <div class="metric">评论: <span class="value">1294条</span></div>
                                            <div class="metric">评分: <span class="rating">★4.4</span></div>
                                            <div class="description">技术与价格平衡的标杆</div>
                                        </div>
                                    </div>
                                    <div class="product-card premium">
                                        <div class="card-header">
                                            <span class="price-range">高端型 &gt;$1501</span>
                                            <span class="product-name">ANZZI ENVO Resonance</span>
                                        </div>
                                        <div class="card-body">
                                            <div class="metric">价格: <span class="value">$1849</span></div>
                                            <div class="metric">评论: <span class="value">478条</span></div>
                                            <div class="metric">评分: <span class="rating">★4.3</span></div>
                                            <div class="description">高端市场的性价比选择</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="gatekeeper-category">
                                <h4>智能马桶盖价格守门员</h4>
                                <div class="product-cards">
                                    <div class="product-card economy">
                                        <div class="card-header">
                                            <span class="price-range">经济型 &lt;$300</span>
                                            <span class="product-name">BIO BIDET SlimTwo</span>
                                        </div>
                                        <div class="card-body">
                                            <div class="metric">价格: <span class="value">$200</span></div>
                                            <div class="metric">评论: <span class="value">800条</span></div>
                                            <div class="metric">评分: <span class="rating">★4.5</span></div>
                                            <div class="description">经济型市场领导者</div>
                                        </div>
                                    </div>
                                    <div class="product-card mid-range">
                                        <div class="card-header">
                                            <span class="price-range">中端型 $300-$500</span>
                                            <span class="product-name">BIO BIDET Bliss BB-2000</span>
                                        </div>
                                        <div class="card-body">
                                            <div class="metric">价格: <span class="value">$494</span></div>
                                            <div class="metric">评论: <span class="value">1336条</span></div>
                                            <div class="metric">评分: <span class="rating">★4.8</span></div>
                                            <div class="description">中端型功能标杆</div>
                                        </div>
                                    </div>
                                    <div class="product-card premium empty">
                                        <div class="card-header">
                                            <span class="price-range">高端型 &gt;$500</span>
                                            <span class="product-name">市场空白</span>
                                        </div>
                                        <div class="card-body">
                                            <div class="opportunity">机会区域</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="strategy-recommendation">
                        <h3>Bejoan进入策略建议</h3>
                        <div class="strategy-points">
                            <div class="strategy-point">
                                <h4>智能马桶盖</h4>
                                <p>Bejoan可凭借其在亚马逊的成功经验和按摩功能优势，瞄准Home Depot经济型和中端市场，成为新的价格守门员。</p>
                            </div>
                            <div class="strategy-point">
                                <h4>智能马桶</h4>
                                <p>针对HOROW T3等性价比产品，Bejoan需强调其独特的按摩功能和除臭功能（补齐后）的组合优势，避免纯价格竞争。</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="chart-explanation">
                        <p>散点图清晰展示了Home Depot市场的竞争格局：经济型智能马桶由HOROW主导，中端由ANZZI占据，智能马桶盖市场BIO BIDET表现强势。图中显示高端智能马桶盖市场存在明显空白，为Bejoan提供了差异化进入机会。</p>
                    </div>
                </div>
            </div>
            
            <div class="content-block" data-animate="fadeIn" data-delay="600">
                <div class="data-source">
                    <p><strong>数据来源：</strong>基于Home Depot 248款产品（其中智能马桶229款，智能马桶盖10款）的评论数量、评分、价格综合排序分析；Bejoan产品数据来自亚马逊美国平台。</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 幻灯片10B：Lowe's价格守门员产品分析 -->
    <div id="slide-8" class="slide">
        <div class="slide-header">
            <h1 class="slide-title">Lowe's价格守门员产品分析</h1>
            <p class="slide-subtitle">Lowe's平台品牌集中度更高，头部产品竞争更激烈。</p>
        </div>
        <div class="slide-content">
            <div class="content-block">
                <div class="gatekeeper-analysis">
                    <h3>智能马桶价格守门员</h3>
                    <div class="gatekeeper-cards">
                        <div class="gatekeeper-card economy">
                            <h4>经济型(<$800)</h4>
                            <div class="product-info">
                                <h5>KOHLER Highline Biscuit</h5>
                                <p>价格: $500.06</p>
                                <p>评论: 528评论</p>
                                <p>评分: 4.4分</p>
                                <p>特点: 传统品牌的性价比产品</p>
                            </div>
                        </div>
                        
                        <div class="gatekeeper-card mid-range">
                            <h4>中端型($801-$1500)</h4>
                            <div class="product-info">
                                <h5>ANZZI ENVO Echo</h5>
                                <p>价格: $864.12</p>
                                <p>评论: 654评论</p>
                                <p>评分: 4.4分</p>
                                <p>特点: 中端市场主导者</p>
                            </div>
                        </div>
                        
                        <div class="gatekeeper-card premium">
                            <h4>高端型(>$1501)</h4>
                            <div class="product-info">
                                <h5>产品稀少</h5>
                                <p>特点: 主要为KOHLER高端系列</p>
                            </div>
                        </div>
                    </div>
                    
                    <h3>智能马桶盖价格守门员</h3>
                    <div class="gatekeeper-cards">
                        <div class="gatekeeper-card economy">
                            <h4>经济型(<$300)</h4>
                            <div class="product-info">
                                <h5>KOHLER PureWash K-10349-CP</h5>
                                <p>价格: $150.85</p>
                                <p>评论: 175评论</p>
                                <p>评分: 4.1分</p>
                                <p>特点: 品牌力强但价格偏低</p>
                            </div>
                        </div>
                        
                        <div class="gatekeeper-card mid-range">
                            <h4>中端型($300-600)</h4>
                            <div class="product-info">
                                <h5>TOTO C100 Washlet</h5>
                                <p>价格: $450</p>
                                <p>评论: 300评论</p>
                                <p>评分: 4.6分</p>
                                <p>特点: 品质与功能兼备</p>
                            </div>
                        </div>
                        
                        <div class="gatekeeper-card premium">
                            <h4>高端型(>$600)</h4>
                            <div class="product-info">
                                <h5>ANZZI Smart Bidet Seat</h5>
                                <p>价格: $493.45</p>
                                <p>评论: 148评论</p>
                                <p>评分: 5.0分</p>
                                <p>特点: 高端标杆</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 散点图定位信息 -->
                    <div class="scatter-plot-section">
                        <h3>竞争定位散点图分析</h3>
                        <div class="scatter-explanation">
                            <p>X轴为价格，Y轴为评论数量，气泡大小代表评分高低</p>
                        </div>
                        <!-- 实际的散点图容器 -->
                        <div class="scatter-chart-container">
                            <canvas id="lowesScatterChart"></canvas>
                        </div>
                        <!-- 守门员产品卡片展示 -->
                        <div class="gatekeeper-cards-grid">
                            <div class="gatekeeper-category">
                                <h4>智能马桶价格守门员</h4>
                                <div class="product-cards">
                                    <div class="product-card economy">
                                        <div class="card-header">
                                            <span class="price-range">经济型 &lt;$800</span>
                                            <span class="product-name">KOHLER Highline</span>
                                        </div>
                                        <div class="card-body">
                                            <div class="metric">价格: <span class="value">$500.06</span></div>
                                            <div class="metric">评论: <span class="value">528条</span></div>
                                            <div class="metric">评分: <span class="rating">★4.4</span></div>
                                            <div class="description">传统品牌的性价比产品</div>
                                        </div>
                                    </div>
                                    <div class="product-card mid-range">
                                        <div class="card-header">
                                            <span class="price-range">中端型 $801-$1500</span>
                                            <span class="product-name">ANZZI ENVO Echo</span>
                                        </div>
                                        <div class="card-body">
                                            <div class="metric">价格: <span class="value">$864.12</span></div>
                                            <div class="metric">评论: <span class="value">654条</span></div>
                                            <div class="metric">评分: <span class="rating">★4.4</span></div>
                                            <div class="description">中端市场主导者</div>
                                        </div>
                                    </div>
                                    <div class="product-card premium">
                                        <div class="card-header">
                                            <span class="price-range">高端型 &gt;$1501</span>
                                            <span class="product-name">KOHLER高端系列</span>
                                        </div>
                                        <div class="card-body">
                                            <div class="description">产品稀少，主要为KOHLER高端系列</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="gatekeeper-category">
                                <h4>智能马桶盖价格守门员</h4>
                                <div class="product-cards">
                                    <div class="product-card economy">
                                        <div class="card-header">
                                            <span class="price-range">经济型 &lt;$300</span>
                                            <span class="product-name">KOHLER PureWash</span>
                                        </div>
                                        <div class="card-body">
                                            <div class="metric">价格: <span class="value">$150.85</span></div>
                                            <div class="metric">评论: <span class="value">175条</span></div>
                                            <div class="metric">评分: <span class="rating">★4.1</span></div>
                                            <div class="description">品牌力强但价格偏低</div>
                                        </div>
                                    </div>
                                    <div class="product-card mid-range">
                                        <div class="card-header">
                                            <span class="price-range">中端型 $300-$600</span>
                                            <span class="product-name">TOTO C100</span>
                                        </div>
                                        <div class="card-body">
                                            <div class="metric">价格: <span class="value">$450</span></div>
                                            <div class="metric">评论: <span class="value">300条</span></div>
                                            <div class="metric">评分: <span class="rating">★4.6</span></div>
                                            <div class="description">品质与功能兼备</div>
                                        </div>
                                    </div>
                                    <div class="product-card premium">
                                        <div class="card-header">
                                            <span class="price-range">高端型 &gt;$600</span>
                                            <span class="product-name">ANZZI Smart Bidet</span>
                                        </div>
                                        <div class="card-body">
                                            <div class="metric">价格: <span class="value">$493.45</span></div>
                                            <div class="metric">评论: <span class="value">148条</span></div>
                                            <div class="metric">评分: <span class="rating">★5.0</span></div>
                                            <div class="description">高端完美评分</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="market-insight">
                        <h3>市场洞察</h3>
                        <p>Lowe's市场品牌集中度高，传统品牌KOHLER和TOTO占据主导地位，但中端智能马桶盖市场存在机会空白，为Bejoan提供差异化进入窗口。</p>
                    </div>
                    
                    <div class="strategy-recommendation">
                        <h3>Bejoan进入策略建议</h3>
                        <div class="strategy-points">
                            <div class="strategy-point">
                                <h4>智能马桶盖</h4>
                                <p>Lowe's智能马桶盖中端市场存在机会，Bejoan可凭借其按摩功能和性价比优势，填补$300-400价格段空白，挑战现有品牌。</p>
                            </div>
                            <div class="strategy-point">
                                <h4>智能马桶</h4>
                                <p>针对Lowe's平台对健康理念的偏好，Bejoan智能马桶（补齐除臭功能后）可强调其按摩+除臭的组合优势，瞄准中高端市场。</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="chart-explanation">
                        <p>散点图显示Lowe's市场呈现明显的品牌集中特征，KOHLER和TOTO等传统品牌占据关键位置。值得注意的是，$300-400价格段的智能马桶盖市场存在明显空白，这为Bejoan提供了理想的进入机会。</p>
                    </div>
                </div>
            </div>
            
            <div class="content-block" data-animate="fadeIn" data-delay="600">
                <div class="data-source">
                    <p><strong>数据来源：</strong>基于Lowe's 105款产品的市场表现综合分析；Bejoan产品数据来自亚马逊美国平台。</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 幻灯片10C：Menards价格守门员产品分析 -->
    <div id="slide-9" class="slide">
        <div class="slide-header">
            <h1 class="slide-title">Menards价格守门员产品分析</h1>
            <p class="slide-subtitle">Menards产品线简化，Bejoan在智能马桶盖市场具备巨大进入潜力。</p>
        </div>
        <div class="slide-content">
            <div class="content-block">
                <div class="gatekeeper-analysis">
                    <h3>智能马桶价格守门员</h3>
                    <div class="gatekeeper-cards">
                        <div class="gatekeeper-card economy">
                            <h4>经济型(<$800)</h4>
                            <div class="product-info">
                                <h5>产品稀少</h5>
                                <p>特点: 主要为基础功能产品</p>
                            </div>
                        </div>
                        
                        <div class="gatekeeper-card mid-range">
                            <h4>中端型($801-$1500)</h4>
                            <div class="product-info">
                                <h5>市场空白</h5>
                                <p>特点: 巨大机会</p>
                            </div>
                        </div>
                        
                        <div class="gatekeeper-card premium">
                            <h4>高端型(>$1501)</h4>
                            <div class="product-info">
                                <h5>OVE Decors等少数品牌</h5>
                                <p>特点: 竞争不激烈</p>
                            </div>
                        </div>
                    </div>
                    
                    <h3>智能马桶盖价格守门员</h3>
                    <div class="gatekeeper-cards">
                        <div class="gatekeeper-card economy">
                            <h4>经济型(<$300)</h4>
                            <div class="product-info">
                                <h5>SmartBidet SB-1000</h5>
                                <p>价格: $250</p>
                                <p>评论: 300评论</p>
                                <p>评分: 4.0分</p>
                                <p>特点: 市场主要玩家</p>
                            </div>
                        </div>
                        
                        <div class="gatekeeper-card mid-range">
                            <h4>中端型($300-600)</h4>
                            <div class="product-info">
                                <h5>SmartBidet等品牌</h5>
                                <p>特点: 但评论数量少</p>
                            </div>
                        </div>
                        
                        <div class="gatekeeper-card premium">
                            <h4>高端型(>$600)</h4>
                            <div class="product-info">
                                <h5>几乎空白市场</h5>
                                <p>特点: 机会巨大</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 散点图定位信息 -->
                    <div class="scatter-plot-section">
                        <h3>竞争定位散点图分析</h3>
                        <div class="scatter-explanation">
                            <p>X轴为价格，Y轴为评论数量，气泡大小代表评分高低</p>
                        </div>
                        <!-- 实际的散点图容器 -->
                        <div class="scatter-chart-container">
                            <canvas id="menardsScatterChart"></canvas>
                        </div>
                        <!-- 守门员产品卡片展示 -->
                        <div class="gatekeeper-cards-grid">
                            <div class="gatekeeper-category">
                                <h4>智能马桶价格守门员</h4>
                                <div class="product-cards">
                                    <div class="product-card economy empty">
                                        <div class="card-header">
                                            <span class="price-range">经济型 &lt;$800</span>
                                            <span class="product-name">产品稀少</span>
                                        </div>
                                        <div class="card-body">
                                            <div class="description">主要为基础功能产品</div>
                                            <div class="opportunity">市场空白明显</div>
                                        </div>
                                    </div>
                                    <div class="product-card mid-range empty">
                                        <div class="card-header">
                                            <span class="price-range">中端型 $801-$1500</span>
                                            <span class="product-name">市场空白</span>
                                        </div>
                                        <div class="card-body">
                                            <div class="opportunity">完全空白，巨大蓝海机会</div>
                                        </div>
                                    </div>
                                    <div class="product-card premium">
                                        <div class="card-header">
                                            <span class="price-range">高端型 &gt;$1501</span>
                                            <span class="product-name">OVE Decors</span>
                                        </div>
                                        <div class="card-body">
                                            <div class="metric">评论数: <span class="value">&lt;100条</span></div>
                                            <div class="metric">评分: <span class="value">中等</span></div>
                                            <div class="description">高端少数玩家，竞争不激烈</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="gatekeeper-category">
                                <h4>智能马桶盖价格守门员</h4>
                                <div class="product-cards">
                                    <div class="product-card economy">
                                        <div class="card-header">
                                            <span class="price-range">经济型 &lt;$300</span>
                                            <span class="product-name">SmartBidet SB-1000</span>
                                        </div>
                                        <div class="card-body">
                                            <div class="metric">价格: <span class="value">$250</span></div>
                                            <div class="metric">评论: <span class="value">300条</span></div>
                                            <div class="metric">评分: <span class="rating">★4.0</span></div>
                                            <div class="description">市场主要玩家</div>
                                        </div>
                                    </div>
                                    <div class="product-card mid-range empty">
                                        <div class="card-header">
                                            <span class="price-range">中端型 $300-$600</span>
                                            <span class="product-name">SmartBidet等品牌</span>
                                        </div>
                                        <div class="card-body">
                                            <div class="description">产品稀少，评论数量极少</div>
                                        </div>
                                    </div>
                                    <div class="product-card premium empty">
                                        <div class="card-header">
                                            <span class="price-range">高端型 &gt;$600</span>
                                            <span class="product-name">几乎空白市场</span>
                                        </div>
                                        <div class="card-body">
                                            <div class="opportunity">机会巨大</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="market-insight">
                        <h3>市场洞察</h3>
                        <p>Menards市场呈现明显的蓝海特征，智能马桶中端市场几乎空白，智能马桶盖市场竞争强度低，为Bejoan提供了理想的市场进入机会。</p>
                    </div>
                    
                    <div class="strategy-recommendation">
                        <h3>Bejoan进入策略建议</h3>
                        <div class="strategy-points">
                            <div class="strategy-point">
                                <h4>智能马桶盖</h4>
                                <p>Bejoan可凭借其在亚马逊的强势表现和产品优势，在Menards智能马桶盖市场快速建立领导地位，成为新的绝对领导者。</p>
                            </div>
                            <div class="strategy-point">
                                <h4>智能马桶</h4>
                                <p>Menards智能马桶市场几乎空白，为Bejoan提供蓝海机会，可作为进入智能马桶市场的跳板，推出具备按摩和除臭功能（补齐后）的入门级智能马桶。</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="chart-explanation">
                        <p>散点图清晰展示了Menards市场的蓝海特征：智能马桶中端市场完全空白，智能马桶盖市场竞争者稀少且评论数量有限。这种市场结构为Bejoan提供了低风险、高回报的进入机会，特别是在智能马桶盖领域可以快速建立市场领导地位。</p>
                    </div>
                </div>
            </div>
            
            <div class="content-block" data-animate="fadeIn" data-delay="600">
                <div class="data-source">
                    <p><strong>数据来源：</strong>基于Menards 26款产品的完整市场分析；Bejoan产品数据来自亚马逊美国平台。</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 幻灯片11：基于渠道特性的营销策略建议 -->
    <div id="slide-10" class="slide">
        <div class="slide-header">
            <h1 class="slide-title">基于渠道特性的营销策略建议</h1>
            <p class="slide-subtitle">针对不同平台特点，制定差异化营销组合策略。</p>
        </div>
        <div class="slide-content">
            <div class="content-block">
                <div class="marketing-strategy">
                    <h3>渠道营销矩阵</h3>
                    
                    <div class="strategy-matrix">
                        <div class="strategy-card home-depot">
                            <h4 style="margin-bottom: 15px;">Home Depot营销策略</h4>
                            <div class="strategy-details">
                                <div class="strategy-item">
                                    <h5>核心定位</h5>
                                    <p>"专业级按摩清洗，DIY安装无忧"</p>
                                </div>
                                <div class="strategy-item">
                                    <h5>价格策略</h5>
                                    <p>竞争性定价，定期促销，批量购买优惠</p>
                                </div>
                                <div class="strategy-item">
                                    <h5>推广重点</h5>
                                    <p>强调安装便捷性，功能齐全性价比高，专业承包商推荐</p>
                                </div>
                                <div class="strategy-item">
                                    <h5>营销手段</h5>
                                    <p>门店展示与体验，在线广告投放，承包商渠道合作</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="strategy-card lowes">
                            <h4 style="margin-bottom: 15px;">Lowe's营销策略</h4>
                            <div class="strategy-details">
                                <div class="strategy-item">
                                    <h5>核心定位</h5>
                                    <p>"健康舒适升级，专业服务保障"</p>
                                </div>
                                <div class="strategy-item">
                                    <h5>价格策略</h5>
                                    <p>价值定价，服务捆绑套餐，质保延长服务</p>
                                </div>
                                <div class="strategy-item">
                                    <h5>推广重点</h5>
                                    <p>健康生活理念，专业安装服务，品质保证承诺</p>
                                </div>
                                <div class="strategy-item">
                                    <h5>营销手段</h5>
                                    <p>家庭装修展会，专业设计师推荐，客户教育研讨会</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="strategy-card menards">
                            <h4 style="margin-bottom: 15px;">Menards营销策略</h4>
                            <div class="strategy-details">
                                <div class="strategy-item">
                                    <h5>核心定位</h5>
                                    <p>"实用智能马桶，中西部首选"</p>
                                </div>
                                <div class="strategy-item">
                                    <h5>价格策略</h5>
                                    <p>极简定价，批量采购折扣，节庆大促销</p>
                                </div>
                                <div class="strategy-item">
                                    <h5>推广重点</h5>
                                    <p>基础功能够用，简单易安装，性价比第一</p>
                                </div>
                                <div class="strategy-item">
                                    <h5>营销手段</h5>
                                    <p>门店陈列展示，本地社区推广，农村市场渗透</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="integrated-marketing">
                        <h3>整合营销计划</h3>
                        
                        <div class="marketing-phases">
                            <div class="phase-card">
                                <h4>启动期营销(0-6个月)</h4>
                                <div class="phase-details">
                                    <div class="phase-goals">
                                        <h5>营销目标</h5>
                                        <ul>
                                            <li>建立品牌认知度目标20%</li>
                                            <li>获得首批客户目标500个</li>
                                            <li>积累用户评价目标100条</li>
                                        </ul>
                                    </div>
                                    <div class="phase-strategies">
                                        <h5>关键策略</h5>
                                        <ul>
                                            <li>免费样品体验活动</li>
                                            <li>早期用户优惠计划</li>
                                            <li>KOL产品测评合作</li>
                                        </ul>
                                    </div>
                                    <div class="phase-budget">
                                        <h5>投入预算</h5>
                                        <p>总预算$200,000，广告投放$120,000，促销活动$80,000</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="phase-card">
                                <h4>增长期营销(6-18个月)</h4>
                                <div class="phase-details">
                                    <div class="phase-goals">
                                        <h5>营销目标</h5>
                                        <ul>
                                            <li>扩大市场份额目标3%</li>
                                            <li>提升客户忠诚度</li>
                                            <li>建立渠道伙伴关系</li>
                                        </ul>
                                    </div>
                                    <div class="phase-strategies">
                                        <h5>关键策略</h5>
                                        <ul>
                                            <li>差异化功能传播</li>
                                            <li>客户成功案例分享</li>
                                            <li>渠道联合营销</li>
                                        </ul>
                                    </div>
                                    <div class="phase-budget">
                                        <h5>投入预算</h5>
                                        <p>总预算$500,000，品牌建设$300,000，渠道支持$200,000</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="marketing-metrics">
                        <h3>营销效果评估指标</h3>
                        <div class="metrics-grid">
                            <div class="metric-item">
                                <h5>品牌认知</h5>
                                <p>目标25%提升</p>
                            </div>
                            <div class="metric-item">
                                <h5>销售转化</h5>
                                <p>目标3%转化率</p>
                            </div>
                            <div class="metric-item">
                                <h5>客户满意</h5>
                                <p>目标4.5分评价</p>
                            </div>
                            <div class="metric-item">
                                <h5>ROI表现</h5>
                                <p>目标25-35% ROI，符合营销投入行业标准</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="core-insight">
                        <h3>核心观点</h3>
                        <p>Bejoan需要基于各渠道特性制定差异化营销策略，在Home Depot主打性价比，在Lowe's强调服务品质，在Menards注重极简实用与区域化推广。</p>
                    </div>
                </div>
            </div>
            
            <div class="content-block" data-animate="fadeIn" data-delay="600">
                <div class="data-source">
                    <p><strong>数据来源：</strong>基于各渠道客户特征分析及竞品营销策略研究；预算测算基于行业标准营销投入比例</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 幻灯片导航逻辑
        let currentSlide = 1;
        const totalSlides = 10;
        
        function showSlide(slideNumber) {
            document.querySelectorAll('.slide').forEach(slide => {
                slide.style.display = 'none';
            });
            
            const targetSlide = document.getElementById(`slide-${slideNumber}`);
            if (targetSlide) {
                targetSlide.style.display = 'block';
                triggerAnimations(targetSlide);
            }
            
            currentSlide = slideNumber;
        }
        
        function triggerAnimations(slide) {
            const animatedElements = slide.querySelectorAll('[data-animate]');
            animatedElements.forEach((element, index) => {
                const delay = parseInt(element.dataset.delay) || 0;
                setTimeout(() => {
                    element.classList.add('animated');
                    
                    // 特殊处理数字动画
                    if (element.classList.contains('number-counter')) {
                        animateNumber(element);
                    }
                    
                    // 特殊处理进度条动画
                    if (element.classList.contains('bar-fill')) {
                        animateBar(element);
                    }
                }, delay);
            });
        }
        
        function animateNumber(element) {
            const target = parseInt(element.dataset.target) || parseInt(element.textContent);
            const duration = 2000;
            const start = 0;
            const startTime = performance.now();
            
            function update(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);
                const current = Math.floor(start + (target - start) * easeOutQuart(progress));
                element.textContent = current;
                
                if (progress < 1) {
                    requestAnimationFrame(update);
                }
            }
            
            requestAnimationFrame(update);
        }
        
        function animateBar(element) {
            const targetWidth = element.style.width;
            element.style.width = '0%';
            element.style.transition = 'width 1.5s cubic-bezier(0.4, 0, 0.2, 1)';
            
            setTimeout(() => {
                element.style.width = targetWidth;
            }, 100);
        }
        
        function easeOutQuart(t) {
            return 1 - Math.pow(1 - t, 4);
        }
        
        // 监听URL hash变化
        function handleHashChange() {
            const hash = window.location.hash;
            const match = hash.match(/#slide-(\d+)/);
            if (match) {
                const slideNumber = parseInt(match[1]);
                if (slideNumber >= 1 && slideNumber <= totalSlides) {
                    showSlide(slideNumber);
                }
            }
        }
        
        // 初始化
        window.addEventListener('load', () => {
            handleHashChange();
            if (!window.location.hash) {
                showSlide(1);
            }
        });
        
        window.addEventListener('hashchange', handleHashChange);

        // 品牌竞争图表初始化
        const brandData = {
            brands: ['KOHLER', 'TOTO', 'HOROW', 'ANZZI', 'BIO BIDET', 'Bejoan'],
            channels: ['Home Depot', "Lowe's", 'Menards'],
            matrix: {
                'KOHLER': { hd: 85, lowes: 95, menards: 0, price: 2200, rating: 4.6 },
                'TOTO': { hd: 70, lowes: 60, menards: 0, price: 1800, rating: 4.5 },
                'HOROW': { hd: 90, lowes: 75, menards: 20, price: 850, rating: 4.2 },
                'ANZZI': { hd: 60, lowes: 85, menards: 0, price: 1200, rating: 4.3 },
                'BIO BIDET': { hd: 80, lowes: 30, menards: 0, price: 650, rating: 3.9 },
                'Bejoan': { hd: 0, lowes: 0, menards: 85, price: 661, rating: 4.1 }
            }
        };

        // 品牌竞争图表控制
        document.querySelectorAll('#brandCompetitionChart').forEach(container => {
            if (container) {
                const controlBtns = container.parentElement.querySelectorAll('.chart-control-btn');
                controlBtns.forEach(btn => {
                    btn.addEventListener('click', function() {
                        controlBtns.forEach(b => b.classList.remove('active'));
                        this.classList.add('active');

                        const view = this.dataset.view;
                        updateBrandChart(view);
                    });
                });

                // 初始化品牌图表
                setTimeout(() => updateBrandChart('matrix'), 2000);
            }
        });

        function updateBrandChart(view) {
            const chartContainer = document.getElementById('brandCompetitionChart');
            if (!chartContainer) return;

            chartContainer.innerHTML = '';

            const canvas = document.createElement('canvas');
            canvas.style.maxHeight = '400px';
            chartContainer.appendChild(canvas);

            const ctx = canvas.getContext('2d');

            let chartConfig;

            switch(view) {
                case 'matrix':
                    // 散点图显示品牌竞争力矩阵
                    chartConfig = {
                        type: 'scatter',
                        data: {
                            datasets: brandData.brands.map((brand, index) => ({
                                label: brand,
                                data: [{
                                    x: brandData.matrix[brand].price,
                                    y: brandData.matrix[brand].rating * 20 // 转换为0-100范围
                                }],
                                backgroundColor: ['#0073e6', '#00a651', '#ff6b35', '#ffc107', '#17a2b8', '#dc3545'][index],
                                borderColor: ['#0073e6', '#00a651', '#ff6b35', '#ffc107', '#17a2b8', '#dc3545'][index],
                                pointRadius: brand === 'Bejoan' ? 12 : 8,
                                pointHoverRadius: brand === 'Bejoan' ? 15 : 10
                            }))
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                x: {
                                    title: { display: true, text: '平均价格 ($)' },
                                    min: 500,
                                    max: 2500
                                },
                                y: {
                                    title: { display: true, text: '用户评分 (转换为0-100)' },
                                    min: 70,
                                    max: 100
                                }
                            },
                            plugins: {
                                legend: { position: 'top' },
                                tooltip: {
                                    callbacks: {
                                        label: (context) => {
                                            const brand = brandData.brands[context.datasetIndex];
                                            const data = brandData.matrix[brand];
                                            return [
                                                `${brand}`,
                                                `价格: $${data.price}`,
                                                `评分: ${data.rating}`,
                                                `HD竞争力: ${data.hd}%`,
                                                `Lowe's竞争力: ${data.lowes}%`
                                            ];
                                        }
                                    }
                                }
                            }
                        }
                    };
                    break;

                case 'share':
                    // 各渠道品牌份额对比
                    chartConfig = {
                        type: 'bar',
                        data: {
                            labels: brandData.brands,
                            datasets: [
                                {
                                    label: 'Home Depot',
                                    data: brandData.brands.map(brand => brandData.matrix[brand].hd),
                                    backgroundColor: 'rgba(0, 115, 230, 0.8)'
                                },
                                {
                                    label: "Lowe's",
                                    data: brandData.brands.map(brand => brandData.matrix[brand].lowes),
                                    backgroundColor: 'rgba(0, 166, 81, 0.8)'
                                },
                                {
                                    label: 'Menards',
                                    data: brandData.brands.map(brand => brandData.matrix[brand].menards),
                                    backgroundColor: 'rgba(255, 107, 53, 0.8)'
                                }
                            ]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    max: 100,
                                    ticks: { callback: (value) => `${value}%` }
                                }
                            },
                            plugins: { legend: { position: 'top' } }
                        }
                    };
                    break;

                case 'price':
                    // 价格分布
                    chartConfig = {
                        type: 'bar',
                        data: {
                            labels: brandData.brands,
                            datasets: [{
                                label: '平均价格',
                                data: brandData.brands.map(brand => brandData.matrix[brand].price),
                                backgroundColor: brandData.brands.map((brand, index) =>
                                    ['#0073e6', '#00a651', '#ff6b35', '#ffc107', '#17a2b8', '#dc3545'][index] + '80'
                                ),
                                borderColor: brandData.brands.map((brand, index) =>
                                    ['#0073e6', '#00a651', '#ff6b35', '#ffc107', '#17a2b8', '#dc3545'][index]
                                ),
                                borderWidth: 2
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    ticks: { callback: (value) => `$${value}` }
                                }
                            },
                            plugins: { legend: { display: false } }
                        }
                    };
                    break;

                case 'rating':
                    // 用户评分对比
                    chartConfig = {
                        type: 'radar',
                        data: {
                            labels: brandData.brands,
                            datasets: [{
                                label: '用户评分',
                                data: brandData.brands.map(brand => brandData.matrix[brand].rating),
                                backgroundColor: 'rgba(0, 115, 230, 0.2)',
                                borderColor: '#0073e6',
                                borderWidth: 3,
                                pointBackgroundColor: '#0073e6'
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                r: {
                                    beginAtZero: true,
                                    min: 3.5,
                                    max: 5,
                                    ticks: { stepSize: 0.5 }
                                }
                            }
                        }
                    };
                    break;
            }

            new Chart(ctx, chartConfig);
        }

        // 导出函数供父页面调用
        window.triggerAnimations = () => {
            const currentSlideElement = document.getElementById(`slide-${currentSlide}`);
            if (currentSlideElement) {
                triggerAnimations(currentSlideElement);
            }
        };

        // 初始化渠道对比图表
        document.addEventListener('DOMContentLoaded', function() {
            const channelData = {
                gmv: {
                    labels: ['Home Depot', "Lowe's", 'Menards'],
                    values: [11.0, 2.0, 0.66],
                    colors: ['#0073e6', '#00a651', '#ff6b35']
                },
                growth: {
                    labels: ['Home Depot', "Lowe's", 'Menards'],
                    values: [21.5, 23.1, 18.0],
                    colors: ['#0073e6', '#00a651', '#ff6b35']
                },
                products: {
                    labels: ['Home Depot', "Lowe's", 'Menards'],
                    values: [248, 105, 26],
                    colors: ['#0073e6', '#00a651', '#ff6b35']
                },
                price: {
                    labels: ['Home Depot', "Lowe's", 'Menards'],
                    values: [858.47, 711.34, 661.36],
                    colors: ['#0073e6', '#00a651', '#ff6b35']
                }
            };

            // 图表控制按钮事件
            document.querySelectorAll('.chart-control-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.chart-control-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');

                    const view = this.dataset.view;
                    updateChannelChart(view);
                });
            });

            // 初始化图表
            setTimeout(() => updateChannelChart('gmv'), 1000);

            // 价格分布图表数据
            const priceData = {
                channels: ['Home Depot', "Lowe's", 'Menards'],
                segments: ['经济型', '中端型', '高端型'],
                data: {
                    'Home Depot': { economic: 36.4, mid: 50.5, premium: 10.9, total: 11.0 },
                    "Lowe's": { economic: 74.0, mid: 20.0, premium: 6.0, total: 2.0 },
                    'Menards': { economic: 69.2, mid: 19.2, premium: 11.5, total: 0.66 }
                }
            };

            // 价格分布图表控制
            document.querySelectorAll('#priceDistributionChart').forEach(container => {
                if (container) {
                    const controlBtns = container.parentElement.querySelectorAll('.chart-control-btn');
                    controlBtns.forEach(btn => {
                        btn.addEventListener('click', function() {
                            controlBtns.forEach(b => b.classList.remove('active'));
                            this.classList.add('active');

                            const view = this.dataset.view;
                            updatePriceChart(view);
                        });
                    });

                    // 初始化价格图表
                    setTimeout(() => updatePriceChart('stacked'), 1500);
                }
            });

            function updatePriceChart(view) {
                const chartContainer = document.getElementById('priceDistributionChart');
                if (!chartContainer) return;

                chartContainer.innerHTML = '';

                switch(view) {
                    case 'stacked':
                        createStackedChart(chartContainer);
                        break;
                    case 'treemap':
                        createTreemap(chartContainer);
                        break;
                }
            }

            function createStackedChart(container) {
                const canvas = document.createElement('canvas');
                canvas.style.maxHeight = '400px';
                container.appendChild(canvas);

                const ctx = canvas.getContext('2d');

                new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: priceData.channels,
                        datasets: [
                            {
                                label: '经济型 (<$800)',
                                data: priceData.channels.map(channel => priceData.data[channel].economic),
                                backgroundColor: 'rgba(23, 162, 184, 0.8)',
                                borderColor: '#17a2b8',
                                borderWidth: 1
                            },
                            {
                                label: '中端型 ($801-$1500)',
                                data: priceData.channels.map(channel => priceData.data[channel].mid),
                                backgroundColor: 'rgba(0, 115, 230, 0.8)',
                                borderColor: '#0073e6',
                                borderWidth: 1
                            },
                            {
                                label: '高端型 (>$1501)',
                                data: priceData.channels.map(channel => priceData.data[channel].premium),
                                backgroundColor: 'rgba(255, 107, 53, 0.8)',
                                borderColor: '#ff6b35',
                                borderWidth: 1
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            x: { stacked: true },
                            y: {
                                stacked: true,
                                beginAtZero: true,
                                max: 100,
                                ticks: {
                                    callback: (value) => `${value}%`
                                }
                            }
                        },
                        plugins: {
                            legend: { position: 'top' },
                            tooltip: {
                                callbacks: {
                                    label: (context) => `${context.dataset.label}: ${context.parsed.y.toFixed(1)}%`
                                }
                            }
                        }
                    }
                });
            }

            function createTreemap(container) {
                const treemapDiv = document.createElement('div');
                treemapDiv.className = 'treemap-container';

                // 简化的树状图实现
                const totalValue = Object.values(priceData.data).reduce((sum, channel) => sum + channel.total, 0);
                let currentX = 0;

                priceData.channels.forEach((channel, index) => {
                    const channelData = priceData.data[channel];
                    const width = (channelData.total / totalValue) * 100;

                    const cell = document.createElement('div');
                    cell.className = 'treemap-cell';
                    cell.style.left = `${currentX}%`;
                    cell.style.width = `${width}%`;
                    cell.style.height = '100%';
                    cell.style.background = ['#0073e6', '#00a651', '#ff6b35'][index];

                    cell.innerHTML = `
                        <div class="cell-title">${channel}</div>
                        <div class="cell-value">$${channelData.total}M</div>
                        <div class="cell-value">${((channelData.total / totalValue) * 100).toFixed(1)}%</div>
                    `;

                    treemapDiv.appendChild(cell);
                    currentX += width;
                });

                container.appendChild(treemapDiv);
            }


            function updateChannelChart(view) {
                const chartContainer = document.getElementById('channelChart');
                if (!chartContainer) return;

                chartContainer.innerHTML = '';
                const canvas = document.createElement('canvas');
                canvas.style.maxHeight = '350px';
                chartContainer.appendChild(canvas);
                const ctx = canvas.getContext('2d');

                if (view === 'gmv' || view === 'growth') {
                    // 图表1: GMV对比条形图 + 增长率折线图
                    new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: ['Home Depot', "Lowe's", 'Menards'],
                            datasets: [
                                {
                                    type: 'bar',
                                    label: 'GMV (Million $)',
                                    data: [11.0, 2.0, 0.66],
                                    backgroundColor: ['rgba(0, 115, 230, 0.8)', 'rgba(0, 166, 81, 0.8)', 'rgba(255, 107, 53, 0.8)'],
                                    borderColor: ['#0073e6', '#00a651', '#ff6b35'],
                                    borderWidth: 2,
                                    borderRadius: 8,
                                    yAxisID: 'y',
                                    order: 2
                                },
                                {
                                    type: 'line',
                                    label: '年复合增长率 (%)',
                                    data: [21.5, 23.1, 18.0],
                                    borderColor: '#E91E63',
                                    backgroundColor: 'rgba(233, 30, 99, 0.1)',
                                    borderWidth: 3,
                                    pointRadius: 6,
                                    pointBackgroundColor: '#E91E63',
                                    pointBorderColor: '#fff',
                                    pointBorderWidth: 2,
                                    tension: 0.2,
                                    yAxisID: 'y1',
                                    order: 1
                                }
                            ]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            interaction: {
                                mode: 'index',
                                intersect: false,
                            },
                            plugins: {
                                legend: {
                                    display: true,
                                    position: 'top'
                                },
                                title: {
                                    display: true,
                                    text: 'GMV与增长率对比'
                                }
                            },
                            scales: {
                                x: {
                                    display: true,
                                    grid: {
                                        display: false
                                    }
                                },
                                y: {
                                    type: 'linear',
                                    display: true,
                                    position: 'left',
                                    title: {
                                        display: true,
                                        text: 'GMV (Million $)'
                                    },
                                    beginAtZero: true,
                                    max: 12
                                },
                                y1: {
                                    type: 'linear',
                                    display: true,
                                    position: 'right',
                                    title: {
                                        display: true,
                                        text: '增长率 (%)'
                                    },
                                    grid: {
                                        drawOnChartArea: false,
                                    },
                                    beginAtZero: true,
                                    max: 25
                                }
                            }
                        }
                    });
                } else if (view === 'products') {
                    // 图表2: 产品数量与平均价格对比图（组合条形图）
                    new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: ['Home Depot', "Lowe's", 'Menards'],
                            datasets: [
                                {
                                    label: '产品数量 (个)',
                                    data: [248, 105, 26],
                                    backgroundColor: ['rgba(0, 115, 230, 0.8)', 'rgba(0, 166, 81, 0.8)', 'rgba(255, 107, 53, 0.8)'],
                                    borderColor: ['#0073e6', '#00a651', '#ff6b35'],
                                    borderWidth: 2,
                                    borderRadius: 8,
                                    yAxisID: 'y'
                                },
                                {
                                    type: 'line',
                                    label: '平均价格 ($)',
                                    data: [858.47, 711.34, 661.36],
                                    borderColor: '#9C27B0',
                                    backgroundColor: 'rgba(156, 39, 176, 0.1)',
                                    borderWidth: 3,
                                    pointRadius: 8,
                                    pointBackgroundColor: '#9C27B0',
                                    pointBorderColor: '#fff',
                                    pointBorderWidth: 2,
                                    tension: 0.1,
                                    yAxisID: 'y1'
                                }
                            ]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            interaction: {
                                mode: 'index',
                                intersect: false,
                            },
                            plugins: {
                                legend: {
                                    display: true,
                                    position: 'top'
                                },
                                title: {
                                    display: true,
                                    text: '产品数量与平均价格对比'
                                },
                                tooltip: {
                                    callbacks: {
                                        afterLabel: function(context) {
                                            if (context.datasetIndex === 0) {
                                                return '产品丰富度与价格定位差异';
                                            }
                                            return '';
                                        }
                                    }
                                }
                            },
                            scales: {
                                x: {
                                    display: true,
                                    grid: {
                                        display: false
                                    }
                                },
                                y: {
                                    type: 'linear',
                                    display: true,
                                    position: 'left',
                                    title: {
                                        display: true,
                                        text: '产品数量 (个)'
                                    },
                                    beginAtZero: true,
                                    max: 300
                                },
                                y1: {
                                    type: 'linear',
                                    display: true,
                                    position: 'right',
                                    title: {
                                        display: true,
                                        text: '平均价格 ($)'
                                    },
                                    grid: {
                                        drawOnChartArea: false,
                                    },
                                    beginAtZero: true,
                                    max: 1000
                                }
                            }
                        }
                    });
                }
            }
        });

        // 数字滚动动画
        function animateNumber(element, target, duration = 2000, decimals = 0) {
            const start = 0;
            const startTime = performance.now();

            function updateNumber(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);

                // 使用缓动函数
                const easeOutQuart = 1 - Math.pow(1 - progress, 4);
                const current = start + (target - start) * easeOutQuart;

                if (decimals > 0) {
                    element.textContent = current.toFixed(decimals);
                } else {
                    element.textContent = Math.floor(current);
                }

                if (progress < 1) {
                    requestAnimationFrame(updateNumber);
                } else {
                    if (decimals > 0) {
                        element.textContent = target.toFixed(decimals);
                    } else {
                        element.textContent = target;
                    }
                }
            }

            requestAnimationFrame(updateNumber);
        }

        // 初始化数字滚动动画
        function initNumberCounters() {
            const counters = document.querySelectorAll('.number-counter');
            counters.forEach(counter => {
                const target = parseFloat(counter.getAttribute('data-target'));
                if (target) {
                    // 检查是否是小数
                    const decimals = target % 1 !== 0 ? 1 : 0;
                    // 延迟启动动画
                    setTimeout(() => {
                        animateNumber(counter, target, 2000, decimals);
                    }, 1000);
                }
            });
        }

        // 初始化渠道雷达图
        function initChannelRadarChart() {
            const radarCanvas = document.getElementById('channelRadarChart');
            if (!radarCanvas) return;

            // 设置画布尺寸
            radarCanvas.width = 500;
            radarCanvas.height = 400;

            const ctx = radarCanvas.getContext('2d');

            new Chart(ctx, {
                type: 'radar',
                data: {
                    labels: ['价格敏感度', '服务要求', '产品丰富度', '品质要求', '进入门槛'],
                    datasets: [
                        {
                            label: 'Home Depot',
                            data: [8, 4, 9, 6, 5],
                            backgroundColor: 'rgba(0, 115, 230, 0.2)',
                            borderColor: '#0073e6',
                            borderWidth: 2,
                            pointBackgroundColor: '#0073e6',
                            pointBorderColor: '#0073e6',
                            pointRadius: 4
                        },
                        {
                            label: "Lowe's",
                            data: [5, 9, 6, 8, 7],
                            backgroundColor: 'rgba(0, 166, 81, 0.2)',
                            borderColor: '#00a651',
                            borderWidth: 2,
                            pointBackgroundColor: '#00a651',
                            pointBorderColor: '#00a651',
                            pointRadius: 4
                        },
                        {
                            label: 'Menards',
                            data: [10, 2, 3, 4, 3],
                            backgroundColor: 'rgba(255, 107, 53, 0.2)',
                            borderColor: '#ff6b35',
                            borderWidth: 2,
                            pointBackgroundColor: '#ff6b35',
                            pointBorderColor: '#ff6b35',
                            pointRadius: 4
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: true,
                    aspectRatio: 1.25,
                    scales: {
                        r: {
                            beginAtZero: true,
                            min: 0,
                            max: 10,
                            ticks: {
                                stepSize: 2,
                                font: {
                                    size: 12
                                }
                            },
                            pointLabels: {
                                font: {
                                    size: 13
                                }
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: {
                                font: {
                                    size: 14
                                },
                                padding: 20
                            }
                        }
                    }
                }
            });
        }

        // 初始化价格箱线图
        function initPriceBoxplotChart() {
            const canvas = document.getElementById('priceBoxplotChart');
            if (!canvas) return;
            
            const ctx = canvas.getContext('2d');
            new Chart(ctx, {
                type: 'boxplot',
                data: {
                    labels: ['Home Depot', 'Lowe\'s', 'Menards'],
                    datasets: [
                        {
                            label: '智能马桶',
                            backgroundColor: 'rgba(54, 162, 235, 0.5)',
                            borderColor: 'rgb(54, 162, 235)',
                            borderWidth: 1,
                            outlierColor: '#999999',
                            padding: 10,
                            itemRadius: 0,
                            data: [
                                {
                                    min: 333.80,
                                    q1: 624.00,
                                    median: 799.00,
                                    q3: 1287.00,
                                    max: 5646.88,
                                    outliers: []
                                },
                                {
                                    min: 189.00,
                                    q1: 500.06,
                                    median: 750.00,
                                    q3: 1200.00,
                                    max: 7999.98,
                                    outliers: []
                                },
                                {
                                    min: 649.00,
                                    q1: 899.00,
                                    median: 1199.00,
                                    q3: 1599.00,
                                    max: 1990.00,
                                    outliers: []
                                }
                            ]
                        },
                        {
                            label: '智能马桶盖',
                            backgroundColor: 'rgba(255, 159, 64, 0.5)',
                            borderColor: 'rgb(255, 159, 64)',
                            borderWidth: 1,
                            outlierColor: '#999999',
                            padding: 10,
                            itemRadius: 0,
                            data: [
                                {
                                    min: 168.66,
                                    q1: 200.00,
                                    median: 250.00,
                                    q3: 350.00,
                                    max: 494.00,
                                    outliers: []
                                },
                                {
                                    min: 109.98,
                                    q1: 200.00,
                                    median: 300.00,
                                    q3: 450.00,
                                    max: 499.99,
                                    outliers: []
                                },
                                {
                                    min: 86.79,
                                    q1: 200.00,
                                    median: 250.00,
                                    q3: 400.00,
                                    max: 599.99,
                                    outliers: []
                                }
                            ]
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        title: {
                            display: true,
                            text: '各渠道价格分布对比'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '价格 (USD)'
                            }
                        }
                    }
                }
            });
        }

        // 初始化Home Depot散点图
        function initHomeDepotScatterChart() {
            const canvas = document.getElementById('homeDepotScatterChart');
            if (!canvas) return;
            
            const ctx = canvas.getContext('2d');
            new Chart(ctx, {
                type: 'scatter',
                data: {
                    datasets: [
                        {
                            label: '智能马桶',
                            data: [
                                {x: 624, y: 1810, r: 4.7*3, name: 'HOROW T3'},
                                {x: 1287, y: 1294, r: 4.4*3, name: 'ANZZI ENVO Echo'},
                                {x: 1849, y: 478, r: 4.3*3, name: 'ANZZI ENVO Resonance'}
                            ],
                            backgroundColor: 'rgba(255, 99, 132, 0.6)',
                            borderColor: 'rgba(255, 99, 132, 1)',
                            borderWidth: 1
                        },
                        {
                            label: '智能马桶盖',
                            data: [
                                {x: 200, y: 800, r: 4.5*3, name: 'BIO BIDET SlimTwo'},
                                {x: 494, y: 1336, r: 4.8*3, name: 'BIO BIDET Bliss BB-2000'}
                            ],
                            backgroundColor: 'rgba(54, 162, 235, 0.6)',
                            borderColor: 'rgba(54, 162, 235, 1)',
                            borderWidth: 1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        title: {
                            display: true,
                            text: 'Home Depot 产品竞争定位'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const point = context.raw;
                                    return [
                                        point.name || '',
                                        '价格: $' + context.parsed.x,
                                        '评论数: ' + context.parsed.y,
                                        '评分: ' + (point.r / 3).toFixed(1)
                                    ];
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            type: 'linear',
                            position: 'bottom',
                            title: {
                                display: true,
                                text: '价格 (USD)'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: '评论数量'
                            }
                        }
                    }
                }
            });
        }

        // 初始化Lowe's散点图
        function initLowesScatterChart() {
            const canvas = document.getElementById('lowesScatterChart');
            if (!canvas) return;
            
            const ctx = canvas.getContext('2d');
            new Chart(ctx, {
                type: 'scatter',
                data: {
                    datasets: [
                        {
                            label: '智能马桶',
                            data: [
                                {x: 500.06, y: 528, r: 4.4*3, name: 'KOHLER Highline'},
                                {x: 864.12, y: 654, r: 4.4*3, name: 'ANZZI ENVO Echo'}
                            ],
                            backgroundColor: 'rgba(255, 99, 132, 0.6)',
                            borderColor: 'rgba(255, 99, 132, 1)',
                            borderWidth: 1
                        },
                        {
                            label: '智能马桶盖',
                            data: [
                                {x: 150.85, y: 175, r: 4.1*3, name: 'KOHLER PureWash'},
                                {x: 450, y: 300, r: 4.6*3, name: 'TOTO C100'},
                                {x: 493.45, y: 148, r: 5.0*3, name: 'ANZZI Smart Bidet'}
                            ],
                            backgroundColor: 'rgba(54, 162, 235, 0.6)',
                            borderColor: 'rgba(54, 162, 235, 1)',
                            borderWidth: 1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        title: {
                            display: true,
                            text: 'Lowe\'s 产品竞争定位'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const point = context.raw;
                                    return [
                                        point.name || '',
                                        '价格: $' + context.parsed.x,
                                        '评论数: ' + context.parsed.y,
                                        '评分: ' + (point.r / 3).toFixed(1)
                                    ];
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            type: 'linear',
                            position: 'bottom',
                            title: {
                                display: true,
                                text: '价格 (USD)'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: '评论数量'
                            }
                        }
                    }
                }
            });
        }

        // 初始化Menards散点图
        function initMenardsScatterChart() {
            const canvas = document.getElementById('menardsScatterChart');
            if (!canvas) return;
            
            const ctx = canvas.getContext('2d');
            new Chart(ctx, {
                type: 'scatter',
                data: {
                    datasets: [
                        {
                            label: '智能马桶盖',
                            data: [
                                {x: 250, y: 300, r: 4.0*3, name: 'SmartBidet SB-1000'}
                            ],
                            backgroundColor: 'rgba(54, 162, 235, 0.6)',
                            borderColor: 'rgba(54, 162, 235, 1)',
                            borderWidth: 1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        title: {
                            display: true,
                            text: 'Menards 产品竞争定位'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const point = context.raw;
                                    return [
                                        point.name || '',
                                        '价格: $' + context.parsed.x,
                                        '评论数: ' + context.parsed.y,
                                        '评分: ' + (point.r / 3).toFixed(1)
                                    ];
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            type: 'linear',
                            position: 'bottom',
                            title: {
                                display: true,
                                text: '价格 (USD)'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: '评论数量'
                            }
                        }
                    }
                }
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initNumberCounters();
            setTimeout(() => initChannelRadarChart(), 2000);
            setTimeout(() => initPriceBoxplotChart(), 2000);
            setTimeout(() => initHomeDepotScatterChart(), 2000);
            setTimeout(() => initLowesScatterChart(), 2000);
            setTimeout(() => initMenardsScatterChart(), 2000);
        });
    </script>

    <script src="../assets/js/charts.js"></script>
    <script src="../assets/js/navigation.js"></script>
</body>
</html>
