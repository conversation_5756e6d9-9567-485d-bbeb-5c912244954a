# Bejoan北美渠道战略演示文稿 - 项目结构分析

## 1. 项目概述

本项目是一个基于Web的交互式演示文稿（Presentation），用于展示“Bejoan品牌在北美的渠道进入战略”。

它采用单页面应用（SPA）的架构模式。`index.html` 作为主容器或“外壳”，负责承载整个应用的UI框架（如导航栏、控制按钮等），并通过一个 `<iframe>` 动态加载和展示各个章节的具体内容。

这种结构将主控制逻辑与具体幻灯片内容解耦，使得项目结构清晰，易于维护和扩展。


### `index.html`
- **角色**: **应用主入口 / 外壳 (App Shell)**
- **职责**:
    1.  定义演示文稿的整体HTML骨架，包括顶部的导航栏、底部的控制栏以及中间的内容展示区。
    2.  通过 `<link>` 标签引入所有必需的CSS样式文件（如 `common.css`, `index.css`）。
    3.  通过 `<script>` 标签从CDN引入第三方库（如 `Chart.js`, `D3.js`, `ECharts`），为图表功能提供支持。
    4.  包含一个核心的 `<iframe>` 元素 (`#slideFrame`)，作为动态加载并显示各章节内容的“视窗”。
    5.  在 `<body>` 底部按顺序引入项目的所有核心JavaScript文件，确保正确的加载和执行顺序。

### `assets/js/main.js`
- **角色**: **主应用控制器 (Main Controller)**
- **职责**:
    1.  定义并实例化 `PresentationApp` 类，这是整个应用的“大脑”。
    2.  **状态管理**: 维护当前幻灯片编号 (`currentSlide`)、总页数 (`totalSlides`) 和当前章节 (`currentChapter`) 等核心状态。
    3.  **事件绑定**: 监听所有主要的交互事件，包括：
        -   上一页/下一页按钮点击。
        -   顶部章节标签页点击。
        -   键盘按键（左右箭头、空格等）。
        -   移动端触摸滑动。
    4.  **导航逻辑**: 核心方法 `goToSlide()` 负责处理所有页面跳转。它会根据目标幻灯片编号，计算出对应的章节HTML文件路径和页面内的锚点 (`#slide-X`)，然后更新 `<iframe>` 的 `src` 属性来加载新内容。
    5.  **全局实例**: 创建 `window.presentationApp` 全局实例，方便其他脚本或 `<iframe>` 内的子页面调用。

### `assets/js/navigation.js`
- **角色**: **导航增强模块 (Navigation Module)**
- **职责**:
    1.  定义 `NavigationSystem` 类，提供比 `main.js` 基础导航更高级的功能。
    2.  **历史管理**: 记录用户的浏览历史，支持通过浏览器前进/后退按钮在幻灯片之间导航。
    3.  **书签功能**: 允许用户为特定的幻灯片添加书签，并将书签数据存储在 `localStorage` 中，以便长期保存。

### `assets/js/animations.js`
- **角色**: **动画引擎 (Animation Engine)**
- **职责**:
    1.  定义 `AnimationEngine` 类，一个独立的、可复用的动画管理系统。
    2.  **自动触发**: 使用 `IntersectionObserver` API 监测元素是否进入可视区域，当带有 `data-animate` 属性的元素滚动到屏幕上时，自动触发预设的入场动画。
    3.  **动画库**: 提供了一系列预定义的动画效果（如 `fadeIn`, `slideUp`, `typewriter` 等）。
    4.  **全局可用**: 实例化为 `window.animationEngine`，使得在 `<iframe>` 中加载的任何页面都能方便地调用这些动画效果。

### `assets/js/charts.js`
- **角色**: **图表管理器 (Chart Manager)**
- **职责**:
    1.  定义 `ChartManager` 类，作为 `Chart.js` 库的封装层。
    2.  **简化创建**: 提供了一系列便捷方法（如 `createMarketSizePieChart`, `createGMVBarChart`），用于快速创建特定类型的图表。
    3.  **统一样式**: 预设了统一的颜色、渐变和配���，确保所有图表的视觉风格一致。
    4.  **全局可用**: 实例化为 `window.chartManager`，供 `<iframe>` 内的章节页面调用，以在指定容器中渲染图表。

## 3. 项目运行流程

1.  **启动**: 浏览器加载 `index.html`。
2.  **资源加载**: CSS和所有JS库（第三方和本地）被加载。`<iframe>` 开始加载其初始内容 `chapters/chapter1_background.html`。
3.  **初始化**:
    -   `animations.js`, `charts.js`, `navigation.js` 分别初始化自己的管理器，并挂载到 `window` 对象上。
    -   `main.js` 的 `DOMContentLoaded` 事件被触发，`PresentationApp` 被实例化，并开始监听用户交互。
4.  **交互与导航**:
    -   用户点击“下一页”按钮。
    -   `main.js` 中的事件监听器捕获该点击，并调用 `presentationApp.nextSlide()`。
    -   该方法更新 `currentSlide` 状态，并调用 `goToSlide()`。
    -   `goToSlide()` 方法计算出新的URL（例如，从 `...#slide-1` 变为 `...#slide-2`），并将其设置为 `<iframe>` 的 `src`。
    -   如果切换到新的章节（例如，从第6页到第7��），`<iframe>` 的 `src` 会被更新为一个全新的HTML文件（例如 `chapters/chapter2_battlefield.html#slide-1`），从而加载一个全新的页面。
5.  **动态内容渲染**:
    -   当一个新的章节页面在 `<iframe>` 内加载完成时，它内部的JavaScript代码会执行。
    -   这些代码可以通过 `window.parent.chartManager.create...()` 来调用父页面的图表管理器，从而在当前幻灯片中渲染出数据图表。
    -   同样，页面中带有 `data-animate` 属性的HTML元素会被父页面的 `AnimationEngine` 自动观察和激活，实现滚动入场动画。

这个流程清晰地展示了父页面 (`index.html`) 作为总控制器，而 `<iframe>` 内的子页面作为被动的内容展示单元的协作模式。
