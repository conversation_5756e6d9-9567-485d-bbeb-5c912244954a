/* 主页面样式 */
.presentation-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background: linear-gradient(135deg, var(--background-primary) 0%, var(--background-secondary) 100%);
}

/* 导航栏样式 */
.main-nav {
    display: flex;
    justify-content: flex-start; /* Align items to the start (left) */
    align-items: center;
    padding: var(--spacing-md) var(--spacing-xl);
    background: var(--background-primary);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-light);
    z-index: 100;
    min-height: 80px;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-right: var(--spacing-xl); /* Add space between brand and chapters */
}

.brand-logo {
    display: flex;
    align-items: center;
}

.logo-img {
    height: 50px;
    width: auto;
}

.logo-fallback {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 12px;
}

.logo-icon {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: white;
}

.brand-text h1 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--primary-color);
    margin: 0;
}

.nav-subtitle {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin: 0;
}

/* 头部章节导航 */
.header-chapters {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
    flex-grow: 1; /* Allow chapters to take available space */
    justify-content: flex-start; /* Align chapters to the left within their space */
}

.chapter-tab {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: 8px;
    cursor: pointer;
    transition: all var(--transition-base);
    border: 1px solid transparent;
    min-width: 80px;
    text-align: center;
}

.chapter-tab:hover {
    background: var(--light-blue);
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.chapter-tab.active {
    background: var(--primary-color);
    color: white;
    box-shadow: var(--shadow-medium);
}

.chapter-tab i {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-xs);
    color: var(--primary-color);
}

.chapter-tab.active i {
    color: white;
}

.chapter-tab span {
    font-size: var(--font-size-xs);
    font-weight: 500;
    line-height: 1.2;
}



/* 主内容区域 */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* 幻灯片容器 */
.slide-container {
    flex: 1;
    position: relative;
    background: var(--background-secondary);
    overflow: hidden;
}

#slideFrame {
    width: 100%;
    height: 100%;
    border: none;
    transition: opacity var(--transition-base);
}

/* 透明导航箭头 */
.nav-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 60px;
    height: 60px;
    background: rgba(0, 115, 230, 0.2);
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 1000;
    transition: all 0.3s ease;
    opacity: 0;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.nav-arrow:hover {
    background: rgba(0, 115, 230, 0.9);
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 115, 230, 0.3);
}

.nav-arrow:disabled {
    opacity: 0 !important;
    cursor: not-allowed;
    background: rgba(128, 128, 128, 0.1);
    pointer-events: none;
}

.nav-arrow-left {
    left: 30px;
}

.nav-arrow-right {
    right: 30px;
}

.nav-arrow i {
    font-size: 24px;
    color: white;
    transition: all 0.3s ease;
}

.nav-arrow:disabled i {
    color: rgba(255, 255, 255, 0.3);
}

/* 悬停时显示箭头 */
.slide-container:hover .nav-arrow:not(:disabled) {
    opacity: 0.8;
}

.slide-container:hover .nav-arrow:not(:disabled):hover {
    opacity: 1;
}

/* 移动端始终显示箭头 */
@media (max-width: 768px) {
    .nav-arrow:not(:disabled) {
        opacity: 0.6;
    }

    .nav-arrow:not(:disabled):hover {
        opacity: 1;
    }
}

/* 确保箭头在iframe上方 */
.slide-container {
    position: relative;
}

.slide-container iframe {
    position: relative;
    z-index: 1;
}







/* 加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.95);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 1;
    transition: opacity var(--transition-slow);
}

.loading-overlay.hidden {
    opacity: 0;
    pointer-events: none;
}

.loading-spinner {
    text-align: center;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-md);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-spinner p {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

/* 响应式设计 */




/* 新布局的响应式设计 */
@media (max-width: 1024px) {
    .header-chapters {
        gap: var(--spacing-xs);
    }

    .chapter-tab {
        min-width: 60px;
        padding: var(--spacing-xs) var(--spacing-sm);
    }

    .chapter-tab span {
        font-size: 10px;
    }
}

@media (max-width: 768px) {
    .main-nav {
        flex-direction: column;
        gap: var(--spacing-md);
        padding: var(--spacing-md);
        min-height: auto;
    }

    .nav-brand {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }

    .brand-text h1 {
        font-size: var(--font-size-lg);
    }

    .header-chapters {
        flex-wrap: wrap;
        justify-content: center;
        gap: var(--spacing-xs);
    }

    .chapter-tab {
        min-width: 50px;
        padding: var(--spacing-xs);
    }

    .chapter-tab span {
        font-size: 9px;
    }

    .nav-arrow {
        width: 50px;
        height: 50px;
    }

    .nav-arrow-left {
        left: 10px;
    }

    .nav-arrow-right {
        right: 10px;
    }

    .nav-arrow i {
        font-size: var(--font-size-lg);
    }
}

@media (max-width: 480px) {
    .main-nav {
        padding: var(--spacing-sm);
    }

    .brand-text h1 {
        font-size: var(--font-size-base);
    }

    .nav-subtitle {
        font-size: 10px;
    }

    .header-chapters {
        gap: 2px;
    }

    .chapter-tab {
        min-width: 40px;
        padding: 4px;
    }

    .chapter-tab i {
        font-size: var(--font-size-sm);
        margin-bottom: 2px;
    }

    .chapter-tab span {
        font-size: 8px;
    }

    .slide-counter {
        font-size: 10px;
        padding: var(--spacing-xs) var(--spacing-sm);
    }

    .nav-arrow {
        width: 40px;
        height: 40px;
    }

    .nav-arrow i {
        font-size: var(--font-size-base);
    }
}
