<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第五章：战略选择与行动方案</title>
    <link rel="stylesheet" href="../assets/css/common.css">
    <link rel="stylesheet" href="../assets/css/slide.css">
    <link rel="stylesheet" href="../assets/css/chapter5-enhanced.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        /* 战略对比矩阵样式 */
        .strategy-comparison-matrix {
            margin: 30px 0;
            background: #fff;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .matrix-header {
            background: var(--primary-color);
            margin-bottom: 0;
            color: white;
            padding: 20px;
            text-align: center;
        }

        .matrix-header h3 {
            margin: 0;
            font-size: 22px;
            font-weight: 600;
            color: #fff;
        }

        .strategy-matrix-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        .strategy-matrix-table th {
            background: #f8f9fa;
            padding: 16px 12px;
            text-align: center;
            font-size: var(--font-size-lg);
            font-weight: 600;
            color: #2d3748;
            border-bottom: 2px solid #e2e8f0;
        }

        .strategy-matrix-table td {
            padding: 14px 12px;
            border-bottom: 1px solid #e2e8f0;
            vertical-align: top;
        }

        .strategy-matrix-table td:first-child {
            background: #f7fafc;
            font-weight: 600;
            color: #2d3748;
            width: 25%;
        }

        .strategy-matrix-table tr:hover {
            background: #f1f5f9;
        }

        /* 决策框架样式 */
        .decision-framework {
            margin: 40px 0;
        }

        .decision-framework h3 {
            text-align: center;
            color: #2d3748;
            margin-bottom: 24px;
            font-size: 20px;
        }

        .decision-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
        }

        .decision-card {
            background: #fff;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            border-top: 4px solid;
            transition: transform 0.3s ease;
        }

        .decision-card:hover {
            transform: translateY(-4px);
        }

        .decision-card.strategy-a {
            border-top-color: #3182ce;
        }

        .decision-card.strategy-b {
            border-top-color: #38a169;
        }

        .decision-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .decision-header h4 {
            color: #2d3748;
            margin: 0;
            font-size: 16px;
        }

        .strategy-badge {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            color: white;
        }

        .strategy-badge.conservative {
            background: #3182ce;
        }

        .strategy-badge.balanced {
            background: #38a169;
        }

        .decision-criteria {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .decision-criteria li {
            padding: 8px 0;
            padding-left: 20px;
            position: relative;
            color: #4a5568;
            line-height: 1.5;
        }

        .decision-criteria li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #38a169;
            font-weight: bold;
        }

        /* 团队优化样式 */
        .team-optimization {
            margin: 40px 0;
            padding: 24px;
            background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
            border-radius: 16px;
            border: 1px solid #e2e8f0;
        }

        .team-optimization h3 {
            text-align: center;
            color: #2d3748;
            margin-bottom: 24px;
            font-size: 20px;
        }

        .optimization-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .optimization-item {
            background: #fff;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.05);
            border-left: 4px solid #3182ce;
        }

        .optimization-item.cost-control {
            grid-column: 1 / -1;
            border-left-color: #f6ad55;
            background: linear-gradient(135deg, #fff 0%, #fffbf5 100%);
        }

        .optimization-item h4 {
            color: #2d3748;
            margin-bottom: 12px;
            font-size: 16px;
        }

        .optimization-item p {
            color: #4a5568;
            line-height: 1.6;
            margin: 0;
        }

        /* 风险缓解样式 */
        .risk-mitigation {
            margin: 40px 0;
        }

        .risk-mitigation h3 {
            text-align: center;
            color: #2d3748;
            margin-bottom: 24px;
            font-size: 20px;
        }

        .risk-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
        }

        .risk-item {
            background: #fff;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            border-top: 4px solid;
            transition: transform 0.3s ease;
        }

        .risk-item:hover {
            transform: translateY(-2px);
        }

        .risk-item.strategy-a-risk {
            border-top-color: #3182ce;
        }

        .risk-item.strategy-b-risk {
            border-top-color: #38a169;
        }

        .risk-header {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .risk-header i {
            color: #3182ce;
            font-size: 20px;
        }

        .risk-item.strategy-b-risk .risk-header i {
            color: #38a169;
        }

        .risk-header h4 {
            color: #2d3748;
            margin: 0;
            font-size: 16px;
        }

        .risk-item p {
            color: #4a5568;
            line-height: 1.6;
            margin: 0;
        }

        .paths-explanation {
            margin-top: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #fff3cd 0%, #fef7e0 100%);
            border-radius: 12px;
            border-left: 4px solid #f6ad55;
        }

        .paths-explanation p {
            margin: 0;
            color: #744210;
            line-height: 1.6;
        }

        /* 执行计划仪表板样式 */
        .execution-dashboard {
            margin: 40px 0;
        }

        .execution-dashboard h3 {
            text-align: center;
            color: #2d3748;
            margin-bottom: 32px;
            font-size: 20px;
        }

        .phases-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 24px;
        }

        .phase-card {
            background: #fff;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            border-left: 6px solid;
            transition: transform 0.3s ease;
        }

        .phase-card:hover {
            transform: translateY(-2px);
        }

        .phase-card.phase-1 {
            border-left-color: #3182ce;
        }

        .phase-card.phase-2 {
            border-left-color: #38a169;
        }

        .phase-card.phase-3 {
            border-left-color: #d69e2e;
        }

        .phase-header {
            margin-bottom: 16px;
        }

        .phase-header h4 {
            color: #2d3748;
            margin-bottom: 8px;
            font-size: 16px;
        }

        .phase-investment {
            color: #3182ce;
            font-weight: 600;
            font-size: 14px;
        }

        .phase-content {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .phase-metrics,
        .phase-resources,
        .phase-outcomes {
            color: #4a5568;
            font-size: 14px;
            line-height: 1.5;
        }

        /* 投入合理性分析样式 */
        .investment-analysis {
            margin: 40px 0;
            padding: 24px;
            background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
            border-radius: 16px;
            border: 1px solid #e2e8f0;
        }

        .investment-analysis h3 {
            text-align: center;
            color: #2d3748;
            margin-bottom: 24px;
            font-size: 20px;
        }

        .analysis-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }

        .analysis-item {
            background: #fff;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.05);
            border-left: 4px solid #3182ce;
        }

        .analysis-item h4 {
            color: #2d3748;
            margin-bottom: 12px;
            font-size: 16px;
        }

        .analysis-item p {
            color: #4a5568;
            line-height: 1.6;
            margin: 0;
            font-size: 14px;
        }

        /* 里程碑样式 */
        .milestone-breakdown {
            margin: 40px 0;
        }

        .milestone-breakdown h4 {
            text-align: center;
            color: #2d3748;
            margin-bottom: 32px;
            font-size: 20px;
        }

        .milestone-item {
            background: #fff;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            border-top: 4px solid #3182ce;
        }

        .milestone-item h5 {
            color: #2d3748;
            margin-bottom: 16px;
            font-size: 16px;
        }

        .milestone-investment {
            color: #3182ce;
            font-weight: 600;
            margin-bottom: 12px;
        }

        .milestone-item ul {
            list-style: none;
            padding: 0;
            margin: 12px 0;
        }

        .milestone-item li {
            padding: 4px 0;
            padding-left: 20px;
            position: relative;
            color: #4a5568;
            font-size: 14px;
        }

        .milestone-item li::before {
            content: '•';
            position: absolute;
            left: 0;
            color: #3182ce;
            font-weight: bold;
        }

        .milestone-actions {
            background: #f7fafc;
            padding: 16px;
            border-radius: 8px;
            margin: 12px 0;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .decision-grid,
            .optimization-grid,
            .risk-grid,
            .analysis-grid {
                grid-template-columns: 1fr;
            }

            .optimization-item.cost-control {
                grid-column: 1;
            }

            .strategy-matrix-table {
                font-size: 12px;
            }

            .strategy-matrix-table th,
            .strategy-matrix-table td {
                padding: 10px 8px;
            }

            .phases-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>

<body>
    <!-- 幻灯片1：第5章首页 -->
    <div id="slide-1" class="slide">
        <div class="slide-content center-content">
            <!-- 背景图片 -->
            <div class="hero-background">
                <div class="bg-image"></div>
                <div class="bg-overlay"></div>
                <div class="bg-pattern animate-fadeIn animate-delay-100"></div>
                <div class="bg-gradient animate-fadeIn animate-delay-200"></div>
            </div>

            <!-- 主标题区域 -->
            <div class="hero-section animate-slideUp animate-delay-300" style="margin-bottom: 40px;">
                <div class="chapter-badge animate-scaleIn animate-delay-400">第五章</div>
                <h1 class="hero-title animate-slideUp animate-delay-500">
                    战略选择与行动方案
                </h1>
                <p class="hero-subtitle animate-slideUp animate-delay-600">
                    两条风险与回报各异的战略路径，从试点验证到激进扩张
                </p>
                <div class="hero-description animate-slideUp animate-delay-700">
                    <p>基于深度分析，为Bejoan规划两条战略路径：市场试点与验证、均衡增长。推荐"均衡增长"策略，通过智能马桶和马桶盖双线并进，投入$2.5M，12个月冲击1.5%市场份额。</p>
                </div>
            </div>



            <!-- 核心洞察 -->
            <div class="content-block core-insights animate-slideUp animate-delay-1250">
                <h3 class="insights-title">核心洞察</h3>
                <div class="insights-grid">
                    <div class="insight-item animate-scaleIn animate-delay-1300">
                        <div class="insight-number">01</div>
                        <div class="insight-content">
                            <p>两条风险与回报各异的<span class="insight-highlight">战略路径</span>，从试点验证到激进扩张</p>
                        </div>
                    </div>
                    <div class="insight-item animate-scaleIn animate-delay-1350">
                        <div class="insight-number">02</div>
                        <div class="insight-content">
                            <p>推荐<span class="insight-highlight">"均衡增长"</span>策略，通过智能马桶和马桶盖<span class="insight-highlight">双线并进</span>，投入$2.5M，12个月冲击1.5%市场份额</p>
                        </div>
                    </div>
                    <div class="insight-item animate-scaleIn animate-delay-1400">
                        <div class="insight-number">03</div>
                        <div class="insight-content">
                            <p>立即启动两项高优先级<span class="insight-highlight">补充调研</span>，确保战略成功落地</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 章节概要 -->
            <div class="content-block chapter-overview animate-slideUp animate-delay-1300">
                <h3 class="overview-title">本章节内容概要</h3>
                <div class="chapter-grid">
                    <div class="chapter-item">
                        <div class="chapter-number">5.1</div>
                        <div class="chapter-content">
                            <h4>两条战略路径</h4>
                            <p>市场试点与验证、均衡增长的详细对比分析</p>
                        </div>
                    </div>
                    <div class="chapter-item">
                        <div class="chapter-number">5.2</div>
                        <div class="chapter-content">
                            <h4>投资回报分析</h4>
                            <p>各路径的资源需求、时间周期与预期回报</p>
                        </div>
                    </div>
                    <div class="chapter-item">
                        <div class="chapter-number">5.3</div>
                        <div class="chapter-content">
                            <h4>风险评估矩阵</h4>
                            <p>市场风险、竞争风险与执行风险的全面评估</p>
                        </div>
                    </div>
                    <div class="chapter-item">
                        <div class="chapter-number">5.4</div>
                        <div class="chapter-content">
                            <h4>决策建议</h4>
                            <p>基于公司现状的最优路径选择建议</p>
                        </div>
                    </div>
                    <div class="chapter-item">
                        <div class="chapter-number">5.5</div>
                        <div class="chapter-content">
                            <h4>实施时间表</h4>
                            <p>18个月分阶段实施的详细时间规划</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 装饰元素 -->
            <div class="hero-decorations">
                <div class="decoration-element circle animate-float animate-delay-1500"></div>
                <div class="decoration-element triangle animate-pulse animate-delay-1700"></div>
                <div class="decoration-element square animate-float animate-delay-1900"></div>
            </div>
        </div>
    </div>

    <!-- 幻灯片2：三条战略路径 -->
    <div class="slide" id="slide-2">
        <div class="slide-header">
            <h1 class="slide-title">基于分析，我们为Bejoan规划了两条风险与回报各异的战略路径</h1>
            <p class="slide-subtitle">战略不是唯一的，决策层需要清晰的选项来匹配公司的风险偏好和资源状况。</p>
        </div>
        <div class="slide-content">
            <div class="content-block" data-animate="slideUp">
                <div class="strategic-paths">
                    <div class="paths-insight">
                        <p class="insight-text">北美之路有两条：稳健的"试点之路"，和平衡的"增长之路"。基于前期分析，我们为每条路径设计了详细的执行方案。</p>
                    </div>

                    <!-- 战略路径对比矩阵 -->
                    <div class="strategy-comparison-matrix" data-animate="cardFloat" data-delay="200">
                        <div class="matrix-header">
                            <h3>战略路径对比矩阵</h3>
                        </div>
                        
                        <div class="comparison-table">
                            <table class="strategy-matrix-table">
                                <thead>
                                    <tr>
                                        <th>维度</th>
                                        <th>战略A：市场试点</th>
                                        <th>战略B：均衡增长</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><strong>投入规模</strong></td>
                                        <td>$1M (12个月)</td>
                                        <td>$2.5M (12个月)</td>
                                    </tr>
                                    <tr>
                                        <td><strong>目标份额</strong></td>
                                        <td>&lt;0.5%</td>
                                        <td>1.5%</td>
                                    </tr>
                                    <tr>
                                        <td><strong>渠道策略</strong></td>
                                        <td>Home Depot单一渠道深耕</td>
                                        <td>三大渠道(60/30/10)并进</td>
                                    </tr>
                                    <tr>
                                        <td><strong>产品策略</strong></td>
                                        <td>智能马桶盖先行，智能马桶跟进</td>
                                        <td>双产品线同步推进</td>
                                    </tr>
                                    <tr>
                                        <td><strong>团队规模</strong></td>
                                        <td>6-8人精简团队</td>
                                        <td>12-15人专业团队</td>
                                    </tr>
                                    <tr>
                                        <td><strong>风险等级</strong></td>
                                        <td>低风险，可控试点</td>
                                        <td>中等风险，平衡增长</td>
                                    </tr>
                                    <tr>
                                        <td><strong>预期ROI</strong></td>
                                        <td>15% (学习价值为主)</td>
                                        <td>22% (市场突破为主)</td>
                                    </tr>
                                    <tr>
                                        <td><strong>时间节奏</strong></td>
                                        <td>4个月一个里程碑，渐进式</td>
                                        <td>4个月一个里程碑，加速式</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 选择决策框架 -->
                    <div class="decision-framework" data-animate="slideUp" data-delay="400">
                        <h3>选择决策框架</h3>
                        
                        <div class="decision-grid">
                            <div class="decision-card strategy-a">
                                <div class="decision-header">
                                    <h4>选择战略A的情况</h4>
                                    <div class="strategy-badge conservative">保守路径</div>
                                </div>
                                <ul class="decision-criteria">
                                    <li>公司现金流相对紧张，需要控制投入风险</li>
                                    <li>对北美市场了解有限，希望先行验证</li>
                                    <li>团队资源有限，无法同时管理多个渠道</li>
                                    <li>更重视学习和数据积累，而非短期市场份额</li>
                                </ul>
                            </div>

                            <div class="decision-card strategy-b">
                                <div class="decision-header">
                                    <h4>选择战略B的情况</h4>
                                    <div class="strategy-badge balanced">平衡路径</div>
                                </div>
                                <ul class="decision-criteria">
                                    <li>公司有充足资金支持，愿意承担中等风险</li>
                                    <li>对市场机会有信心，希望快速建立市场地位</li>
                                    <li>团队能力较强，可以同时管理多个渠道</li>
                                    <li>更重视市场份额和收入增长，追求规模效应</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- 团队优化策略 -->
                    <div class="team-optimization" data-animate="slideUp" data-delay="600">
                        <h3>团队优化策略</h3>
                        
                        <div class="optimization-grid">
                            <div class="optimization-item">
                                <h4>战略A优化方案</h4>
                                <p>通过一人多岗提升效率，营销和客服可兼岗，数据分析外包给专业机构</p>
                            </div>
                            <div class="optimization-item">
                                <h4>战略B优化方案</h4>
                                <p>采用"核心团队+外包服务"模式，将部分运营支持、内容制作、数据分析外包，重点岗位保持专人专岗</p>
                            </div>
                            <div class="optimization-item cost-control">
                                <h4>成本控制</h4>
                                <p>通过合理的组织架构和外包策略，在保证执行质量的前提下降低30-40%的人员成本</p>
                            </div>
                        </div>
                    </div>

                    <!-- 风险缓解机制 -->
                    <div class="risk-mitigation" data-animate="slideUp" data-delay="800">
                        <h3>风险缓解机制</h3>
                        
                        <div class="risk-grid">
                            <div class="risk-item strategy-a-risk">
                                <div class="risk-header">
                                    <i class="fas fa-shield-alt"></i>
                                    <h4>战略A风险控制</h4>
                                </div>
                                <p>单一渠道深度学习，投入递增，随时可调整策略</p>
                            </div>
                            <div class="risk-item strategy-b-risk">
                                <div class="risk-header">
                                    <i class="fas fa-balance-scale"></i>
                                    <h4>战略B风险控制</h4>
                                </div>
                                <p>分阶段投入，每个里程碑都有明确的成功标准和退出机制</p>
                            </div>
                        </div>
                    </div>

                    <div class="paths-explanation">
                        <p><strong>图表解释：</strong>两条路径都经过严格的财务建模和风险评估，代表了不同的雄心水平和资源配置策略。</p>
                    </div>
                </div>
            </div>
            <div class="content-block" data-animate="fadeIn" data-delay="700">
                <div class="data-source">
                    <p><strong>数据来源：</strong>基于市场规模测算、竞品成功案例分析、行业标准投入产出模型及渠道合作成本调研</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 幻灯片3：战略A - 市场试点 -->
    <div class="slide" id="slide-3" style="display: none;">
        <div class="slide-header">
            <h1 class="slide-title">战略A - 市场试点与验证：12个月投入$1M，低风险验证Home Depot单一渠道</h1>
            <p class="slide-subtitle">这是最保守、最安全的路径，旨在用最小成本试水，并在12个月内完成关键验证。</p>
        </div>
        <div class="slide-content">
            <div class="content-block" data-animate="slideUp">
                <div class="strategy-details">
                    <div class="strategy-insight">
                        <p class="insight-text">如果公司的首要目标是控制风险和快速验证市场，那么此方案最为合适。我们将$1M投资细化为三个4个月的里程碑。</p>
                    </div>

                    <div class="conservative" data-animate="cardFloat" data-delay="200">
                        <div class="dashboard-header">
                            <h3>战略A 核心指标</h3>
                        </div>
                        <div class="dashboard-metrics">
                            <div class="metric-item" data-animate="scaleIn" data-delay="300">
                                <div class="metric-icon">
                                    <i class="fas fa-bullseye"></i>
                                </div>
                                <div class="metric-content">
                                    <div class="metric-label">目标</div>
                                    <div class="metric-value">验证PMF & 建立正向现金流</div>
                                    <div class="metric-detail">产品市场匹配度</div>
                                </div>
                            </div>

                            <div class="metric-item" data-animate="scaleIn" data-delay="400">
                                <div class="metric-icon">
                                    <i class="fas fa-dollar-sign"></i>
                                </div>
                                <div class="metric-content">
                                    <div class="metric-label">投资</div>
                                    <div class="metric-value">~$1M</div>
                                    <div class="metric-detail">12个月周期</div>
                                </div>
                            </div>

                            <div class="metric-item" data-animate="scaleIn" data-delay="500">
                                <div class="metric-icon">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="metric-content">
                                    <div class="metric-label">团队</div>
                                    <div class="metric-value">6-8人</div>
                                    <div class="metric-detail">精简团队</div>
                                </div>
                            </div>

                            <div class="metric-item" data-animate="scaleIn" data-delay="600">
                                <div class="metric-icon">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <div class="metric-content">
                                    <div class="metric-label">回报</div>
                                    <div class="metric-value">
                                        <div>12个月内实现盈利，市场份额<0.5%</div>
                                    </div>
                                </div>

                               
                            </div>
                            <div class="metric-item" data-animate="scaleIn" data-delay="700">
                                <div class="metric-icon">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <div class="metric-content">
                                    <div class="metric-label">风险</div>
                                    <div class="metric-value">低</div>
                                    <div class="metric-detail">可控范围</div>
                                </div>
                            </div>
                        </div>

                        <!-- 执行计划仪表板 (12个月3阶段) -->
                        <div class="execution-dashboard" style="margin: 50px 0 20px 0;" data-animate="slideUp" data-delay="500">
                            <h3>执行计划仪表板 (12个月3阶段)</h3>
                            
                            <div class="phases-grid">
                                <!-- 阶段1 -->
                                <div class="phase-card phase-1">
                                    <div class="phase-header">
                                        <h4>阶段1 (1-4个月): 市场启动</h4>
                                        <div class="phase-investment">投入: $300K | 核心目标: 验证PMF</div>
                                    </div>
                                    <div class="phase-content">
                                        <div class="phase-metrics">
                                            <strong>关键指标:</strong> 首批用户反馈、Home Depot渠道接受度
                                        </div>
                                        <div class="phase-resources">
                                            <strong>资源配置:</strong> 产品27% | 营销40% | 运营33%
                                        </div>
                                        <div class="phase-outcomes">
                                            <strong>预期成果:</strong> 建立基础运营体系，获取市场验证数据
                                        </div>
                                    </div>
                                </div>

                                <!-- 阶段2 -->
                                <div class="phase-card phase-2">
                                    <div class="phase-header">
                                        <h4>阶段2 (5-8个月): 产品优化</h4>
                                        <div class="phase-investment">投入: $350K | 核心目标: 提升竞争力</div>
                                    </div>
                                    <div class="phase-content">
                                        <div class="phase-metrics">
                                            <strong>关键指标:</strong> 产品评分提升、获客成本降低
                                        </div>
                                        <div class="phase-resources">
                                            <strong>资源配置:</strong> 产品29% | 营销43% | 运营20% | 团队8%
                                        </div>
                                        <div class="phase-outcomes">
                                            <strong>预期成果:</strong> 智能马桶盖优化完成，智能马桶准备就绪
                                        </div>
                                    </div>
                                </div>

                                <!-- 阶段3 -->
                                <div class="phase-card phase-3">
                                    <div class="phase-header">
                                        <h4>阶段3 (9-12个月): 稳健增长</h4>
                                        <div class="phase-investment">投入: $350K | 核心目标: 实现盈利</div>
                                    </div>
                                    <div class="phase-content">
                                        <div class="phase-metrics">
                                            <strong>关键指标:</strong> 月度盈利、双产品线销售模式
                                        </div>
                                        <div class="phase-resources">
                                            <strong>资源配置:</strong> 产品26% | 营销40% | 运营23% | 数据11%
                                        </div>
                                        <div class="phase-outcomes">
                                            <strong>预期成果:</strong> 建立可复制增长模式，为扩张准备数据基础
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 投入总体合理性分析 -->
                        <div class="investment-analysis" data-animate="slideUp" data-delay="600">
                            <h3>投入总体合理性分析</h3>
                            <div class="analysis-grid">
                                <div class="analysis-item">
                                    <h4>投入规模对标</h4>
                                    <p>$1M投入对应<0.5%市场份额($1.7M收入)，毛利率25%，净利润$0.15M，ROI达到15%，符合保守试点策略</p>
                                </div>
                                <div class="analysis-item">
                                    <h4>投入结构优化</h4>
                                    <p>营销占41%建立初步认知，产品占27%确保竞争力，运营占24%支撑业务，团队占8%保障执行</p>
                                </div>
                                <div class="analysis-item">
                                    <h4>风险控制优势</h4>
                                    <p>低投入、分阶段验证，每个里程碑投入递增，确保风险可控</p>
                                </div>
                                <div class="analysis-item">
                                    <h4>学习价值</h4>
                                    <p>除财务回报外，获得宝贵的市场数据、用户反馈、运营经验，为后续扩张提供科学依据</p>
                                </div>
                            </div>
                        </div>

                        <div class="strategy-conclusion" data-animate="fadeIn" data-delay="700">
                            <p><strong>图表解释：</strong>此方案的核心是"快速学习与验证"，其产出不仅是财务回报，更是宝贵的市场数据和运营经验，为Bejoan的长期发展奠定基础。</p>
                        </div>
                    </div>
                </div>
                <div class="content-block" data-animate="fadeIn" data-delay="900">
                    <div class="data-source">
                        <p><strong>数据来源：</strong>基于类似规模品牌在北美市场进入成本分析；ROI预测基于保守的市场渗透率及运营效率提升假设</p>
                    </div>
                </div>
            </div>
        </div>

        
    </div>
    <!-- 幻灯片4：战略B - 均衡增长（推荐） -->
    <div class="slide" id="slide-4" style="display: none;">
        <div class="slide-header">
            <h1 class="slide-title">战略B - 均衡增长（核心建议）：12个月投资$2.5M，冲击1.5%市场份额</h1>
            <p class="slide-subtitle">这是我们基于数据分析，认为风险与收益最平衡、最适合Bejoan的路径，旨在12个月内成为市场重要玩家。</p>
        </div>
        <div class="slide-content">
            <div class="content-block" data-animate="slideUp">
                <div class="strategy-details">
                    <div class="strategy-insight">
                        <p class="insight-text">此方案旨在将Bejoan打造为市场一个不容忽视的重要玩家，并建立长期、健康的盈利能力。我们将$2.5M投资细化为三个4个月的里程碑，通过智能马桶和马桶盖双线并进实现均衡增长。</p>
                    </div>

                    <div class="balanced" data-animate="cardFloat" data-delay="200">
                        <div class="dashboard-header">
                            <h3>战略B 核心指标</h3>
                        </div>
                        <div class="dashboard-metrics">
                            <div class="metric-item" data-animate="scaleIn" data-delay="300">
                                <div class="metric-icon">
                                    <i class="fas fa-bullseye"></i>
                                </div>
                                <div class="metric-content">
                                    <div class="metric-label">目标</div>
                                    <div class="metric-value">成为市场重要玩家 & 1.5%市场份额</div>
                                    <div class="metric-detail">12个月达成</div>
                                </div>
                            </div>

                            <div class="metric-item" data-animate="scaleIn" data-delay="400">
                                <div class="metric-icon">
                                    <i class="fas fa-dollar-sign"></i>
                                </div>
                                <div class="metric-content">
                                    <div class="metric-label">投资</div>
                                    <div class="metric-value">~$2.5M</div>
                                    <div class="metric-detail">12个月周期</div>
                                </div>
                            </div>

                            <div class="metric-item" data-animate="scaleIn" data-delay="500">
                                <div class="metric-icon">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="metric-content">
                                    <div class="metric-label">团队</div>
                                    <div class="metric-value">12-15人</div>
                                    <div class="metric-detail">专业团队</div>
                                </div>
                            </div>

                            <div class="metric-item" data-animate="scaleIn" data-delay="600">
                                <div class="metric-icon">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <div class="metric-content">
                                    <div class="metric-label">回报</div>
                                    <div class="metric-value">
                                        <div>12个月内实现1.5%市场份额，ROI预期22%</div>
                                    </div>
                                </div>
                            </div>

                            <div class="metric-item" data-animate="scaleIn" data-delay="700">
                                <div class="metric-icon">
                                    <i class="fas fa-balance-scale"></i>
                                </div>
                                <div class="metric-content">
                                    <div class="metric-label">风险</div>
                                    <div class="metric-value">中等</div>
                                    <div class="metric-detail">可控风险</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="recommended-badge" data-animate="bounceIn" data-delay="800" style="text-align: center; margin-top: 20px;">
                            <span>
                                <i class="fas fa-star"></i> 核心推荐方案
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 详细执行计划 -->
            <div class="content-block milestone-breakdown animate-slideUp animate-delay-1400">
                <h4>详细执行计划 (12个月里程碑)</h4>
                
                <!-- 里程碑1 -->
                <div class="milestone-item">
                    <h5>里程碑1 (1-4个月): 渠道深耕与产品优化</h5>
                    <div class="milestone-investment">投入: ~$800K</div>
                    
                    <div class="milestone-actions">
                        <p><strong>投入详细拆解:</strong></p>
                        <ul>
                            <li>产品研发: $200K (双产品线除臭功能开发、认证、测试)</li>
                            <li>营销推广: $350K (Home Depot $210K, Lowe's $105K, 品牌建设 $35K)</li>
                            <li>渠道建设: $120K (Home Depot slotting费 $45K、Lowe's slotting费 $30K、展示费用 $25K、合规培训费 $20K)</li>
                            <li>运营体系: $80K (物流、客服、库存管理系统)</li>
                            <li>团队建设: $50K (12-15人团队首月启动成本)</li>
                        </ul>
                    </div>
                    
                    <div class="milestone-actions">
                        <p><strong>行动:</strong></p>
                        <ul>
                            <li>渠道策略：重点投入Home Depot (60%) 和 Lowe's (30%) 线上渠道</li>
                            <li>产品策略：智能马桶盖快速补齐除臭功能并上线；智能马桶完成除臭功能研发</li>
                            <li>营销策略：启动大规模线上营销（精准广告、KOL合作），突出按摩+除臭双重优势</li>
                            <li>基础建设：建立客户服务和物流体系，支持两条产品线</li>
                        </ul>
                    </div>
                    
                    <div class="milestone-actions">
                        <p><strong>预期成果:</strong> 智能马桶盖在Home Depot和Lowe's销量提升；除臭功能获得市场认可；初步建立品牌知名度</p>
                    </div>
                </div>

                <!-- 里程碑2 -->
                <div class="milestone-item">
                    <h5>里程碑2 (5-8个月): 市场扩张与品牌建设</h5>
                    <div class="milestone-investment">投入: ~$900K</div>
                    
                    <div class="milestone-actions">
                        <p><strong>投入详细拆解:</strong></p>
                        <ul>
                            <li>产品升级: $180K (智能马桶功能完善、智能马桶盖技术升级)</li>
                            <li>营销扩张: $450K (三渠道营销 $360K, 品牌建设 $90K)</li>
                            <li>渠道拓展: $150K (Menards slotting费 $60K、线下试点展示费 $50K、合规成本 $40K)</li>
                            <li>运营升级: $90K (供应链优化、多渠道管理系统)</li>
                            <li>团队扩充: $30K (专业人员增加、培训成本)</li>
                        </ul>
                    </div>
                    
                    <div class="milestone-actions">
                        <p><strong>行动:</strong></p>
                        <ul>
                            <li>渠道扩张：探索Menards线上渠道（10%投入），以智能马桶盖为主打产品</li>
                            <li>产品推进：智能马桶产品开始在Home Depot小规模测试，强化按摩功能卖点</li>
                            <li>营销升级：进行差异化营销，突出两条产品线的不同优势和适用场景</li>
                            <li>线下拓展：Home Depot/Lowe's部分门店试点，优化供应链和交付效率</li>
                        </ul>
                    </div>
                    
                    <div class="milestone-actions">
                        <p><strong>预期成果:</strong> 扩大市场覆盖率；两条产品线在不同渠道找到最佳定位；提升品牌影响力</p>
                    </div>
                </div>

                <!-- 里程碑3 -->
                <div class="milestone-item">
                    <h5>里程碑3 (9-12个月): 份额冲刺与数据沉淀</h5>
                    <div class="milestone-investment">投入: ~$800K</div>
                    
                    <div class="milestone-actions">
                        <p><strong>投入详细拆解:</strong></p>
                        <ul>
                            <li>产品完善: $160K (双产品线最终优化、新功能开发)</li>
                            <li>营销冲刺: $400K (份额冲刺营销、品牌巩固、用户留存)</li>
                            <li>渠道深化: $120K (Pay-to-stay费用 $50K、展示升级费 $40K、长期合作协议费 $30K)</li>
                            <li>运营优化: $80K (效率提升、成本控制、质量保障)</li>
                            <li>数据分析: $40K (全面数据收集、分析、战略规划支持)</li>
                        </ul>
                    </div>
                    
                    <div class="milestone-actions">
                        <p><strong>行动:</strong></p>
                        <ul>
                            <li>份额冲刺：智能马桶盖巩固领先地位，智能马桶快速放量，共同冲刺1.5%市场份额</li>
                            <li>产品优化：持续优化两条产品线的功能配置和营销策略</li>
                            <li>渠道深化：深化与三大渠道伙伴的合作，建立长期战略关系</li>
                            <li>数据沉淀：全面评估两条产品线的市场表现，为下阶段规划提供数据支持</li>
                        </ul>
                    </div>
                    
                    <div class="milestone-actions">
                        <p><strong>预期成果:</strong> 达到1.5%市场份额目标；两条产品线实现稳健盈利；形成可复制的增长模式</p>
                    </div>
                </div>
            </div>

            <!-- 投入总体合理性分析 -->
            <div class="investment-analysis" data-animate="slideUp" data-delay="1600">
                <h3>投入总体合理性分析</h3>
                <div class="analysis-grid">
                    <div class="analysis-item">
                        <h4>投入规模对标</h4>
                        <p>$2.5M投入对应1.5%市场份额($5.2M收入)，投入产出比1:2.08，符合行业新品牌进入标准</p>
                    </div>
                    <div class="analysis-item">
                        <h4>投入结构优化</h4>
                        <p>营销占47%快速建立市场地位，产品占22%保持技术领先，渠道占16%确保合作深度，运营占10%提升效率，其他占5%</p>
                    </div>
                    <div class="analysis-item">
                        <h4>风险控制机制</h4>
                        <p>分阶段投入，每个里程碑都有明确的成功标准和退出机制，最大化投资安全性</p>
                    </div>
                    <div class="analysis-item">
                        <h4>ROI实现路径</h4>
                        <p>基于1.5%市场份额目标，预计12个月收入$5.2M，毛利率30%，运营成本$1.0M，净利润$0.56M，ROI达到22%</p>
                    </div>
                </div>
            </div>
            <div class="content-block" data-animate="fadeIn" data-delay="1800">
                <div class="strategy-conclusion">
                    <p><strong>图表解释：</strong>此方案是我们基于"四重验证"得出的核心建议，它兼顾了增长速度和经营稳健性，并在12个月内实现关键的市场突破。</p>
                </div>
                <div class="data-source">
                    <p><strong>数据来源：</strong>基于市场规模$34.6亿及1.5%份额目标的收入预测；投资分配基于行业标准及竞品成功案例；ROI预测基于市场渗透率及运营效率提升假设。</p>
                </div>
            </div>
        </div>
    </div>

    

    <!-- 幻灯片6：战略对比与推荐 -->
    <div class="slide" id="slide-5" style="display: none;">
        <div class="slide-header">
            <h1 class="slide-title">两大战略路径对比：我们建议选择"B方案"，因为它最符合长期价值</h1>
            <p class="slide-subtitle">最后，我们将所有选项并列，给出我们明确的、有数据支撑的专业建议。</p>
        </div>
        <div class="slide-content">
            <div class="content-block" data-animate="slideUp">
                <div class="strategy-comparison">
                    <div class="comparison-insight" style="margin-bottom: 10px;">
                        <p class="insight-text">经过全面分析，我们认为战略B在风险控制和增长潜力之间达到了最佳平衡，最适合Bejoan的现状。</p>
                    </div>

                    <div class="chapter5-slide6-comparison-table" data-animate="cardFloat" data-delay="200" style="margin: 0 auto; max-width: 1400px;">
                        <div class="chapter5-slide6-table-header">
                            <h3>三大战略全面对比</h3>
                        </div>
                        <div class="chapter5-slide6-comparison-grid">
                            <div class="chapter5-slide6-comparison-row header">
                                <div class="chapter5-slide6-comparison-cell">维度</div>
                                <div class="chapter5-slide6-comparison-cell">战略A(试点验证)</div>
                                <div class="chapter5-slide6-comparison-cell recommended">战略B(均衡增长)★推荐</div>
                            </div>
                            <div class="chapter5-slide6-comparison-row">
                                <div class="chapter5-slide6-comparison-cell">投资规模</div>
                                <div class="chapter5-slide6-comparison-cell">$1M</div>
                                <div class="chapter5-slide6-comparison-cell recommended">$2.5M</div>
                            </div>
                            <div class="chapter5-slide6-comparison-row">
                                <div class="chapter5-slide6-comparison-cell">时间周期</div>
                                <div class="chapter5-slide6-comparison-cell">12个月</div>
                                <div class="chapter5-slide6-comparison-cell recommended">12个月</div>
                            </div>
                            <div class="chapter5-slide6-comparison-row">
                                <div class="chapter5-slide6-comparison-cell">目标份额</div>
                                <div class="chapter5-slide6-comparison-cell"><0.5%</div>
                                <div class="chapter5-slide6-comparison-cell recommended">1.5%</div>
                            </div>
                            <div class="chapter5-slide6-comparison-row">
                                <div class="chapter5-slide6-comparison-cell">风险等级</div>
                                <div class="chapter5-slide6-comparison-cell low-risk">低</div>
                                <div class="chapter5-slide6-comparison-cell recommended medium-risk">中</div>
                            </div>
                            <div class="chapter5-slide6-comparison-row">
                                <div class="chapter5-slide6-comparison-cell">预期ROI</div>
                                <div class="chapter5-slide6-comparison-cell">15%</div>
                                <div class="chapter5-slide6-comparison-cell recommended">22%</div>
                            </div>
                            <div class="chapter5-slide6-comparison-row">
                                <div class="chapter5-slide6-comparison-cell">团队要求</div>
                                <div class="chapter5-slide6-comparison-cell">6-8人基础团队</div>
                                <div class="chapter5-slide6-comparison-cell recommended">12-15人专业团队</div>
                            </div>
                            <div class="chapter5-slide6-comparison-row">
                                <div class="chapter5-slide6-comparison-cell">成功概率</div>
                                <div class="chapter5-slide6-comparison-cell">85%</div>
                                <div class="chapter5-slide6-comparison-cell recommended">70%</div>
                            </div>
                        </div>

                        <div class="chapter5-slide6-recommendation-badge" data-animate="scaleIn" data-delay="400">
                            <i class="fas fa-star"></i>
                            <span>推荐方案：战略B - 均衡增长</span>
                        </div>

                        <div class="chapter5-slide6-recommendation-reasons" data-animate="slideUp" data-delay="600">
                            <div class="chapter5-slide6-reasons-header">
                                <h3>推荐理由</h3>
                            </div>
                            <div class="chapter5-slide6-reasons-grid">
                                <div class="chapter5-slide6-reason-item">
                                    <div class="chapter5-slide6-reason-icon">
                                        <i class="fas fa-balance-scale"></i>
                                    </div>
                                    <div class="chapter5-slide6-reason-content">
                                        <h4>风险可控</h4>
                                        <p>投资规模适中，通过分阶段投入，不会对公司现金流造成过大压力</p>
                                    </div>
                                </div>
                                <div class="chapter5-slide6-reason-item">
                                    <div class="chapter5-slide6-reason-icon">
                                        <i class="fas fa-chart-line"></i>
                                    </div>
                                    <div class="chapter5-slide6-reason-content">
                                        <h4>回报合理</h4>
                                        <p>基于保守估算，预期ROI为22%，符合消费品行业新产品进入新渠道的现实水平</p>
                                    </div>
                                </div>
                                <div class="chapter5-slide6-reason-item">
                                    <div class="chapter5-slide6-reason-icon">
                                        <i class="fas fa-cogs"></i>
                                    </div>
                                    <div class="chapter5-slide6-reason-content">
                                        <h4>执行可行</h4>
                                        <p>团队规模和能力要求在可控范围内，且有明确的里程碑和投入产出预期</p>
                                    </div>
                                </div>
                                <div class="chapter5-slide6-reason-item">
                                    <div class="chapter5-slide6-reason-icon">
                                        <i class="fas fa-rocket"></i>
                                    </div>
                                    <div class="chapter5-slide6-reason-content">
                                        <h4>产品线优势</h4>
                                        <p>有效利用Bejoan在智能马桶盖领域的既有优势，同时为智能马桶产品线在新渠道的拓展提供合理路径，实现双产品线协同发展</p>
                                    </div>
                                </div>
                                <div class="chapter5-slide6-reason-item">
                                    <div class="chapter5-slide6-reason-icon">
                                        <i class="fas fa-star"></i>
                                    </div>
                                    <div class="chapter5-slide6-reason-content">
                                        <h4>战略价值</h4>
                                        <p>建立长期竞争优势，通过12个月的市场验证和份额积累，为后续的持续增长和品牌溢价奠定坚实基础</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="content-block" data-animate="fadeIn" data-delay="800">
                    <div class="data-source">
                        <p><strong>数据来源：</strong>基于两大战略方案的详细财务模型分析；成功概率基于类似案例的历史数据统计；ROI现实性参考智能卫浴行业细分品牌市场进入案例</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        // 幻灯片导航逻辑
        let currentSlide = 1;
        const totalSlides = 5;

        function showSlide(slideNumber) {
            document.querySelectorAll('.slide').forEach(slide => {
                slide.style.display = 'none';
            });

            const targetSlide = document.getElementById(`slide-${slideNumber}`);
            if (targetSlide) {
                targetSlide.style.display = 'block';
                triggerAnimations(targetSlide);
            }

            currentSlide = slideNumber;
        }

        function triggerAnimations(slide) {
            const animatedElements = slide.querySelectorAll('[data-animate]');
            animatedElements.forEach((element, index) => {
                const delay = parseInt(element.dataset.delay) || 0;
                setTimeout(() => {
                    element.classList.add('animated');

                    // 特殊处理数字动画
                    if (element.classList.contains('number-counter')) {
                        animateNumber(element);
                    }
                }, delay);
            });
        }

        function animateNumber(element) {
            const target = parseInt(element.dataset.target);
            const duration = 2000;
            const start = 0;
            const startTime = performance.now();

            function update(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);
                const current = Math.floor(start + (target - start) * easeOutQuart(progress));
                element.textContent = current;

                if (progress < 1) {
                    requestAnimationFrame(update);
                }
            }

            requestAnimationFrame(update);
        }

        function easeOutQuart(t) {
            return 1 - Math.pow(1 - t, 4);
        }

        // 监听URL hash变化
        function handleHashChange() {
            const hash = window.location.hash;
            const match = hash.match(/#slide-(\d+)/);
            if (match) {
                const slideNumber = parseInt(match[1]);
                if (slideNumber >= 1 && slideNumber <= totalSlides) {
                    showSlide(slideNumber);
                }
            }
        }

        // 初始化
        window.addEventListener('load', () => {
            handleHashChange();
            if (!window.location.hash) {
                showSlide(1);
            }
        });

        window.addEventListener('hashchange', handleHashChange);

        // 导出函数供父页面调用
        window.triggerAnimations = () => {
            const currentSlideElement = document.getElementById(`slide-${currentSlide}`);
            if (currentSlideElement) {
                triggerAnimations(currentSlideElement);
            }
        };
    </script>
</body>

</html>