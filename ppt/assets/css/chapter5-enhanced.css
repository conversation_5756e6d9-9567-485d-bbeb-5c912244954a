/* ===================================
   第5章增强样式 - 战略选择与路径
   ================================== */

/* === 第5章首页样式 === */

/* 背景装饰系统 - 第5章特定图片 */
.bg-image {
    background-image: url('https://images.unsplash.com/photo-1631048499052-e6d9f305d2c0?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D');
}




/* 第5章特定装饰元素 */
.decoration-element.circle {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    top: 10%;
    right: 10%;
}

.decoration-element.triangle {
    width: 0;
    height: 0;
    border-left: 40px solid transparent;
    border-right: 40px solid transparent;
    border-bottom: 70px solid var(--accent-color);
    bottom: 20%;
    left: 15%;
}

.decoration-element.square {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    transform: rotate(45deg);
    top: 60%;
    right: 20%;
}


/* === 第24、25页：战略详情页面增强样式 === */

/* 推荐徽章 */
.recommended-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    color: white;
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--radius-full);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    box-shadow: var(--shadow-lg);
    margin: var(--spacing-lg) auto;
    animation: pulse 2s infinite;
}

.recommended-badge i {
    font-size: var(--font-size-lg);
    animation: sparkle 1.5s ease-in-out infinite;
}

@keyframes sparkle {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

/* 风险警示 */
.risk-warning {
    display: flex;
    align-items: center;
    flex-direction: column;
    gap: var(--spacing-lg);
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1), rgba(255, 193, 7, 0.1));
    border: 2px solid rgba(220, 53, 69, 0.3);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin: var(--spacing-xl) 0;
    position: relative;
    overflow: hidden;
}

.risk-warning::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #dc3545, #ffc107);
}

.warning-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #dc3545, #fd7e14);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    flex-shrink: 0;
    animation: pulse 2s infinite;
}

.warning-content h4 {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    color: #dc3545;
    margin: 0 0 var(--spacing-xs) 0;
}

.warning-content p {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin: 0;
    line-height: var(--line-height-relaxed);
}

/* 战略仪表板 */
.strategy-dashboard {
    background: var(--background-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-2xl);
    border: 2px solid var(--border-light);
    box-shadow: var(--shadow-lg);
    margin: var(--spacing-2xl) 0;
    position: relative;
    overflow: hidden;
}

.strategy-dashboard::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--accent-color));
}

.strategy-dashboard.recommended::before {
    background: linear-gradient(90deg, #ff6b35, #f7931e, #ffcc00);
}

.strategy-dashboard.aggressive::before {
    background: linear-gradient(90deg, #dc3545, #fd7e14, #ffc107);
}

.dashboard-header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
}

.dashboard-header h3 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin: 0 0 var(--spacing-sm) 0;
}

.dashboard-subtitle {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin: 0;
    font-style: italic;
}

.dashboard-metrics {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: var(--spacing-lg);
}

/* 3个指标的情况 */
.dashboard-metrics.three-metrics {
    grid-template-columns: repeat(3, 1fr);
}

/* 4个指标的情况 */
.dashboard-metrics.four-metrics {
    grid-template-columns: repeat(4, 1fr);
}

.metric-item {
    background: var(--background-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    text-align: center;
    border: 1px solid var(--border-light);
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.metric-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-color);
    transform: scaleX(0);
    transition: transform var(--transition-base);
}

.metric-item:hover::before {
    transform: scaleX(1);
}

.metric-item:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-alpha-20);
}

.metric-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-md) auto;
    font-size: var(--font-size-xl);
    transition: all var(--transition-base);
}

.metric-item:hover .metric-icon {
    transform: scale(1.1);
    box-shadow: var(--shadow-md);
}

.metric-content {
    text-align: center;
}

.metric-label {
    font-size: var(--font-size-sm);
    color: var(--text-tertiary);
    font-weight: var(--font-weight-medium);
    margin-bottom: var(--spacing-xs);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.metric-value {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.metric-detail {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    font-style: italic;
}

/* 投资分配明细 */
.strategy-breakdown {
    background: var(--background-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-2xl);
    border: 2px solid var(--border-light);
    box-shadow: var(--shadow-md);
    margin: var(--spacing-2xl) 0;
}

.breakdown-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.breakdown-header h3 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin: 0;
}

.breakdown-content {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg);
}

.investment-category {
    background: var(--background-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-light);
    transition: all var(--transition-base);
}

.investment-category:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
    border-color: var(--primary-alpha-20);
}

.category-header h4 {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    color: var(--primary-color);
    margin: 0 0 var(--spacing-md) 0;
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--primary-alpha-20);
}

.category-items {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.category-items .item {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--background-tertiary);
    border-radius: var(--radius-sm);
    border-left: 3px solid var(--primary-color);
    transition: all var(--transition-base);
}

.category-items .item:hover {
    background: var(--primary-alpha-10);
    transform: translateX(4px);
}

.plan-grid{
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-md);
    margin-top: var(--spacing-md);
}

.plan-item{
    background: var(--background-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-light);
}


/* 响应式设计 - 战略页面 */
@media (max-width: 768px) {
    .dashboard-metrics,
    .dashboard-metrics.three-metrics,
    .dashboard-metrics.four-metrics {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);
    }

    .risk-warning {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }

    .warning-icon {
        width: 50px;
        height: 50px;
        font-size: var(--font-size-lg);
    }

    .breakdown-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .metric-icon {
        width: 50px;
        height: 50px;
        font-size: var(--font-size-lg);
    }

    .metric-value {
        font-size: var(--font-size-xl);
    }

    .strategy-dashboard {
        padding: var(--spacing-lg);
    }

    .strategy-breakdown {
        padding: var(--spacing-lg);
    }
}

@media (max-width: 480px) {
    .dashboard-metrics,
    .dashboard-metrics.three-metrics,
    .dashboard-metrics.four-metrics {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }

    .metric-item {
        padding: var(--spacing-md);
    }

    .metric-icon {
        width: 40px;
        height: 40px;
        font-size: var(--font-size-base);
    }

    .metric-value {
        font-size: var(--font-size-lg);
    }

    .recommended-badge {
        padding: var(--spacing-sm) var(--spacing-lg);
        font-size: var(--font-size-sm);
    }

    /* 第24页响应式 */
    .strategy-insight {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }

    .insight-icon {
        width: 50px;
        height: 50px;
        font-size: var(--font-size-lg);
        margin: 0 auto;
    }

    .primary-metrics {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .secondary-metrics {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .investment-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .category-header {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-sm);
    }

    .category-icon {
        width: 40px;
        height: 40px;
        font-size: var(--font-size-base);
    }

    .strategy-overview,
    .strategy-metrics,
    .investment-breakdown {
        padding: var(--spacing-lg);
    }
}

/* 额外的视觉增强 */
.strategy-insight {
    background: linear-gradient(135deg, rgba(0, 115, 230, 0.05), rgba(0, 166, 81, 0.05));
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    border: 1px solid var(--border-light);
    position: relative;
    overflow: hidden;
}

.strategy-insight::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.insight-text {
    font-size: var(--font-size-base);
    line-height: var(--line-height-relaxed);
    color: var(--text-primary);
    margin: 0;
    font-weight: var(--font-weight-medium);
}

/* 数字滚动动画 */
.metric-value {
    position: relative;
    overflow: hidden;
}

.metric-value.counting {
    animation: countUp 1.5s ease-out;
}

@keyframes countUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* 成功指示器 */
.success-indicator {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    background: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-semibold);
    border: 1px solid rgba(40, 167, 69, 0.3);
}

.success-indicator i {
    font-size: 10px;
}

/* 风险指示器 */
.risk-indicator {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-semibold);
    border: 1px solid rgba(220, 53, 69, 0.3);
}

.risk-indicator i {
    font-size: 10px;
}

/* === 第24页重新设计的布局样式 === */

/* 策略概览区域 */
.strategy-overview {
    background: linear-gradient(135deg, rgba(0, 115, 230, 0.05), rgba(0, 166, 81, 0.05));
    border-radius: var(--radius-xl);
    padding: var(--spacing-2xl);
    border: 2px solid var(--border-light);
    margin-bottom: var(--spacing-2xl);
    position: relative;
    overflow: hidden;
}

.strategy-overview::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.strategy-insight {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
}

.insight-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #ffc107, #ff8c00);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    flex-shrink: 0;
    box-shadow: var(--shadow-md);
}

.insight-content h3 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin: 0 0 var(--spacing-sm) 0;
}

/* 核心指标区域 */
.strategy-metrics {
    background: var(--background-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-2xl);
    border: 2px solid var(--border-light);
    box-shadow: var(--shadow-lg);
    margin-bottom: var(--spacing-2xl);
}

.metrics-header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
}

.metrics-header h3 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin: 0 0 var(--spacing-sm) 0;
}

.metrics-subtitle {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin: 0;
    font-style: italic;
}

/* 主要指标 - 2x2网格 */
.primary-metrics {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
}

.metric-card.primary {
    background: var(--background-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    border: 2px solid var(--border-light);
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.metric-card.primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    transform: scaleX(0);
    transition: transform var(--transition-base);
}

.metric-card.primary:hover::before {
    transform: scaleX(1);
}

.metric-card.primary:hover {
    transform: translateY(-6px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-alpha-20);
}

.metric-icon.investment {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.metric-icon.target {
    background: linear-gradient(135deg, #007bff, #6610f2);
}

.metric-icon.roi {
    background: linear-gradient(135deg, #fd7e14, #e83e8c);
}

.metric-icon.risk {
    background: linear-gradient(135deg, #6c757d, #495057);
}

.metric-trend {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    margin-top: var(--spacing-sm);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-semibold);
}

.metric-trend.positive {
    background: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(40, 167, 69, 0.3);
}

.metric-trend.balanced {
    background: rgba(108, 117, 125, 0.1);
    color: #6c757d;
    border: 1px solid rgba(108, 117, 125, 0.3);
}

/* 次要指标 - 水平布局 */
.secondary-metrics {
    display: flex;
    justify-content: center;
    gap: var(--spacing-2xl);
    padding: var(--spacing-lg);
    background: var(--background-tertiary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-light);
}

.metric-item.secondary {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.metric-icon-small {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-sm);
}

.metric-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.metric-label-small {
    font-size: var(--font-size-xs);
    color: var(--text-tertiary);
    font-weight: var(--font-weight-medium);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.metric-value-small {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
}

/* 投资分配区域 */
.investment-breakdown {
    background: var(--background-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-2xl);
    border: 2px solid var(--border-light);
    box-shadow: var(--shadow-md);
}

.breakdown-header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
}

.breakdown-header h3 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin: 0 0 var(--spacing-sm) 0;
}

.breakdown-subtitle {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin: 0;
    font-style: italic;
}

.investment-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg);
}

.investment-category {
    background: var(--background-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-light);
    transition: all var(--transition-base);
}

.investment-category:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-sm);
    border-color: var(--primary-alpha-20);
}

.category-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 2px solid var(--border-light);
}

.category-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
}

.category-header h4 {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin: 0;
    flex: 1;
}

.category-amount {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-bold);
    color: var(--primary-color);
    background: var(--primary-alpha-10);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-full);
}

/* ===================================
   综合风险分析样式系统
   ================================== */

/* 综合风险分析容器 */
.comprehensive-risk-analysis {
    background: var(--background-primary);
    border-radius: 16px;
    padding: var(--spacing-xl);
    border: 2px solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
    margin: var(--spacing-xl) 0;
    position: relative;
    overflow: hidden;
}

.comprehensive-risk-analysis::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, 
        #dc3545 0%, 
        #fd7e14 50%, 
        #ffc107 100%);
}

/* 风险分析头部 */
.risk-analysis-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
    position: relative;
}

.risk-analysis-header h3 {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-sm) 0;
    letter-spacing: -0.02em;
    position: relative;
    display: inline-block;
}

.risk-analysis-header h3::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background: linear-gradient(90deg, #dc3545, #fd7e14);
    border-radius: 2px;
}

.risk-overview {
    font-size: 1rem;
    color: var(--text-secondary);
    font-weight: 500;
    margin-top: var(--spacing-sm);
}

/* 风险矩阵 */
.risk-matrix {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

/* 风险类别 */
.risk-category {
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.95) 0%, 
        rgba(248, 250, 252, 0.95) 100%);
    border-radius: 16px;
    padding: var(--spacing-xl);
    position: relative;
    transition: all var(--transition-smooth);
    overflow: hidden;
    border: 2px solid transparent;
}

.risk-category::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    transition: all var(--transition-base);
}

.risk-category:hover {
    transform: translateY(-8px);
    box-shadow: 0 16px 40px rgba(0, 0, 0, 0.15);
}

/* 高优先级风险样式 */
.risk-category.high-priority {
    background: linear-gradient(135deg, 
        rgba(220, 53, 69, 0.08) 0%, 
        rgba(220, 53, 69, 0.03) 100%);
    border-color: rgba(220, 53, 69, 0.2);
}

.risk-category.high-priority::before {
    background: linear-gradient(180deg, #dc3545, #c82333);
}

.risk-category.high-priority:hover {
    background: linear-gradient(135deg, 
        rgba(220, 53, 69, 0.15) 0%, 
        rgba(220, 53, 69, 0.05) 100%);
    border-color: rgba(220, 53, 69, 0.4);
}

.risk-category.high-priority .risk-level-icon {
    background: linear-gradient(135deg, #dc3545, #fd7e14);
    color: #ffffff;
}

/* 中优先级风险样式 */
.risk-category.medium-priority {
    background: linear-gradient(135deg, 
        rgba(255, 193, 7, 0.08) 0%, 
        rgba(255, 193, 7, 0.03) 100%);
    border-color: rgba(255, 193, 7, 0.2);
}

.risk-category.medium-priority::before {
    background: linear-gradient(180deg, #ffc107, #e0a800);
}

.risk-category.medium-priority:hover {
    background: linear-gradient(135deg, 
        rgba(255, 193, 7, 0.15) 0%, 
        rgba(255, 193, 7, 0.05) 100%);
    border-color: rgba(255, 193, 7, 0.4);
}

.risk-category.medium-priority .risk-level-icon {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: #ffffff;
}

/* 风险指示器 */
.risk-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.risk-level-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    position: relative;
    overflow: hidden;
    transition: all var(--transition-base);
}

.risk-level-icon::before {
    content: '';
    position: absolute;
    inset: 3px;
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.3) 0%, 
        rgba(255, 255, 255, 0.1) 100%);
    border-radius: 50%;
}

.risk-level-icon i {
    position: relative;
    z-index: 1;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.risk-severity {
    background: linear-gradient(135deg, 
        rgba(var(--primary-color-rgb), 0.1) 0%, 
        rgba(var(--secondary-color-rgb), 0.1) 100%);
    color: var(--text-primary);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
    border: 1px solid rgba(var(--primary-color-rgb), 0.2);
}

/* 风险详情 */
.risk-details h4 {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-sm) 0;
    letter-spacing: -0.01em;
}

.risk-description {
    font-size: 1rem;
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
}

/* 风险指标 */
.risk-metrics {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.risk-metric {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background: rgba(255, 255, 255, 0.7);
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all var(--transition-base);
}

.risk-metric:hover {
    background: rgba(255, 255, 255, 0.9);
    transform: translateX(4px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.metric-label {
    font-size: 0.85rem;
    color: var(--text-secondary);
    font-weight: 600;
}

.metric-value {
    font-size: 0.9rem;
    font-weight: 700;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 16px;
    border: 1px solid transparent;
}

/* 风险等级样式 */
.metric-value.critical {
    background: linear-gradient(135deg, 
        rgba(220, 53, 69, 0.15) 0%, 
        rgba(220, 53, 69, 0.08) 100%);
    color: #dc3545;
    border-color: rgba(220, 53, 69, 0.3);
}

.metric-value.high {
    background: linear-gradient(135deg, 
        rgba(255, 193, 7, 0.15) 0%, 
        rgba(255, 193, 7, 0.08) 100%);
    color: #ffc107;
    border-color: rgba(255, 193, 7, 0.3);
}

.metric-value.moderate {
    background: linear-gradient(135deg, 
        rgba(255, 193, 7, 0.12) 0%, 
        rgba(255, 193, 7, 0.06) 100%);
    color: #e0a800;
    border-color: rgba(255, 193, 7, 0.25);
}

.metric-value.medium {
    background: linear-gradient(135deg, 
        rgba(255, 193, 7, 0.15) 0%, 
        rgba(255, 193, 7, 0.08) 100%);
    color: #ffc107;
    border-color: rgba(255, 193, 7, 0.3);
}

/* 缓解措施 */
.mitigation-measures {
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.8) 0%, 
        rgba(248, 250, 252, 0.8) 100%);
    border-radius: 12px;
    padding: var(--spacing-lg);
    border: 2px solid rgba(var(--primary-color-rgb), 0.1);
    position: relative;
    overflow: hidden;
}

.mitigation-measures::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.measure-title {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.measure-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.measure-item {
    background: rgba(255, 255, 255, 0.9);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: 8px;
    border-left: 3px solid var(--primary-color);
    font-size: 0.9rem;
    color: var(--text-primary);
    font-weight: 500;
    transition: all var(--transition-base);
    position: relative;
}

.measure-item::before {
    content: '●';
    position: absolute;
    left: 8px;
    color: var(--primary-color);
    font-weight: bold;
}

.measure-item {
    padding-left: 24px;
}

.measure-item:hover {
    background: rgba(255, 255, 255, 1);
    transform: translateX(4px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 风险总结 */
.risk-summary {
    padding: var(--spacing-lg);
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.6) 0%, 
        rgba(248, 250, 252, 0.6) 100%);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.8);
}

.summary-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-lg);
}

.stat-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    transition: all var(--transition-base);
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 1.25rem;
    flex-shrink: 0;
    box-shadow: 0 6px 16px rgba(var(--primary-color-rgb), 0.3);
}

.stat-content {
    flex: 1;
}

.stat-value {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    font-size: 0.85rem;
    color: var(--text-secondary);
    font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 992px) {
    .comprehensive-risk-analysis {
        padding: var(--spacing-lg);
    }
    
    .risk-analysis-header h3 {
        font-size: 1.5rem;
    }
    
    .risk-matrix {
        gap: var(--spacing-lg);
    }
    
    .risk-category {
        padding: var(--spacing-lg);
    }
    
    .risk-level-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
    
    .risk-details h4 {
        font-size: 1.125rem;
    }
    
    .risk-metrics {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }
}

@media (max-width: 768px) {
    .comprehensive-risk-analysis {
        margin: var(--spacing-lg) 0;
        padding: var(--spacing-md);
    }
    
    .risk-analysis-header {
        margin-bottom: var(--spacing-lg);
    }
    
    .risk-analysis-header h3 {
        font-size: 1.25rem;
    }
    
    .risk-overview {
        font-size: 0.9rem;
    }
    
    .risk-matrix {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
        margin-bottom: var(--spacing-lg);
    }
    
    .risk-category {
        padding: var(--spacing-md);
    }
    
    .risk-indicator {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-sm);
    }
    
    .risk-level-icon {
        width: 45px;
        height: 45px;
        font-size: 1.125rem;
    }
    
    .risk-severity {
        font-size: 0.8rem;
        padding: var(--spacing-xs) var(--spacing-sm);
    }
    
    .risk-details h4 {
        font-size: 1rem;
        text-align: center;
    }
    
    .risk-description {
        font-size: 0.9rem;
        text-align: center;
    }
    
    .risk-metric {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-xs);
        padding: var(--spacing-sm);
    }
    
    .risk-metric:hover {
        transform: translateY(-2px);
    }
    
    .metric-label {
        font-size: 0.8rem;
    }
    
    .metric-value {
        font-size: 0.85rem;
    }
    
    .mitigation-measures {
        padding: var(--spacing-md);
    }
    
    .measure-item {
        font-size: 0.85rem;
        padding: var(--spacing-xs) var(--spacing-sm);
        padding-left: 20px;
    }
    
    .summary-stats {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }
    
    .stat-item {
        padding: var(--spacing-sm);
        gap: var(--spacing-sm);
    }
    
    .stat-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
    
    .stat-value {
        font-size: 1.125rem;
    }
    
    .stat-label {
        font-size: 0.8rem;
    }
}

/* 动画效果 */
@keyframes risk-analysis-fade-in {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes risk-category-slide-in {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes measure-appear {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.comprehensive-risk-analysis {
    animation: risk-analysis-fade-in 0.8s ease-out;
}

.risk-category {
    animation: risk-category-slide-in 0.6s ease-out;
    animation-fill-mode: both;
}

.risk-category.high-priority {
    animation-delay: 0.1s;
}

.risk-category.medium-priority {
    animation-delay: 0.2s;
}

.measure-item {
    animation: measure-appear 0.5s ease-out;
    animation-fill-mode: both;
}

.measure-item:nth-child(1) { animation-delay: 0.3s; }
.measure-item:nth-child(2) { animation-delay: 0.4s; }
.measure-item:nth-child(3) { animation-delay: 0.5s; }

.stat-item {
    animation: risk-analysis-fade-in 0.6s ease-out;
    animation-fill-mode: both;
}

.stat-item:nth-child(1) { animation-delay: 0.6s; }
.stat-item:nth-child(2) { animation-delay: 0.7s; }
.stat-item:nth-child(3) { animation-delay: 0.8s; }

/* 悬停增强效果 */
.risk-category:hover .risk-level-icon {
    transform: scale(1.1);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.2);
}

.risk-category:hover .metric-value {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.3);
}

/* 特殊效果 */
@keyframes warning-pulse {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.4);
    }
    50% {
        box-shadow: 0 0 0 10px rgba(220, 53, 69, 0);
    }
}

.risk-category.high-priority:hover {
    animation: warning-pulse 2s infinite;
}

/* 进度指示器 */
.risk-category::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    transition: width 0.6s ease-out;
}

.risk-category:hover::after {
    width: 100%;
}

/* ===================================
   第5章第6页 - 三大战略全面对比表格样式
   ================================== */

/* 对比表格容器 */
.chapter5-slide6-comparison-table {
    background: var(--background-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-2xl);
    border: 2px solid var(--border-light);
    box-shadow: var(--shadow-lg);
    margin: var(--spacing-2xl) 0;
    position: relative;
    overflow: hidden;
}

.chapter5-slide6-comparison-table::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--accent-color));
}

/* 表格头部 */
.chapter5-slide6-table-header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
}

.chapter5-slide6-table-header h3 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin: 0;
    position: relative;
    display: inline-block;
}

.chapter5-slide6-table-header h3::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--radius-full);
}

/* 对比网格 */
.chapter5-slide6-comparison-grid {
    display: grid;
    grid-template-columns: 1fr 1.5fr 1.5fr;
    gap: 1px;
    background: var(--border-color);
    border-radius: var(--radius-lg);
    overflow: hidden;
    margin-bottom: var(--spacing-2xl);
    box-shadow: var(--shadow-md);
}

/* 对比行 */
.chapter5-slide6-comparison-row {
    display: contents;
}
.chapter5-slide6-comparison-row>div:nth-child(1){
    background: #f7fafc;
}
/* 对比单元格 */
.chapter5-slide6-comparison-cell {
    background: var(--background-primary);
    padding: var(--spacing-md);
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    transition: all var(--transition-base);
    position: relative;
}

/* 表头单元格 */
.chapter5-slide6-comparison-row.header .chapter5-slide6-comparison-cell {
    background: var(--background-secondary);
    font-weight: var(--font-weight-bold);
    font-size: var(--font-size-base);
    color: var(--text-primary);
    padding: var(--spacing-md);
}

/* 推荐列样式 */
.chapter5-slide6-comparison-cell.recommended {
    font-weight: var(--font-weight-semibold);
    color: var(--accent-color);
    position: relative;
}

/* Removed star from recommended cells
.chapter5-slide6-comparison-cell.recommended::before {
    content: '★';
    position: absolute;
    top: 4px;
    right: 4px;
    color: var(--accent-color);
    font-size: 12px;
    font-weight: bold;
} */

/* 风险等级样式 */
.chapter5-slide6-comparison-cell.low-risk {
    color: var(--success-color);
    font-weight: var(--font-weight-semibold);
    border-radius: var(--radius-sm);
}

.chapter5-slide6-comparison-cell.medium-risk {
    color: #e0a800;
    font-weight: var(--font-weight-semibold);
    border-radius: var(--radius-sm);
}

.chapter5-slide6-comparison-cell.high-risk {
    color: #dc3545;
    font-weight: var(--font-weight-semibold);
    border-radius: var(--radius-sm);
}

/* 悬停效果 */
.chapter5-slide6-comparison-cell:hover {
    background: var(--light-blue);
    transform: scale(1.02);
    z-index: 10;
    box-shadow: var(--shadow-sm);
}

.chapter5-slide6-comparison-cell.recommended:hover {

    transform: scale(1.05);
    box-shadow: var(--shadow-md);
}

/* 推荐徽章 */
.chapter5-slide6-recommendation-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    background: linear-gradient(135deg, var(--accent-color), #f7931e);
    color: white;
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--radius-full);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    box-shadow: var(--shadow-lg);
    margin: 0 auto var(--spacing-2xl) auto;
    animation: pulse 2s infinite;
    position: relative;
}

.chapter5-slide6-recommendation-badge::before {
    content: '';
    position: absolute;
    inset: -2px;
    background: linear-gradient(135deg, var(--accent-color), #f7931e);
    border-radius: var(--radius-full);
    z-index: -1;
    opacity: 0.3;
    animation: pulse 2s infinite alternate;
}

.chapter5-slide6-recommendation-badge i {
    font-size: var(--font-size-lg);
    animation: sparkle 1.5s ease-in-out infinite;
}

/* 推荐理由容器 */
.chapter5-slide6-recommendation-reasons {
    background: var(--background-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-2xl);
    border: 1px solid var(--border-light);
    position: relative;
    overflow: hidden;
}

.chapter5-slide6-recommendation-reasons::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--accent-color), #f7931e);
}

/* 理由头部 */
.chapter5-slide6-reasons-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.chapter5-slide6-reasons-header h3 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin: 0;
    position: relative;
    display: inline-block;
}

.chapter5-slide6-reasons-header h3::after {
    content: '';
    position: absolute;
    bottom: -6px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 2px;
    background: var(--accent-color);
    border-radius: var(--radius-full);
}

/* 理由网格 */
.chapter5-slide6-reasons-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg);
}

/* 理由项目 */
.chapter5-slide6-reason-item {
    background: var(--background-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-light);
    transition: all var(--transition-base);
    display: flex;
    align-items: center;
    flex-direction: column;
    gap: var(--spacing-md);
    position: relative;
    overflow: hidden;
}

.chapter5-slide6-reason-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(180deg, var(--accent-color), #f7931e);
    transform: scaleY(0);
    transition: transform var(--transition-base);
}

.chapter5-slide6-reason-item:hover::before {
    transform: scaleY(1);
}

.chapter5-slide6-reason-item:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-md);
    border-color: rgba(255, 107, 53, 0.3);
}

/* 理由图标 */
.chapter5-slide6-reason-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--accent-color), #f7931e);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    flex-shrink: 0;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-base);
}

.chapter5-slide6-reason-item:hover .chapter5-slide6-reason-icon {
    transform: scale(1.1);
    box-shadow: var(--shadow-md);
}

/* 理由内容 */
.chapter5-slide6-reason-content {
    flex: 1;
}

.chapter5-slide6-reason-content h4 {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin: 0 0 var(--spacing-xs) 0;
}

.chapter5-slide6-reason-content p {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin: 0;
    line-height: var(--line-height-relaxed);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .chapter5-slide6-comparison-table {
        padding: var(--spacing-lg);
    }

    .chapter5-slide6-comparison-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-xs);
    }

    .chapter5-slide6-comparison-row {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr 1fr;
        gap: var(--spacing-xs);
        margin-bottom: var(--spacing-sm);
        background: var(--background-secondary);
        border-radius: var(--radius-sm);
        padding: var(--spacing-sm);
    }

    .chapter5-slide6-comparison-row.header {
        background: var(--primary-color);
        color: white;
        font-weight: var(--font-weight-bold);
    }

    .chapter5-slide6-comparison-row.header .chapter5-slide6-comparison-cell {
        background: transparent;
        color: white;
        padding: var(--spacing-sm);
        font-size: var(--font-size-xs);
    }

    .chapter5-slide6-comparison-cell {
        padding: var(--spacing-sm);
        font-size: var(--font-size-xs);
    }

    .chapter5-slide6-reasons-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .chapter5-slide6-reason-item {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-sm);
        padding: var(--spacing-md);
    }

    .chapter5-slide6-reason-icon {
        width: 45px;
        height: 45px;
        font-size: var(--font-size-base);
    }

    .chapter5-slide6-recommendation-badge {
        padding: var(--spacing-sm) var(--spacing-lg);
        font-size: var(--font-size-sm);
    }
}

@media (max-width: 480px) {
    .chapter5-slide6-comparison-table {
        padding: var(--spacing-md);
        margin: var(--spacing-lg) 0;
    }

    .chapter5-slide6-comparison-row {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-xs);
    }

    .chapter5-slide6-comparison-cell {
        padding: var(--spacing-xs);
        font-size: 11px;
    }

    .chapter5-slide6-table-header h3 {
        font-size: var(--font-size-lg);
    }

    .chapter5-slide6-reasons-header h3 {
        font-size: var(--font-size-base);
    }

    .chapter5-slide6-reason-icon {
        width: 40px;
        height: 40px;
        font-size: var(--font-size-sm);
    }

    .chapter5-slide6-reason-content h4 {
        font-size: var(--font-size-sm);
    }

    .chapter5-slide6-reason-content p {
        font-size: var(--font-size-xs);
    }
}

/* 动画效果 */
@keyframes sparkle {
    0%, 100% { 
        transform: scale(1) rotate(0deg); 
    }
    50% { 
        transform: scale(1.2) rotate(180deg); 
    }
}

@keyframes pulse {
    0%, 100% { 
        transform: scale(1); 
        box-shadow: 0 0 0 0 rgba(255, 107, 53, 0.4);
    }
    50% { 
        transform: scale(1.05); 
        box-shadow: 0 0 0 10px rgba(255, 107, 53, 0);
    }
}
