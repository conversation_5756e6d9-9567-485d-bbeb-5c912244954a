/* ===================================
   第4章增强样式 - Bejoan差异化策略
   ================================== */

/* === 第4章首页样式 === */

/* 背景装饰系统 - 第4章特定图片 */
.bg-image {
    background-image: url('https://images.unsplash.com/photo-1630699376443-a79cea41ed80?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D');
}




/* 第4章特定装饰元素 */
.decoration-element.circle {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    top: 10%;
    right: 10%;
}

.decoration-element.triangle {
    width: 0;
    height: 0;
    border-left: 40px solid transparent;
    border-right: 40px solid transparent;
    border-bottom: 70px solid var(--accent-color);
    bottom: 20%;
    left: 15%;
}

.decoration-element.square {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    transform: rotate(45deg);
    top: 60%;
    right: 20%;
}

/* === 产品策略色块样式 === */
.product-strategy-block {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 15px;
    padding: 25px;
    border: 2px solid #cbd5e1;
    margin: 20px 0;
    position: relative;
    overflow: hidden;
}

.product-strategy-block::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #3b82f6, #10b981, #f59e0b);
    border-radius: 15px 15px 0 0;
}

.product-strategy-block .strategy-insight {
    margin-bottom: 20px;
    padding: 20px;
    background: #ffffff;
    border-radius: 10px;
    border-left: 5px solid #3b82f6;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.product-strategy-block .insight-text {
    font-size: 16px;
    line-height: 1.7;
    color: #1e293b;
    margin: 0;
}

.product-line-strategy {
    display: flex;
    gap: 20px;
    margin-top: 20px;
}

.product-line-item {
    flex: 1;
    padding: 20px;
    border-radius: 12px;
    position: relative;
    transition: all 0.3s ease;
}

.product-line-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.product-line-item.toilet-seat {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    border: 2px solid #3b82f6;
}

.product-line-item.toilet {
    background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
    border: 2px solid #10b981;
}

.product-line-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 15px;
}

.product-line-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
}

.toilet-seat .product-line-icon {
    background: #3b82f6;
}

.toilet .product-line-icon {
    background: #10b981;
}

.product-line-title {
    font-size: 16px;
    font-weight: 700;
    color: #1e293b;
    margin: 0;
}

.product-line-content {
    font-size: 14px;
    line-height: 1.6;
    color: #374151;
    text-align: left;
}

.product-line-content strong {
    color: #1e293b;
    font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .product-line-strategy {
        flex-direction: column;
        gap: 15px;
    }
    
    .product-strategy-block {
        padding: 20px;
        margin: 15px 0;
    }
    
    .product-line-item {
        padding: 15px;
    }
}



.triangle-container {
    position: relative;
    width: 100%;
    height: 100%;
}

.triangle-point {
    position: absolute;
    width: 150px;
    padding: var(--spacing-lg);
    background: var(--background-primary);
    border: 2px solid var(--primary-color);
    border-radius: 12px;
    text-align: center;
    box-shadow: var(--shadow-light);
    transition: all var(--transition-base);
}

.triangle-point:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-medium);
}

.triangle-point.top {
    top: 0;
    left: 50%;
    transform: translateX(-50%);
}

.triangle-point.bottom-left {
    bottom: 0;
    left: 0;
}

.triangle-point.bottom-right {
    bottom: 0;
    right: 0;
}

.triangle-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    padding: var(--spacing-xl);
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    text-align: center;
    box-shadow: var(--shadow-heavy);
    z-index: 10;
}
