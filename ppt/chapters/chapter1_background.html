<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第一章：战略背景与核心结论</title>
    <link rel="stylesheet" href="../assets/css/design-system.css">
    <link rel="stylesheet" href="../assets/css/animations.css">
    <link rel="stylesheet" href="../assets/css/common.css">
    <link rel="stylesheet" href="../assets/css/slide.css">
    <link rel="stylesheet" href="../assets/css/charts.css">
    <link rel="stylesheet" href="../assets/css/chapter1-enhanced.css">

    <!-- 图表库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/d3@7.8.5/dist/d3.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 幻灯片1：封面页 -->
    <div class="slide" id="slide-1">
        <div class="slide-content center-content">
            <!-- 背景图片 -->
            <div class="hero-background">
                <div class="bg-image" style="background-image: url('https://t3.ftcdn.net/jpg/02/63/29/19/360_F_263291921_53I9YxJ1s3s2x3h2s5n1GfQzG4s3jB9w.jpg');"></div>
                <div class="bg-overlay"></div>
                <div class="bg-pattern animate-fadeIn animate-delay-100"></div>
                <div class="bg-gradient animate-fadeIn animate-delay-200"></div>
            </div>

            <!-- 主标题区域 -->
            <div class="content-block hero-title-section" style="margin-bottom: 40px;">
                <h1 class="slide-title enhanced-title animate-fadeInDown animate-delay-300">
                    <span class="title-primary">Bejoan北美三大渠道</span>
                    <span class="title-emphasis">进入战略</span>
                </h1>
            </div>

           


            <!-- 章节概要 -->
            <div class="content-block chapter-overview animate-slideUp animate-delay-1700">
                <h3 class="overview-title">本报告章节概要</h3>
                <div class="chapter-grid">
                    <div class="chapter-item">
                        <div class="chapter-number">01</div>
                        <div class="chapter-content">
                            <h4>战略背景</h4>
                            <p>市场规模分析、核心问题聚焦、验证框架</p>
                        </div>
                    </div>
                    <div class="chapter-item">
                        <div class="chapter-number">02</div>
                        <div class="chapter-content">
                            <h4>渠道战场</h4>
                            <p>三大渠道GMV对比、价格分析、守门员产品</p>
                        </div>
                    </div>
                    <div class="chapter-item">
                        <div class="chapter-number">03</div>
                        <div class="chapter-content">
                            <h4>核心玩家</h4>
                            <p>竞争对手分析、功能对比、营销策略研究</p>
                        </div>
                    </div>
                    <div class="chapter-item">
                        <div class="chapter-number">04</div>
                        <div class="chapter-content">
                            <h4>差异化策略</h4>
                            <p>产品定位、功能创新、营销差异化方案</p>
                        </div>
                    </div>
                    <div class="chapter-item">
                        <div class="chapter-number">05</div>
                        <div class="chapter-content">
                            <h4>战略选择</h4>
                            <p>进入策略、时机选择、资源配置建议</p>
                        </div>
                    </div>
                    <div class="chapter-item">
                        <div class="chapter-number">06</div>
                        <div class="chapter-content">
                            <h4>行动计划</h4>
                            <p>实施路径、关键里程碑、成功指标</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 装饰元素 -->
            <div class="hero-decorations">
                <div class="decoration-element circle animate-float animate-delay-2100"></div>
                <div class="decoration-element triangle animate-pulse animate-delay-2300"></div>
                <div class="decoration-element square animate-float animate-delay-2500"></div>
            </div>
        </div>
    </div>

    <!-- 幻灯片2：北美智能马桶市场规模分析 -->
    <div class="slide" id="slide-2" style="display: none;">
        <div class="slide-header">
            <h1 class="slide-title">北美智能马桶及马桶盖市场规模分析：$34.6亿机遇的深度解读</h1>
            <p class="slide-subtitle">基于三大渠道318款产品的全面数据分析，揭示北美智能马桶及马桶盖市场的真实规模与增长潜力</p>
        </div>
        <div class="slide-content">
            <!-- 市场规模概览 -->
            <div class="content-block">
                <div class="market-overview-insight animate-fadeInUp animate-delay-200">
                    <p class="insight-text">北美智能马桶及马桶盖市场正处于快速增长期，三大主流渠道总GMV达$34.6亿，年复合增长率超过20%，其中Home Depot占据主导地位，Lowe's增长最快，Menards潜力巨大。</p>
                </div>

                <!-- 市场规模仪表板 -->
                <div class="market-dashboard animate-slideUp animate-delay-400">
                    <div class="dashboard-grid">
                        <div class="dashboard-metric animate-scaleIn animate-delay-600 hover-lift">
                            <div class="metric-icon">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                            <div class="metric-value number-counter" data-target="34.6">1</div>
                            <div class="metric-label">总市场规模 (亿美元)</div>
                            <div class="metric-change positive">+22.3% CAGR</div>
                        </div>

                        <div class="dashboard-metric animate-scaleIn animate-delay-700 hover-lift">
                            <div class="metric-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="metric-value number-counter" data-target="318">0</div>
                            <div class="metric-label">在售产品总数</div>
                            <div class="metric-change positive">全面覆盖</div>
                        </div>

                        <div class="dashboard-metric animate-scaleIn animate-delay-800 hover-lift">
                            <div class="metric-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="metric-value number-counter" data-target="11600">0</div>
                            <div class="metric-label">用户评论总数</div>
                            <div class="metric-change positive">真实反馈</div>
                        </div>

                        <div class="dashboard-metric animate-scaleIn animate-delay-900 hover-lift">
                            <div class="metric-icon">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="metric-value number-counter" data-target="4.2">0</div>
                            <div class="metric-label">平均用户评分</div>
                            <div class="metric-change positive">高满意度</div>
                        </div>
                    </div>
                </div>

            </div>

            <div class="content-block" data-animate="fadeIn" data-delay="1400">
                <div class="data-source">
                    <p><strong>数据来源：</strong>基于Home Depot、Lowe's、Menards三大平台318款智能马桶产品的价格、销量、评论数据综合分析；GMV估算基于产品价格和评论数量的相关性模型</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 幻灯片3：会议聚焦与核心问题 -->
    <div class="slide" id="slide-3" style="display: none;">
        <div class="slide-header">
            <h1 class="slide-title">会议聚焦：三个决定Bejoan成败的核心商业问题</h1>
            <p class="slide-subtitle">本次会议聚焦于战略决策，而非泛泛的市场分析。我们将基于数据深度回答三个关键问题，为Bejoan制定精准的北美市场进入策略。</p>
        </div>
        <div class="slide-content">
            <div class="content-block" data-animate="slideUp">
                <div class="business-modules">
                    <div class="module-card" data-animate="cardFloat" data-delay="100">
                        <div class="module-icon">
                            <i class="fas fa-map"></i>
                        </div>
                        <div class="module-content">
                            <h3>模块一：三大渠道剖析</h3>
                            <p class="module-subtitle">基于数据推算各渠道的GMV和增长潜力</p>
                            <ul class="module-questions">
                                <li>三大渠道的市场容量与增长潜力分别是多少？</li>
                                <li>每个渠道内部是如何被细分的？</li>
                                <li>线上机会与全渠道潜力相比，是否存在被高估或低估的可能？</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="module-card" data-animate="cardFloat" data-delay="200">
                        <div class="module-icon">
                            <i class="fas fa-chess"></i>
                        </div>
                        <div class="module-content">
                            <h3>模块二：核心玩家地图</h3>
                            <p class="module-subtitle">了解每个渠道的核心合作品牌和竞争格局</p>
                            <ul class="module-questions">
                                <li>每个渠道的关键玩家，核心合作品牌和竞争格局</li>
                                <li>关键玩家在不同渠道的差异化策略分析</li>
                                <li>每个渠道的细分市场的价格守门员是谁？</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="module-card" data-animate="cardFloat" data-delay="300">
                        <div class="module-icon">
                            <i class="fas fa-bullseye"></i>
                        </div>
                        <div class="module-content">
                            <h3>模块三：Bejoan差异化策略</h3>
                            <p class="module-subtitle">针对不同渠道特性制定产品、定价、营销策略</p>
                            <ul class="module-questions">
                                <li>Bejoan的制胜产品组合是什么？</li>
                                <li>我们推荐的战略选择其背后的考量是什么？</li>
                                <li>我们如何管理风险，下一步最优先的行动是什么？</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <!-- 幻灯片4：多源验证框架 -->
    <div class="slide" id="slide-4" style="display: none;">
        <div class="slide-header">
            <h1 class="slide-title">我们的结论基于"多源验证"框架，确保了决策的最高可靠性</h1>
            <p class="slide-subtitle">在进入一个新市场时，最大的风险是基于不完整信息做决策。我们的核心价值是提供经过高度验证的、可信的结论。</p>
        </div>
        <div class="slide-content">
            <div class="content-block" data-animate="slideUp">
                <div class="verification-funnel">
                    <div class="funnel-level" data-animate="slideLeft" data-delay="100">
                        <div class="level-number">1</div>
                        <div class="level-content">
                            <h3>一级数据源 (核心实证数据)</h3>
                            <div class="data-items">
                                <div class="data-item">
                                    <div class="data-item-title">
                                        <i class="fas fa-shopping-cart"></i>
                                        <span><strong>竞品数据：</strong></span>
                                    </div>
                                    <span>对Home Depot, Lowe's, Menards三大渠道共318款竞品进行全面采集</span>
                                </div>
                                <div class="data-item">
                                    <div class="data-item-title">
                                        <i class="fas fa-cog"></i>
                                        <span><strong>自身数据：</strong></span>
                                    </div>
                                    <span>对Bejoan产品线进行深度分析</span>
                                </div>
                                <div class="data-item">
                                    <div class="data-item-title">
                                        <i class="fas fa-comments"></i>
                                        <span><strong>用户数据：</strong></span>
                                    </div>
                                    <span>对超过1.16万条真实用户评论进行深度文本挖掘</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="funnel-level" data-animate="slideLeft" data-delay="200">
                        <div class="level-number">2</div>
                        <div class="level-content">
                            <h3>二级数据源 (宏观趋势验证)</h3>
                            <div class="data-items">
                                <div class="data-item">
                                    <div class="data-item-title">
                                        <i class="fas fa-file-alt"></i>
                                        <span><strong>行业报告：</strong></span>
                                    </div>
                                    <span>精选来自Arizton、Grand View、Mordor Intelligence等机构的7份核心市场研究报告</span>
                                </div>
                                <div class="data-item">
                                    <div class="data-item-title">
                                        <i class="fas fa-building"></i>
                                        <span><strong>官方数据：</strong></span>
                                    </div>
                                    <span>Home Depot及Lowe's的年报、投资者电话会议纪要等</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="funnel-level" data-animate="slideLeft" data-delay="300">
                        <div class="level-number">3</div>
                        <div class="level-content">
                            <h3>三级数据源 (风险校准与补充)</h3>
                            <div class="data-items">
                                <div class="data-item">
                                    <div class="data-item-title">
                                        <i class="fas fa-search"></i>
                                        <span><strong>补充调研：</strong></span>
                                    </div>
                                    <span>产出一份高优先级补充调研计划，以弥补数据盲点</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="funnel-level" data-animate="slideLeft" data-delay="400">
                        <div class="level-number">4</div>
                        <div class="level-content">
                            <h3>多源验证机制</h3>
                            <div class="verification-steps">
                                <div class="step">趋势一致性对比</div>
                                <div class="step">数值范围校验</div>
                                <div class="step">逻辑相符验证</div>
                                <div class="step">差异分析解释</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="content-block" data-animate="fadeIn" data-delay="800">
                <div class="data-source">
                    <p><strong>数据来源：</strong>基于Home Depot、Lowe's、Menards三大平台2024年6月-2025年1月产品数据采集；结合Arizton、Mordor Intelligence、Custom Market Insights等7家权威机构2024年市场研究报告</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 幻灯片5：核心数据资产 -->
    <div class="slide" id="slide-5" style="display: none;">
        <div class="slide-header">
            <h1 class="slide-title">本次分析产出了七大核心数据资产，构成了我们战略的基石</h1>
            <p class="slide-subtitle">我们的建议不是凭空而来，而是建立在扎实、可追溯的数据工作之上。</p>
        </div>
        <div class="slide-content">
            <div class="content-block" data-animate="slideUp">
                <div class="data-assets-grid">
                    <div class="asset-card" data-animate="cardFloat" data-delay="100">
                        <div class="asset-icon">
                            <i class="fas fa-table"></i>
                        </div>
                        <div class="asset-content">
                            <h3>竞品数据库</h3>
                            <p class="asset-format">Excel</p>
                            <p class="asset-description">包含318款竞品详细信息</p>
                        </div>
                    </div>

                    <div class="asset-card" data-animate="cardFloat" data-delay="150">
                        <div class="asset-icon">
                            <i class="fas fa-comments"></i>
                        </div>
                        <div class="asset-content">
                            <h3>用户评论分析库</h3>
                            <p class="asset-format">Excel</p>
                            <p class="asset-description">1.16万+用户评论的标签化分析数据</p>
                        </div>
                    </div>

                    <div class="asset-card" data-animate="cardFloat" data-delay="200">
                        <div class="asset-icon">
                            <i class="fas fa-sitemap"></i>
                        </div>
                        <div class="asset-content">
                            <h3>渠道策略矩阵</h3>
                            <p class="asset-format">Excel</p>
                            <p class="asset-description">针对不同渠道的详细策略规划</p>
                        </div>
                    </div>

                    <div class="asset-card" data-animate="cardFloat" data-delay="250">
                        <div class="asset-icon">
                            <i class="fas fa-file-pdf"></i>
                        </div>
                        <div class="asset-content">
                            <h3>市场研究报告综述</h3>
                            <p class="asset-format">PDF</p>
                            <p class="asset-description">7份核心行业报告的关键洞察摘要</p>
                        </div>
                    </div>

                    <div class="asset-card" data-animate="cardFloat" data-delay="300">
                        <div class="asset-icon">
                            <i class="fas fa-map-marked-alt"></i>
                        </div>
                        <div class="asset-content">
                            <h3>竞争定位地图</h3>
                            <p class="asset-format">PDF</p>
                            <p class="asset-description">各品牌在不同维度的竞争态势图</p>
                        </div>
                    </div>

                    <div class="asset-card" data-animate="cardFloat" data-delay="350">
                        <div class="asset-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <div class="asset-content">
                            <h3>渠道深度分析报告</h3>
                            <p class="asset-format">PDF</p>
                            <p class="asset-description">Home Depot, Lowe's, Menards专项分析报告</p>
                        </div>
                    </div>

                    <div class="asset-card" data-animate="cardFloat" data-delay="400">
                        <div class="asset-icon">
                            <i class="fas fa-tasks"></i>
                        </div>
                        <div class="asset-content">
                            <h3>高优先级补充调研计划</h3>
                            <p class="asset-format">PDF</p>
                            <p class="asset-description">明确下一步需要验证的关键问题</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="content-block" data-animate="fadeIn" data-delay="600">
                <div class="asset-value">
                    <p class="value-statement">这些数据资产不仅支撑了本次报告，更可以成为Bejoan未来在北美市场进行精细化运营的宝贵财富。</p>
                </div>
            </div>
            <div class="content-block" data-animate="fadeIn" data-delay="800">
                <div class="data-source">
                    <p><strong>数据来源：</strong>基于2024年6月-2025年1月期间对三大渠道的全面数据采集与分析</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 幻灯片6：核心建议 -->
    <div class="slide" id="slide-6" style="display: none;">
        <div class="slide-header">
            <h1 class="slide-title">核心建议：采取"均衡增长"策略，12个月投资$2.5M，冲击1.5%市场份额</h1>
            <p class="slide-subtitle">综合所有分析，我们建议采取风险与收益最平衡的"均衡增长"路径。这是我们认为最适合Bejoan当前阶段的战略选择。</p>
        </div>
        <div class="slide-content">
            <div class="content-block" data-animate="slideUp">
                <div class="strategy-dashboard">
                    <div class="dashboard-item" data-animate="scaleIn" data-delay="100">
                        <div class="dashboard-icon">
                            <i class="fas fa-store"></i>
                        </div>
                        <div class="dashboard-content">
                            <h3>渠道优先级</h3>
                            <div class="channel-allocation">
                                <div class="channel-item">
                                    <span class="channel-name">Home Depot</span>
                                    <div class="channel-bar">
                                        <div class="channel-fill" style="width: 60%; background: var(--primary-color);"></div>
                                    </div>
                                    <span class="channel-percent">60%</span>
                                </div>
                                <div class="channel-item">
                                    <span class="channel-name">Lowe's</span>
                                    <div class="channel-bar">
                                        <div class="channel-fill" style="width: 30%; background: var(--secondary-color);"></div>
                                    </div>
                                    <span class="channel-percent">30%</span>
                                </div>
                                <div class="channel-item">
                                    <span class="channel-name">Menards</span>
                                    <div class="channel-bar">
                                        <div class="channel-fill" style="width: 10%; background: var(--accent-color);"></div>
                                    </div>
                                    <span class="channel-percent">10%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-item" data-animate="scaleIn" data-delay="200">
                        <div class="dashboard-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <div class="dashboard-content">
                            <h3>产品核心策略</h3>
                            <div class="strategy-points">
                                <div class="strategy-point urgent">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <span>立即补齐"除臭功能"</span>
                                </div>
                                <div class="strategy-point advantage">
                                    <i class="fas fa-star"></i>
                                    <span>强力主打"按摩功能"差异化</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-item" data-animate="scaleIn" data-delay="300">
                        <div class="dashboard-icon">
                            <i class="fas fa-bullseye"></i>
                        </div>
                        <div class="dashboard-content">
                            <h3>关键机会</h3>
                            <p class="opportunity-text">抢占Home Depot中端价格带</p>
                            <div class="price-range">$699 - $899</div>
                        </div>
                    </div>

                    <div class="dashboard-item" data-animate="scaleIn" data-delay="400">
                        <div class="dashboard-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="dashboard-content">
                            <h3>投资回报</h3>
                            <div class="roi-metrics">
                                <div class="metric">
                                    <span class="metric-label">投入</span>
                                    <span class="metric-value highlight-pulse">$2.5M</span>
                                </div>
                                <div class="metric">
                                    <span class="metric-label">预期ROI</span>
                                    <span class="metric-value number-counter">22%</span>
                                </div>
                                <div class="metric">
                                    <span class="metric-label">时间</span>
                                    <span class="metric-value">12个月</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="content-block" data-animate="fadeIn" data-delay="600">
                <div class="strategy-goal">
                    <p class="goal-statement">这个战略旨在1年内将Bejoan打造为北美市场一个不容忽视的重要玩家，并建立可持续的盈利能力。</p>
                </div>
            </div>
            <div class="content-block" data-animate="fadeIn" data-delay="800">
                <div class="data-source">
                    <p><strong>数据来源：</strong>基于三大渠道竞品分析及市场规模测算模型；参考Arizton 2024年北美智能马桶市场预测报告</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 幻灯片导航逻辑
        let currentSlide = 1;
        const totalSlides = 6;
        
        function showSlide(slideNumber) {
            // 隐藏所有幻灯片
            document.querySelectorAll('.slide').forEach(slide => {
                slide.style.display = 'none';
            });
            
            // 显示指定幻灯片
            const targetSlide = document.getElementById(`slide-${slideNumber}`);
            if (targetSlide) {
                targetSlide.style.display = 'block';
                _triggerAnimations(targetSlide);
            }
            
            currentSlide = slideNumber;
        }
        
        function _triggerAnimations(slide) {
            const animatedElements = slide.querySelectorAll('[data-animate]');
            animatedElements.forEach((element, index) => {
                const delay = parseInt(element.dataset.delay) || 0;
                setTimeout(() => {
                    element.classList.add('animated');
                }, delay);
            });
        }
        
        // 监听URL hash变化
        function handleHashChange() {
            const hash = window.location.hash;
            const match = hash.match(/#slide-(\d+)/);
            if (match) {
                const slideNumber = parseInt(match[1]);
                if (slideNumber >= 1 && slideNumber <= totalSlides) {
                    showSlide(slideNumber);
                }
            }
        }
        
        // 初始化
        window.addEventListener('load', () => {
            handleHashChange();
            if (!window.location.hash) {
                showSlide(1);
            }
        });
        
        window.addEventListener('hashchange', handleHashChange);
        
        // 导出函数供父页面调用
        window.triggerAnimations = () => {
            const currentSlideElement = document.getElementById(`slide-${currentSlide}`);
            if (currentSlideElement) {
                _triggerAnimations(currentSlideElement);
            }
        };

        // 初始化图表
        document.addEventListener('DOMContentLoaded', function() {
            // 市场规模图表数据
            const marketData = {
                segments: {
                    labels: ['智能马桶', '智能马桶盖', '配件服务'],
                    values: [2.35, 1.11, 0.0]
                },
                growth: {
                    labels: ['2024', '2025', '2026', '2027', '2028', '2029', '2030'],
                    values: [3.46, 4.03, 4.70, 5.48, 6.39, 7.45, 8.92]
                },
                regions: {
                    labels: ['美国', '加拿大', '墨西哥'],
                    values: [2.87, 0.42, 0.17]
                }
            };

            // 图表控制按钮事件
            document.querySelectorAll('.chart-control-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    // 移除所有active类
                    document.querySelectorAll('.chart-control-btn').forEach(b => b.classList.remove('active'));
                    // 添加active类到当前按钮
                    this.classList.add('active');

                    const view = this.dataset.view;
                    updateMarketChart(view);
                });
            });

            // 初始化图表
            setTimeout(() => updateMarketChart('segments'), 1000);

            function updateMarketChart(view) {
                const chartContainer = document.getElementById('marketChart');
                if (!chartContainer) return;

                // 清空容器
                chartContainer.innerHTML = '';

                let data, chartType;
                switch(view) {
                    case 'segments':
                        data = marketData.segments;
                        chartType = 'doughnut';
                        break;
                    case 'growth':
                        data = marketData.growth;
                        chartType = 'line';
                        break;
                    case 'regions':
                        data = marketData.regions;
                        chartType = 'bar';
                        break;
                }

                // 创建canvas
                const canvas = document.createElement('canvas');
                canvas.style.maxHeight = '300px';
                chartContainer.appendChild(canvas);

                const ctx = canvas.getContext('2d');

                // 图表配置
                const config = {
                    type: chartType,
                    data: {
                        labels: data.labels,
                        datasets: [{
                            data: data.values,
                            backgroundColor: chartType === 'line' ? 'rgba(0, 115, 230, 0.1)' : [
                                '#0073e6', '#00a651', '#ff6b35', '#ffc107', '#17a2b8'
                            ],
                            borderColor: chartType === 'line' ? '#0073e6' : [
                                '#0073e6', '#00a651', '#ff6b35', '#ffc107', '#17a2b8'
                            ],
                            borderWidth: chartType === 'line' ? 3 : 2,
                            fill: chartType === 'line',
                            tension: chartType === 'line' ? 0.4 : undefined
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: chartType === 'doughnut' ? 'right' : 'top',
                                labels: {
                                    usePointStyle: true,
                                    padding: 20
                                }
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        if (view === 'segments' || view === 'regions') {
                                            return context.label + ': $' + context.parsed + 'B';
                                        } else {
                                            return '市场规模: $' + context.parsed + 'B';
                                        }
                                    }
                                }
                            }
                        },
                        scales: chartType !== 'doughnut' ? {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    callback: function(value) {
                                        return '$' + value + 'B';
                                    }
                                }
                            }
                        } : {},
                        animation: {
                            duration: 1500,
                            easing: 'easeOutQuart'
                        }
                    }
                };

                new Chart(ctx, config);
            }
        });

        // 数字滚动动画
        function animateNumber(element, target, duration = 2000) {
            const start = 0;
            const startTime = performance.now();

            function updateNumber(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);

                // 使用缓动函数
                const easeOutQuart = 1 - Math.pow(1 - progress, 4);
                const current = Math.floor(start + (target - start) * easeOutQuart);

                element.textContent = current;

                if (progress < 1) {
                    requestAnimationFrame(updateNumber);
                } else {
                    element.textContent = target;
                }
            }

            requestAnimationFrame(updateNumber);
        }

        // 初始化数字滚动动画
        function initNumberCounters() {
            const counters = document.querySelectorAll('.number-counter');
            counters.forEach(counter => {
                const target = parseInt(counter.getAttribute('data-target'));
                if (target) {
                    // 延迟启动动画
                    setTimeout(() => {
                        animateNumber(counter, target);
                    }, 1000);
                }
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initNumberCounters();
        });
    </script>

    <script src="../assets/js/charts.js"></script>
    <script src="../assets/js/navigation.js"></script>
</body>
</html>
